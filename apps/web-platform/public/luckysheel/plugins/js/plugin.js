!function(t,e){"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(t.document)return e(t);throw new Error("jQuery requires a window with a document")}:e(t)}("undefined"!=typeof window?window:this,function(_,M){function W(t,e){return e.toUpperCase()}var t=[],x=_.document,l=t.slice,L=t.concat,F=t.push,i=t.indexOf,H={},B=H.toString,d=H.hasOwnProperty,g={},e="2.2.4",C=function(t,e){return new C.fn.init(t,e)},q=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,z=/^-ms-/,U=/-([\da-z])/gi;function G(t){var e=!!t&&"length"in t&&t.length,n=C.type(t);return"function"!==n&&!C.isWindow(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}C.fn=C.prototype={jquery:e,constructor:C,selector:"",length:0,toArray:function(){return l.call(this)},get:function(t){return null!=t?t<0?this[t+this.length]:this[t]:l.call(this)},pushStack:function(t){t=C.merge(this.constructor(),t);return t.prevObject=this,t.context=this.context,t},each:function(t){return C.each(this,t)},map:function(n){return this.pushStack(C.map(this,function(t,e){return n.call(t,e,t)}))},slice:function(){return this.pushStack(l.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(t){var e=this.length,t=+t+(t<0?e:0);return this.pushStack(0<=t&&t<e?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:F,sort:t.sort,splice:t.splice},C.extend=C.fn.extend=function(){var t,e,n,r,i,o=arguments[0]||{},s=1,a=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[s]||{},s++),"object"==typeof o||C.isFunction(o)||(o={}),s===a&&(o=this,s--);s<a;s++)if(null!=(t=arguments[s]))for(e in t)i=o[e],n=t[e],o!==n&&(u&&n&&(C.isPlainObject(n)||(r=C.isArray(n)))?(i=r?(r=!1,i&&C.isArray(i)?i:[]):i&&C.isPlainObject(i)?i:{},o[e]=C.extend(u,i,n)):void 0!==n&&(o[e]=n));return o},C.extend({expando:"jQuery"+(e+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isFunction:function(t){return"function"===C.type(t)},isArray:Array.isArray,isWindow:function(t){return null!=t&&t===t.window},isNumeric:function(t){var e=t&&t.toString();return!C.isArray(t)&&0<=e-parseFloat(e)+1},isPlainObject:function(t){if("object"!==C.type(t)||t.nodeType||C.isWindow(t))return!1;if(t.constructor&&!d.call(t,"constructor")&&!d.call(t.constructor.prototype||{},"isPrototypeOf"))return!1;for(var e in t);return void 0===e||d.call(t,e)},isEmptyObject:function(t){for(var e in t)return!1;return!0},type:function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?H[B.call(t)]||"object":typeof t},globalEval:function(t){var e,n=eval;(t=C.trim(t))&&(1===t.indexOf("use strict")?((e=x.createElement("script")).text=t,x.head.appendChild(e).parentNode.removeChild(e)):n(t))},camelCase:function(t){return t.replace(z,"ms-").replace(U,W)},nodeName:function(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()},each:function(t,e){var n,r=0;if(G(t))for(n=t.length;r<n&&!1!==e.call(t[r],r,t[r]);r++);else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},trim:function(t){return null==t?"":(t+"").replace(q,"")},makeArray:function(t,e){e=e||[];return null!=t&&(G(Object(t))?C.merge(e,"string"==typeof t?[t]:t):F.call(e,t)),e},inArray:function(t,e,n){return null==e?-1:i.call(e,t,n)},merge:function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;r++)t[i++]=e[r];return t.length=i,t},grep:function(t,e,n){for(var r=[],i=0,o=t.length,s=!n;i<o;i++)!e(t[i],i)!=s&&r.push(t[i]);return r},map:function(t,e,n){var r,i,o=0,s=[];if(G(t))for(r=t.length;o<r;o++)null!=(i=e(t[o],o,n))&&s.push(i);else for(o in t)i=e(t[o],o,n),null!=i&&s.push(i);return L.apply([],s)},guid:1,proxy:function(t,e){var n,r;return"string"==typeof e&&(r=t[e],e=t,t=r),C.isFunction(t)?(n=l.call(arguments,2),(r=function(){return t.apply(e||this,n.concat(l.call(arguments)))}).guid=t.guid=t.guid||C.guid++,r):void 0},now:Date.now,support:g}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=t[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){H["[object "+e+"]"]=e.toLowerCase()});function r(t,e,n){for(var r=[],i=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(i&&C(t).is(n))break;r.push(t)}return r}function V(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n}var e=function(M){function h(t,e,n){var r="0x"+e-65536;return r!=r||n?e:r<0?String.fromCharCode(65536+r):String.fromCharCode(r>>10|55296,1023&r|56320)}function W(){x()}var t,d,w,o,L,g,F,H,_,u,c,x,C,e,k,m,r,i,v,S="sizzle"+ +new Date,y=M.document,O=0,B=0,q=ct(),z=ct(),b=ct(),U=function(t,e){return t===e&&(c=!0),0},G={}.hasOwnProperty,n=[],V=n.pop,$=n.push,E=n.push,X=n.slice,T=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},Y="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",s="[\\x20\\t\\r\\n\\f]",a="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",K="\\["+s+"*("+a+")(?:"+s+"*([*^$|!~]?=)"+s+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+a+"))|)"+s+"*\\]",Q=":("+a+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+K+")*)|.*)\\)|)",J=new RegExp(s+"+","g"),A=new RegExp("^"+s+"+|((?:^|[^\\\\])(?:\\\\.)*)"+s+"+$","g"),Z=new RegExp("^"+s+"*,"+s+"*"),tt=new RegExp("^"+s+"*([>+~]|"+s+")"+s+"*"),et=new RegExp("="+s+"*([^\\]'\"]*?)"+s+"*\\]","g"),nt=new RegExp(Q),rt=new RegExp("^"+a+"$"),f={ID:new RegExp("^#("+a+")"),CLASS:new RegExp("^\\.("+a+")"),TAG:new RegExp("^("+a+"|[*])"),ATTR:new RegExp("^"+K),PSEUDO:new RegExp("^"+Q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+s+"*(even|odd|(([+-]|)(\\d*)n|)"+s+"*(?:([+-]|)"+s+"*(\\d+)|))"+s+"*\\)|)","i"),bool:new RegExp("^(?:"+Y+")$","i"),needsContext:new RegExp("^"+s+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+s+"*((?:-\\d)?\\d*)"+s+"*\\)|)(?=[^-]|$)","i")},it=/^(?:input|select|textarea|button)$/i,ot=/^h\d$/i,l=/^[^{]+\{\s*\[native \w/,st=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,at=/[+~]/,ut=/'|\\/g,p=new RegExp("\\\\([\\da-f]{1,6}"+s+"?|("+s+")|.)","ig");try{E.apply(n=X.call(y.childNodes),y.childNodes),n[y.childNodes.length].nodeType}catch(t){E={apply:n.length?function(t,e){$.apply(t,X.call(e))}:function(t,e){for(var n=t.length,r=0;t[n++]=e[r++];);t.length=n-1}}}function I(t,e,n,r){var i,o,s,a,u,c,l,h,f=e&&e.ownerDocument,p=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==p&&9!==p&&11!==p)return n;if(!r&&((e?e.ownerDocument||e:y)!==C&&x(e),e=e||C,k)){if(11!==p&&(c=st.exec(t)))if(i=c[1]){if(9===p){if(!(s=e.getElementById(i)))return n;if(s.id===i)return n.push(s),n}else if(f&&(s=f.getElementById(i))&&v(e,s)&&s.id===i)return n.push(s),n}else{if(c[2])return E.apply(n,e.getElementsByTagName(t)),n;if((i=c[3])&&d.getElementsByClassName&&e.getElementsByClassName)return E.apply(n,e.getElementsByClassName(i)),n}if(d.qsa&&!b[t+" "]&&(!m||!m.test(t))){if(1!==p)f=e,h=t;else if("object"!==e.nodeName.toLowerCase()){for((a=e.getAttribute("id"))?a=a.replace(ut,"\\$&"):e.setAttribute("id",a=S),o=(l=g(t)).length,u=rt.test(a)?"#"+a:"[id='"+a+"']";o--;)l[o]=u+" "+D(l[o]);h=l.join(","),f=at.test(t)&&ft(e.parentNode)||e}if(h)try{return E.apply(n,f.querySelectorAll(h)),n}catch(t){}finally{a===S&&e.removeAttribute("id")}}}return H(t.replace(A,"$1"),e,n,r)}function ct(){var n=[];function r(t,e){return n.push(t+" ")>w.cacheLength&&delete r[n.shift()],r[t+" "]=e}return r}function j(t){return t[S]=!0,t}function P(t){var e=C.createElement("div");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e)}}function lt(t,e){for(var n=t.split("|"),r=n.length;r--;)w.attrHandle[n[r]]=e}function ht(t,e){var n=e&&t,r=n&&1===t.nodeType&&1===e.nodeType&&(~e.sourceIndex||1<<31)-(~t.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function N(s){return j(function(o){return o=+o,j(function(t,e){for(var n,r=s([],t.length,o),i=r.length;i--;)t[n=r[i]]&&(t[n]=!(e[n]=t[n]))})})}function ft(t){return t&&void 0!==t.getElementsByTagName&&t}for(t in d=I.support={},L=I.isXML=function(t){t=t&&(t.ownerDocument||t).documentElement;return!!t&&"HTML"!==t.nodeName},x=I.setDocument=function(t){var t=t?t.ownerDocument||t:y;return t!==C&&9===t.nodeType&&t.documentElement&&(e=(C=t).documentElement,k=!L(C),(t=C.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",W,!1):t.attachEvent&&t.attachEvent("onunload",W)),d.attributes=P(function(t){return t.className="i",!t.getAttribute("className")}),d.getElementsByTagName=P(function(t){return t.appendChild(C.createComment("")),!t.getElementsByTagName("*").length}),d.getElementsByClassName=l.test(C.getElementsByClassName),d.getById=P(function(t){return e.appendChild(t).id=S,!C.getElementsByName||!C.getElementsByName(S).length}),d.getById?(w.find.ID=function(t,e){if(void 0!==e.getElementById&&k)return(e=e.getElementById(t))?[e]:[]},w.filter.ID=function(t){var e=t.replace(p,h);return function(t){return t.getAttribute("id")===e}}):(delete w.find.ID,w.filter.ID=function(t){var e=t.replace(p,h);return function(t){t=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return t&&t.value===e}}),w.find.TAG=d.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):d.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,r=[],i=0,o=e.getElementsByTagName(t);if("*"!==t)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},w.find.CLASS=d.getElementsByClassName&&function(t,e){return void 0!==e.getElementsByClassName&&k?e.getElementsByClassName(t):void 0},r=[],m=[],(d.qsa=l.test(C.querySelectorAll))&&(P(function(t){e.appendChild(t).innerHTML="<a id='"+S+"'></a><select id='"+S+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+s+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||m.push("\\["+s+"*(?:value|"+Y+")"),t.querySelectorAll("[id~="+S+"-]").length||m.push("~="),t.querySelectorAll(":checked").length||m.push(":checked"),t.querySelectorAll("a#"+S+"+*").length||m.push(".#.+[+~]")}),P(function(t){var e=C.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&m.push("name"+s+"*[*^$|!~]?="),t.querySelectorAll(":enabled").length||m.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),m.push(",.*:")})),(d.matchesSelector=l.test(i=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.msMatchesSelector))&&P(function(t){d.disconnectedMatch=i.call(t,"div"),i.call(t,"[s!='']:x"),r.push("!=",Q)}),m=m.length&&new RegExp(m.join("|")),r=r.length&&new RegExp(r.join("|")),t=l.test(e.compareDocumentPosition),v=t||l.test(e.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,e=e&&e.parentNode;return t===e||!(!e||1!==e.nodeType||!(n.contains?n.contains(e):t.compareDocumentPosition&&16&t.compareDocumentPosition(e)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},U=t?function(t,e){var n;return t===e?(c=!0,0):(n=!t.compareDocumentPosition-!e.compareDocumentPosition)||(1&(n=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!d.sortDetached&&e.compareDocumentPosition(t)===n?t===C||t.ownerDocument===y&&v(y,t)?-1:e===C||e.ownerDocument===y&&v(y,e)?1:u?T(u,t)-T(u,e):0:4&n?-1:1)}:function(t,e){if(t===e)return c=!0,0;var n,r=0,i=t.parentNode,o=e.parentNode,s=[t],a=[e];if(!i||!o)return t===C?-1:e===C?1:i?-1:o?1:u?T(u,t)-T(u,e):0;if(i===o)return ht(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?ht(s[r],a[r]):s[r]===y?-1:a[r]===y?1:0}),C},I.matches=function(t,e){return I(t,null,null,e)},I.matchesSelector=function(t,e){if((t.ownerDocument||t)!==C&&x(t),e=e.replace(et,"='$1']"),d.matchesSelector&&k&&!b[e+" "]&&(!r||!r.test(e))&&(!m||!m.test(e)))try{var n=i.call(t,e);if(n||d.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){}return 0<I(e,C,null,[t]).length},I.contains=function(t,e){return(t.ownerDocument||t)!==C&&x(t),v(t,e)},I.attr=function(t,e){(t.ownerDocument||t)!==C&&x(t);var n=w.attrHandle[e.toLowerCase()],n=n&&G.call(w.attrHandle,e.toLowerCase())?n(t,e,!k):void 0;return void 0!==n?n:d.attributes||!k?t.getAttribute(e):(n=t.getAttributeNode(e))&&n.specified?n.value:null},I.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},I.uniqueSort=function(t){var e,n=[],r=0,i=0;if(c=!d.detectDuplicates,u=!d.sortStable&&t.slice(0),t.sort(U),c){for(;e=t[i++];)e===t[i]&&(r=n.push(i));for(;r--;)t.splice(n[r],1)}return u=null,t},o=I.getText=function(t){var e,n="",r=0,i=t.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=o(t)}else if(3===i||4===i)return t.nodeValue}else for(;e=t[r++];)n+=o(e);return n},(w=I.selectors={cacheLength:50,createPseudo:j,match:f,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(p,h),t[3]=(t[3]||t[4]||t[5]||"").replace(p,h),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||I.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&I.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return f.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&nt.test(n)&&(e=g(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(p,h).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=q[t+" "];return e||(e=new RegExp("(^|"+s+")"+t+"("+s+"|$)"))&&q(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(e,n,r){return function(t){t=I.attr(t,e);return null==t?"!="===n:!n||(t+="","="===n?t===r:"!="===n?t!==r:"^="===n?r&&0===t.indexOf(r):"*="===n?r&&-1<t.indexOf(r):"$="===n?r&&t.slice(-r.length)===r:"~="===n?-1<(" "+t.replace(J," ")+" ").indexOf(r):"|="===n&&(t===r||t.slice(0,r.length+1)===r+"-"))}},CHILD:function(d,t,e,g,m){var v="nth"!==d.slice(0,3),y="last"!==d.slice(-4),b="of-type"===t;return 1===g&&0===m?function(t){return!!t.parentNode}:function(t,e,n){var r,i,o,s,a,u,c=v!=y?"nextSibling":"previousSibling",l=t.parentNode,h=b&&t.nodeName.toLowerCase(),f=!n&&!b,p=!1;if(l){if(v){for(;c;){for(s=t;s=s[c];)if(b?s.nodeName.toLowerCase()===h:1===s.nodeType)return!1;u=c="only"===d&&!u&&"nextSibling"}return!0}if(u=[y?l.firstChild:l.lastChild],y&&f){for(p=(a=(r=(i=(o=(s=l)[S]||(s[S]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]||[])[0]===O&&r[1])&&r[2],s=a&&l.childNodes[a];s=++a&&s&&s[c]||(p=a=0)||u.pop();)if(1===s.nodeType&&++p&&s===t){i[d]=[O,a,p];break}}else if(!1===(p=f?a=(r=(i=(o=(s=t)[S]||(s[S]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]||[])[0]===O&&r[1]:p))for(;(s=++a&&s&&s[c]||(p=a=0)||u.pop())&&((b?s.nodeName.toLowerCase()!==h:1!==s.nodeType)||!++p||(f&&((i=(o=s[S]||(s[S]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]=[O,p]),s!==t)););return(p-=m)===g||p%g==0&&0<=p/g}}},PSEUDO:function(t,o){var e,s=w.pseudos[t]||w.setFilters[t.toLowerCase()]||I.error("unsupported pseudo: "+t);return s[S]?s(o):1<s.length?(e=[t,t,"",o],w.setFilters.hasOwnProperty(t.toLowerCase())?j(function(t,e){for(var n,r=s(t,o),i=r.length;i--;)t[n=T(t,r[i])]=!(e[n]=r[i])}):function(t){return s(t,0,e)}):s}},pseudos:{not:j(function(t){var r=[],i=[],a=F(t.replace(A,"$1"));return a[S]?j(function(t,e,n,r){for(var i,o=a(t,null,r,[]),s=t.length;s--;)(i=o[s])&&(t[s]=!(e[s]=i))}):function(t,e,n){return r[0]=t,a(r,null,n,i),r[0]=null,!i.pop()}}),has:j(function(e){return function(t){return 0<I(e,t).length}}),contains:j(function(e){return e=e.replace(p,h),function(t){return-1<(t.textContent||t.innerText||o(t)).indexOf(e)}}),lang:j(function(n){return rt.test(n||"")||I.error("unsupported lang: "+n),n=n.replace(p,h).toLowerCase(),function(t){var e;do{if(e=k?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(e=e.toLowerCase())===n||0===e.indexOf(n+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var e=M.location&&M.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===e},focus:function(t){return t===C.activeElement&&(!C.hasFocus||C.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:function(t){return!1===t.disabled},disabled:function(t){return!0===t.disabled},checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!w.pseudos.empty(t)},header:function(t){return ot.test(t.nodeName)},input:function(t){return it.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(t=t.getAttribute("type"))||"text"===t.toLowerCase())},first:N(function(){return[0]}),last:N(function(t,e){return[e-1]}),eq:N(function(t,e,n){return[n<0?n+e:n]}),even:N(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:N(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:N(function(t,e,n){for(var r=n<0?n+e:n;0<=--r;)t.push(r);return t}),gt:N(function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[t]=function(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}(t);for(t in{submit:!0,reset:!0})w.pseudos[t]=function(n){return function(t){var e=t.nodeName.toLowerCase();return("input"===e||"button"===e)&&t.type===n}}(t);function pt(){}function D(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function dt(s,t,e){var a=t.dir,u=e&&"parentNode"===a,c=B++;return t.first?function(t,e,n){for(;t=t[a];)if(1===t.nodeType||u)return s(t,e,n)}:function(t,e,n){var r,i,o=[O,c];if(n){for(;t=t[a];)if((1===t.nodeType||u)&&s(t,e,n))return!0}else for(;t=t[a];)if(1===t.nodeType||u){if((r=(i=(i=t[S]||(t[S]={}))[t.uniqueID]||(i[t.uniqueID]={}))[a])&&r[0]===O&&r[1]===c)return o[2]=r[2];if((i[a]=o)[2]=s(t,e,n))return!0}}}function gt(i){return 1<i.length?function(t,e,n){for(var r=i.length;r--;)if(!i[r](t,e,n))return!1;return!0}:i[0]}function R(t,e,n,r,i){for(var o,s=[],a=0,u=t.length,c=null!=e;a<u;a++)!(o=t[a])||n&&!n(o,r,i)||(s.push(o),c&&e.push(a));return s}function mt(p,d,g,m,v,t){return m&&!m[S]&&(m=mt(m)),v&&!v[S]&&(v=mt(v,t)),j(function(t,e,n,r){var i,o,s,a=[],u=[],c=e.length,l=t||function(t,e,n){for(var r=0,i=e.length;r<i;r++)I(t,e[r],n);return n}(d||"*",n.nodeType?[n]:n,[]),h=!p||!t&&d?l:R(l,a,p,n,r),f=g?v||(t?p:c||m)?[]:e:h;if(g&&g(h,f,n,r),m)for(i=R(f,u),m(i,[],n,r),o=i.length;o--;)(s=i[o])&&(f[u[o]]=!(h[u[o]]=s));if(t){if(v||p){if(v){for(i=[],o=f.length;o--;)(s=f[o])&&i.push(h[o]=s);v(null,f=[],i,r)}for(o=f.length;o--;)(s=f[o])&&-1<(i=v?T(t,s):a[o])&&(t[i]=!(e[i]=s))}}else f=R(f===e?f.splice(c,f.length):f),v?v(null,e,f,r):E.apply(e,f)})}function vt(m,v){function t(t,e,n,r,i){var o,s,a,u=0,c="0",l=t&&[],h=[],f=_,p=t||b&&w.find.TAG("*",i),d=O+=null==f?1:Math.random()||.1,g=p.length;for(i&&(_=e===C||e||i);c!==g&&null!=(o=p[c]);c++){if(b&&o){for(s=0,e||o.ownerDocument===C||(x(o),n=!k);a=m[s++];)if(a(o,e||C,n)){r.push(o);break}i&&(O=d)}y&&((o=!a&&o)&&u--,t&&l.push(o))}if(u+=c,y&&c!==u){for(s=0;a=v[s++];)a(l,h,e,n);if(t){if(0<u)for(;c--;)l[c]||h[c]||(h[c]=V.call(r));h=R(h)}E.apply(r,h),i&&!t&&0<h.length&&1<u+v.length&&I.uniqueSort(r)}return i&&(O=d,_=f),l}var y=0<v.length,b=0<m.length;return y?j(t):t}return pt.prototype=w.filters=w.pseudos,w.setFilters=new pt,g=I.tokenize=function(t,e){var n,r,i,o,s,a,u,c=z[t+" "];if(c)return e?0:c.slice(0);for(s=t,a=[],u=w.preFilter;s;){for(o in n&&!(r=Z.exec(s))||(r&&(s=s.slice(r[0].length)||s),a.push(i=[])),n=!1,(r=tt.exec(s))&&(n=r.shift(),i.push({value:n,type:r[0].replace(A," ")}),s=s.slice(n.length)),w.filter)!(r=f[o].exec(s))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),s=s.slice(n.length));if(!n)break}return e?s.length:s?I.error(t):z(t,a).slice(0)},F=I.compile=function(t,e){var n,r=[],i=[],o=b[t+" "];if(!o){for(n=(e=e||g(t)).length;n--;)((o=function t(e){for(var r,n,i,o=e.length,s=w.relative[e[0].type],a=s||w.relative[" "],u=s?1:0,c=dt(function(t){return t===r},a,!0),l=dt(function(t){return-1<T(r,t)},a,!0),h=[function(t,e,n){return t=!s&&(n||e!==_)||((r=e).nodeType?c:l)(t,e,n),r=null,t}];u<o;u++)if(n=w.relative[e[u].type])h=[dt(gt(h),n)];else{if((n=w.filter[e[u].type].apply(null,e[u].matches))[S]){for(i=++u;i<o&&!w.relative[e[i].type];i++);return mt(1<u&&gt(h),1<u&&D(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(A,"$1"),n,u<i&&t(e.slice(u,i)),i<o&&t(e=e.slice(i)),i<o&&D(e))}h.push(n)}return gt(h)}(e[n]))[S]?r:i).push(o);(o=b(t,vt(i,r))).selector=t}return o},H=I.select=function(t,e,n,r){var i,o,s,a,u,c="function"==typeof t&&t,l=!r&&g(t=c.selector||t);if(n=n||[],1===l.length){if(2<(o=l[0]=l[0].slice(0)).length&&"ID"===(s=o[0]).type&&d.getById&&9===e.nodeType&&k&&w.relative[o[1].type]){if(!(e=(w.find.ID(s.matches[0].replace(p,h),e)||[])[0]))return n;c&&(e=e.parentNode),t=t.slice(o.shift().value.length)}for(i=f.needsContext.test(t)?0:o.length;i--&&(s=o[i],!w.relative[a=s.type]);)if((u=w.find[a])&&(r=u(s.matches[0].replace(p,h),at.test(o[0].type)&&ft(e.parentNode)||e))){if(o.splice(i,1),t=r.length&&D(o))break;return E.apply(n,r),n}}return(c||F(t,l))(r,e,!k,n,!e||at.test(t)&&ft(e.parentNode)||e),n},d.sortStable=S.split("").sort(U).join("")===S,d.detectDuplicates=!!c,x(),d.sortDetached=P(function(t){return 1&t.compareDocumentPosition(C.createElement("div"))}),P(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||lt("type|href|height|width",function(t,e,n){return n?void 0:t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),d.attributes&&P(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||lt("value",function(t,e,n){return n||"input"!==t.nodeName.toLowerCase()?void 0:t.defaultValue}),P(function(t){return null==t.getAttribute("disabled")})||lt(Y,function(t,e,n){return n?void 0:!0===t[e]?e.toLowerCase():(n=t.getAttributeNode(e))&&n.specified?n.value:null}),I}(_),$=(C.find=e,C.expr=e.selectors,C.expr[":"]=C.expr.pseudos,C.uniqueSort=C.unique=e.uniqueSort,C.text=e.getText,C.isXMLDoc=e.isXML,C.contains=e.contains,C.expr.match.needsContext),X=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,Y=/^.[^:#\[\.,]*$/;function K(t,n,r){if(C.isFunction(n))return C.grep(t,function(t,e){return!!n.call(t,e,t)!==r});if(n.nodeType)return C.grep(t,function(t){return t===n!==r});if("string"==typeof n){if(Y.test(n))return C.filter(n,t,r);n=C.filter(n,t)}return C.grep(t,function(t){return-1<i.call(n,t)!==r})}C.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?C.find.matchesSelector(r,t)?[r]:[]:C.find.matches(t,C.grep(e,function(t){return 1===t.nodeType}))},C.fn.extend({find:function(t){var e,n=this.length,r=[],i=this;if("string"!=typeof t)return this.pushStack(C(t).filter(function(){for(e=0;e<n;e++)if(C.contains(i[e],this))return!0}));for(e=0;e<n;e++)C.find(t,i[e],r);return(r=this.pushStack(1<n?C.unique(r):r)).selector=this.selector?this.selector+" "+t:t,r},filter:function(t){return this.pushStack(K(this,t||[],!1))},not:function(t){return this.pushStack(K(this,t||[],!0))},is:function(t){return!!K(this,"string"==typeof t&&$.test(t)?C(t):t||[],!1).length}});var Q,J=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,Z=((C.fn.init=function(t,e,n){if(t){if(n=n||Q,"string"!=typeof t)return t.nodeType?(this.context=this[0]=t,this.length=1,this):C.isFunction(t)?void 0!==n.ready?n.ready(t):t(C):(void 0!==t.selector&&(this.selector=t.selector,this.context=t.context),C.makeArray(t,this));if(!(r="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:J.exec(t))||!r[1]&&e)return(!e||e.jquery?e||n:this.constructor(e)).find(t);if(r[1]){if(e=e instanceof C?e[0]:e,C.merge(this,C.parseHTML(r[1],e&&e.nodeType?e.ownerDocument||e:x,!0)),X.test(r[1])&&C.isPlainObject(e))for(var r in e)C.isFunction(this[r])?this[r](e[r]):this.attr(r,e[r])}else(n=x.getElementById(r[2]))&&n.parentNode&&(this.length=1,this[0]=n),this.context=x,this.selector=t}return this}).prototype=C.fn,Q=C(x),/^(?:parents|prev(?:Until|All))/),tt={children:!0,contents:!0,next:!0,prev:!0};function et(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}C.fn.extend({has:function(t){var e=C(t,this),n=e.length;return this.filter(function(){for(var t=0;t<n;t++)if(C.contains(this,e[t]))return!0})},closest:function(t,e){for(var n,r=0,i=this.length,o=[],s=$.test(t)||"string"!=typeof t?C(t,e||this.context):0;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&C.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(1<o.length?C.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?i.call(C(t),this[0]):i.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),C.each({parent:function(t){t=t.parentNode;return t&&11!==t.nodeType?t:null},parents:function(t){return r(t,"parentNode")},parentsUntil:function(t,e,n){return r(t,"parentNode",n)},next:function(t){return et(t,"nextSibling")},prev:function(t){return et(t,"previousSibling")},nextAll:function(t){return r(t,"nextSibling")},prevAll:function(t){return r(t,"previousSibling")},nextUntil:function(t,e,n){return r(t,"nextSibling",n)},prevUntil:function(t,e,n){return r(t,"previousSibling",n)},siblings:function(t){return V((t.parentNode||{}).firstChild,t)},children:function(t){return V(t.firstChild)},contents:function(t){return t.contentDocument||C.merge([],t.childNodes)}},function(r,i){C.fn[r]=function(t,e){var n=C.map(this,i,t);return(e="Until"!==r.slice(-5)?t:e)&&"string"==typeof e&&(n=C.filter(e,n)),1<this.length&&(tt[r]||C.uniqueSort(n),Z.test(r)&&n.reverse()),this.pushStack(n)}});var nt,k=/\S+/g;function rt(){x.removeEventListener("DOMContentLoaded",rt),_.removeEventListener("load",rt),C.ready()}C.Callbacks=function(r){var t,n;r="string"==typeof r?(t=r,n={},C.each(t.match(k)||[],function(t,e){n[e]=!0}),n):C.extend({},r);function i(){for(a=r.once,s=o=!0;c.length;l=-1)for(e=c.shift();++l<u.length;)!1===u[l].apply(e[0],e[1])&&r.stopOnFalse&&(l=u.length,e=!1);r.memory||(e=!1),o=!1,a&&(u=e?[]:"")}var o,e,s,a,u=[],c=[],l=-1,h={add:function(){return u&&(e&&!o&&(l=u.length-1,c.push(e)),function n(t){C.each(t,function(t,e){C.isFunction(e)?r.unique&&h.has(e)||u.push(e):e&&e.length&&"string"!==C.type(e)&&n(e)})}(arguments),e&&!o&&i()),this},remove:function(){return C.each(arguments,function(t,e){for(var n;-1<(n=C.inArray(e,u,n));)u.splice(n,1),n<=l&&l--}),this},has:function(t){return t?-1<C.inArray(t,u):0<u.length},empty:function(){return u=u&&[],this},disable:function(){return a=c=[],u=e="",this},disabled:function(){return!u},lock:function(){return a=c=[],e||(u=e=""),this},locked:function(){return!!a},fireWith:function(t,e){return a||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),o||i()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!s}};return h},C.extend({Deferred:function(t){var o=[["resolve","done",C.Callbacks("once memory"),"resolved"],["reject","fail",C.Callbacks("once memory"),"rejected"],["notify","progress",C.Callbacks("memory")]],i="pending",s={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},then:function(){var i=arguments;return C.Deferred(function(r){C.each(o,function(t,e){var n=C.isFunction(i[t])&&i[t];a[e[1]](function(){var t=n&&n.apply(this,arguments);t&&C.isFunction(t.promise)?t.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[e[0]+"With"](this===s?r.promise():this,n?[t]:arguments)})}),i=null}).promise()},promise:function(t){return null!=t?C.extend(t,s):s}},a={};return s.pipe=s.then,C.each(o,function(t,e){var n=e[2],r=e[3];s[e[1]]=n.add,r&&n.add(function(){i=r},o[1^t][2].disable,o[2][2].lock),a[e[0]]=function(){return a[e[0]+"With"](this===a?s:this,arguments),this},a[e[0]+"With"]=n.fireWith}),s.promise(a),t&&t.call(a,a),a},when:function(t){function e(e,n,r){return function(t){n[e]=this,r[e]=1<arguments.length?l.call(arguments):t,r===i?c.notifyWith(n,r):--u||c.resolveWith(n,r)}}var i,n,r,o=0,s=l.call(arguments),a=s.length,u=1!==a||t&&C.isFunction(t.promise)?a:0,c=1===u?t:C.Deferred();if(1<a)for(i=new Array(a),n=new Array(a),r=new Array(a);o<a;o++)s[o]&&C.isFunction(s[o].promise)?s[o].promise().progress(e(o,n,i)).done(e(o,r,s)).fail(c.reject):--u;return u||c.resolveWith(r,s),c.promise()}}),C.fn.ready=function(t){return C.ready.promise().done(t),this},C.extend({isReady:!1,readyWait:1,holdReady:function(t){t?C.readyWait++:C.ready(!0)},ready:function(t){(!0===t?--C.readyWait:C.isReady)||((C.isReady=!0)!==t&&0<--C.readyWait||(nt.resolveWith(x,[C]),C.fn.triggerHandler&&(C(x).triggerHandler("ready"),C(x).off("ready"))))}}),C.ready.promise=function(t){return nt||(nt=C.Deferred(),"complete"===x.readyState||"loading"!==x.readyState&&!x.documentElement.doScroll?_.setTimeout(C.ready):(x.addEventListener("DOMContentLoaded",rt),_.addEventListener("load",rt))),nt.promise(t)},C.ready.promise();function h(t,e,n,r,i,o,s){var a=0,u=t.length,c=null==n;if("object"===C.type(n))for(a in i=!0,n)h(t,e,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,C.isFunction(r)||(s=!0),e=c?s?(e.call(t,r),null):(c=e,function(t,e,n){return c.call(C(t),n)}):e))for(;a<u;a++)e(t[a],n,s?r:r.call(t[a],a,e(t[a],n)));return i?t:c?e.call(t):u?e(t[0],n):o}function m(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function n(){this.expando=C.expando+n.uid++}n.uid=1,n.prototype={register:function(t,e){e=e||{};return t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,writable:!0,configurable:!0}),t[this.expando]},cache:function(t){var e;return m(t)?((e=t[this.expando])||(e={},m(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e):{}},set:function(t,e,n){var r,i=this.cache(t);if("string"==typeof e)i[e]=n;else for(r in e)i[r]=e[r];return i},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][e]},access:function(t,e,n){var r;return void 0===e||e&&"string"==typeof e&&void 0===n?void 0!==(r=this.get(t,e))?r:this.get(t,C.camelCase(e)):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,r,i,o=t[this.expando];if(void 0!==o){if(void 0===e)this.register(t);else{n=(r=C.isArray(e)?e.concat(e.map(C.camelCase)):(i=C.camelCase(e),e in o?[e,i]:(r=i)in o?[r]:r.match(k)||[])).length;for(;n--;)delete o[r[n]]}void 0!==e&&!C.isEmptyObject(o)||(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){t=t[this.expando];return void 0!==t&&!C.isEmptyObject(t)}};var v=new n,u=new n,it=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ot=/[A-Z]/g;function st(t,e,n){var r;if(void 0===n&&1===t.nodeType)if(r="data-"+e.replace(ot,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:it.test(n)?C.parseJSON(n):n)}catch(t){}u.set(t,e,n)}else n=void 0;return n}C.extend({hasData:function(t){return u.hasData(t)||v.hasData(t)},data:function(t,e,n){return u.access(t,e,n)},removeData:function(t,e){u.remove(t,e)},_data:function(t,e,n){return v.access(t,e,n)},_removeData:function(t,e){v.remove(t,e)}}),C.fn.extend({data:function(r,t){var e,n,i,o=this[0],s=o&&o.attributes;if(void 0!==r)return"object"==typeof r?this.each(function(){u.set(this,r)}):h(this,function(e){var t,n;if(o&&void 0===e)return void 0!==(t=u.get(o,r)||u.get(o,r.replace(ot,"-$&").toLowerCase()))||(n=C.camelCase(r),void 0!==(t=u.get(o,n))||void 0!==(t=st(o,n,void 0)))?t:void 0;n=C.camelCase(r),this.each(function(){var t=u.get(this,n);u.set(this,n,e),-1<r.indexOf("-")&&void 0!==t&&u.set(this,r,e)})},null,t,1<arguments.length,null,!0);if(this.length&&(i=u.get(o),1===o.nodeType&&!v.get(o,"hasDataAttrs"))){for(e=s.length;e--;)s[e]&&(0===(n=s[e].name).indexOf("data-")&&(n=C.camelCase(n.slice(5)),st(o,n,i[n])));v.set(o,"hasDataAttrs",!0)}return i},removeData:function(t){return this.each(function(){u.remove(this,t)})}}),C.extend({queue:function(t,e,n){var r;return t?(r=v.get(t,e=(e||"fx")+"queue"),n&&(!r||C.isArray(n)?r=v.access(t,e,C.makeArray(n)):r.push(n)),r||[]):void 0},dequeue:function(t,e){e=e||"fx";var n=C.queue(t,e),r=n.length,i=n.shift(),o=C._queueHooks(t,e);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete o.stop,i.call(t,function(){C.dequeue(t,e)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return v.get(t,n)||v.access(t,n,{empty:C.Callbacks("once memory").add(function(){v.remove(t,[e+"queue",n])})})}}),C.fn.extend({queue:function(e,n){var t=2;return"string"!=typeof e&&(n=e,e="fx",t--),arguments.length<t?C.queue(this[0],e):void 0===n?this:this.each(function(){var t=C.queue(this,e,n);C._queueHooks(this,e),"fx"===e&&"inprogress"!==t[0]&&C.dequeue(this,e)})},dequeue:function(t){return this.each(function(){C.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){function n(){--i||o.resolveWith(s,[s])}var r,i=1,o=C.Deferred(),s=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(r=v.get(s[a],t+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(e)}});function y(t,e){return"none"===C.css(t=e||t,"display")||!C.contains(t.ownerDocument,t)}var t=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,f=new RegExp("^(?:([+-])=|)("+t+")([a-z%]*)$","i"),a=["Top","Right","Bottom","Left"];function at(t,e,n,r){var i,o=1,s=20,a=r?function(){return r.cur()}:function(){return C.css(t,e,"")},u=a(),c=n&&n[3]||(C.cssNumber[e]?"":"px"),l=(C.cssNumber[e]||"px"!==c&&+u)&&f.exec(C.css(t,e));if(l&&l[3]!==c)for(c=c||l[3],n=n||[],l=+u||1;C.style(t,e,(l/=o=o||".5")+c),o!==(o=a()/u)&&1!==o&&--s;);return n&&(l=+l||+u||0,i=n[1]?l+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=l,r.end=i)),i}var ut=/^(?:checkbox|radio)$/i,ct=/<([\w:-]+)/,lt=/^$|\/(?:java|ecma)script/i,b={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function w(t,e){var n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[];return void 0===e||e&&C.nodeName(t,e)?C.merge([t],n):n}function ht(t,e){for(var n=0,r=t.length;n<r;n++)v.set(t[n],"globalEval",!e||v.get(e[n],"globalEval"))}b.optgroup=b.option,b.tbody=b.tfoot=b.colgroup=b.caption=b.thead,b.th=b.td;var ft=/<|&#?\w+;/;function pt(t,e,n,r,i){for(var o,s,a,u,c,l=e.createDocumentFragment(),h=[],f=0,p=t.length;f<p;f++)if((o=t[f])||0===o)if("object"===C.type(o))C.merge(h,o.nodeType?[o]:o);else if(ft.test(o)){for(s=s||l.appendChild(e.createElement("div")),a=(ct.exec(o)||["",""])[1].toLowerCase(),a=b[a]||b._default,s.innerHTML=a[1]+C.htmlPrefilter(o)+a[2],c=a[0];c--;)s=s.lastChild;C.merge(h,s.childNodes),(s=l.firstChild).textContent=""}else h.push(e.createTextNode(o));for(l.textContent="",f=0;o=h[f++];)if(r&&-1<C.inArray(o,r))i&&i.push(o);else if(u=C.contains(o.ownerDocument,o),s=w(l.appendChild(o),"script"),u&&ht(s),n)for(c=0;o=s[c++];)lt.test(o.type||"")&&n.push(o);return l}e=x.createDocumentFragment().appendChild(x.createElement("div")),(I=x.createElement("input")).setAttribute("type","radio"),I.setAttribute("checked","checked"),I.setAttribute("name","t"),e.appendChild(I),g.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,e.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue;var dt=/^key/,gt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,mt=/^([^.]*)(?:\.(.+)|)/;function vt(){return!0}function c(){return!1}function yt(){try{return x.activeElement}catch(t){}}function bt(t,e,n,r,i,o){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(r=r||n,n=void 0),e)bt(t,a,n,r,e[a],o);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=c;else if(!i)return t;return 1===o&&(s=i,(i=function(t){return C().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=C.guid++)),t.each(function(){C.event.add(this,e,i,r,n)})}C.event={global:{},add:function(e,t,n,r,i){var o,s,a,u,c,l,h,f,p,d=v.get(e);if(d)for(n.handler&&(n=(o=n).handler,i=o.selector),n.guid||(n.guid=C.guid++),(a=d.events)||(a=d.events={}),(s=d.handle)||(s=d.handle=function(t){return void 0!==C&&C.event.triggered!==t.type?C.event.dispatch.apply(e,arguments):void 0}),u=(t=(t||"").match(k)||[""]).length;u--;)h=p=(f=mt.exec(t[u])||[])[1],f=(f[2]||"").split(".").sort(),h&&(c=C.event.special[h]||{},h=(i?c.delegateType:c.bindType)||h,c=C.event.special[h]||{},p=C.extend({type:h,origType:p,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&C.expr.match.needsContext.test(i),namespace:f.join(".")},o),(l=a[h])||((l=a[h]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(e,r,f,s)||e.addEventListener&&e.addEventListener(h,s)),c.add&&(c.add.call(e,p),p.handler.guid||(p.handler.guid=n.guid)),i?l.splice(l.delegateCount++,0,p):l.push(p),C.event.global[h]=!0)},remove:function(t,e,n,r,i){var o,s,a,u,c,l,h,f,p,d,g,m=v.hasData(t)&&v.get(t);if(m&&(u=m.events)){for(c=(e=(e||"").match(k)||[""]).length;c--;)if(p=g=(a=mt.exec(e[c])||[])[1],d=(a[2]||"").split(".").sort(),p){for(h=C.event.special[p]||{},f=u[p=(r?h.delegateType:h.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)l=f[o],!i&&g!==l.origType||n&&n.guid!==l.guid||a&&!a.test(l.namespace)||r&&r!==l.selector&&("**"!==r||!l.selector)||(f.splice(o,1),l.selector&&f.delegateCount--,h.remove&&h.remove.call(t,l));s&&!f.length&&(h.teardown&&!1!==h.teardown.call(t,d,m.handle)||C.removeEvent(t,p,m.handle),delete u[p])}else for(p in u)C.event.remove(t,p+e[c],n,r,!0);C.isEmptyObject(u)&&v.remove(t,"handle events")}},dispatch:function(t){t=C.event.fix(t);var e,n,r,i,o,s=l.call(arguments),a=(v.get(this,"events")||{})[t.type]||[],u=C.event.special[t.type]||{};if((s[0]=t).delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,t)){for(o=C.event.handlers.call(this,t,a),e=0;(r=o[e++])&&!t.isPropagationStopped();)for(t.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!t.isImmediatePropagationStopped();)t.rnamespace&&!t.rnamespace.test(i.namespace)||(t.handleObj=i,t.data=i.data,void 0!==(i=((C.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,s))&&!1===(t.result=i)&&(t.preventDefault(),t.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,t),t.result}},handlers:function(t,e){var n,r,i,o,s=[],a=e.delegateCount,u=t.target;if(a&&u.nodeType&&("click"!==t.type||isNaN(t.button)||t.button<1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&(!0!==u.disabled||"click"!==t.type)){for(r=[],n=0;n<a;n++)void 0===r[i=(o=e[n]).selector+" "]&&(r[i]=o.needsContext?-1<C(i,this).index(u):C.find(i,this,null,[u]).length),r[i]&&r.push(o);r.length&&s.push({elem:u,handlers:r})}return a<e.length&&s.push({elem:this,handlers:e.slice(a)}),s},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(t,e){return null==t.which&&(t.which=null!=e.charCode?e.charCode:e.keyCode),t}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(t,e){var n,r,i=e.button;return null==t.pageX&&null!=e.clientX&&(n=(r=t.target.ownerDocument||x).documentElement,r=r.body,t.pageX=e.clientX+(n&&n.scrollLeft||r&&r.scrollLeft||0)-(n&&n.clientLeft||r&&r.clientLeft||0),t.pageY=e.clientY+(n&&n.scrollTop||r&&r.scrollTop||0)-(n&&n.clientTop||r&&r.clientTop||0)),t.which||void 0===i||(t.which=1&i?1:2&i?3:4&i?2:0),t}},fix:function(t){if(t[C.expando])return t;var e,n,r,i=t.type,o=t,s=this.fixHooks[i];for(s||(this.fixHooks[i]=s=gt.test(i)?this.mouseHooks:dt.test(i)?this.keyHooks:{}),r=s.props?this.props.concat(s.props):this.props,t=new C.Event(o),e=r.length;e--;)t[n=r[e]]=o[n];return t.target||(t.target=x),3===t.target.nodeType&&(t.target=t.target.parentNode),s.filter?s.filter(t,o):t},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==yt()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===yt()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&C.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(t){return C.nodeName(t.target,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},C.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},C.Event=function(t,e){return this instanceof C.Event?(t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?vt:c):this.type=t,e&&C.extend(this,e),this.timeStamp=t&&t.timeStamp||C.now(),void(this[C.expando]=!0)):new C.Event(t,e)},C.Event.prototype={constructor:C.Event,isDefaultPrevented:c,isPropagationStopped:c,isImmediatePropagationStopped:c,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=vt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=vt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=vt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,i){C.event.special[t]={delegateType:i,bindType:i,handle:function(t){var e,n=t.relatedTarget,r=t.handleObj;return n&&(n===this||C.contains(this,n))||(t.type=r.origType,e=r.handler.apply(this,arguments),t.type=i),e}}}),C.fn.extend({on:function(t,e,n,r){return bt(this,t,e,n,r)},one:function(t,e,n,r){return bt(this,t,e,n,r,1)},off:function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)r=t.handleObj,C(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);else{if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=c),this.each(function(){C.event.remove(this,t,n,e)});for(i in t)this.off(i,e,t[i])}return this}});var wt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,_t=/<script|<style|<link/i,xt=/checked\s*(?:[^=]|=\s*.checked.)/i,Ct=/^true\/(.*)/,kt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function St(t,e){return C.nodeName(t,"table")&&C.nodeName(11!==e.nodeType?e:e.firstChild,"tr")?t.getElementsByTagName("tbody")[0]||t.appendChild(t.ownerDocument.createElement("tbody")):t}function Ot(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Et(t){var e=Ct.exec(t.type);return e?t.type=e[1]:t.removeAttribute("type"),t}function Tt(t,e){var n,r,i,o,s,a;if(1===e.nodeType){if(v.hasData(t)&&(o=v.access(t),s=v.set(e,o),a=o.events))for(i in delete s.handle,s.events={},a)for(n=0,r=a[i].length;n<r;n++)C.event.add(e,i,a[i][n]);u.hasData(t)&&(o=u.access(t),s=C.extend({},o),u.set(e,s))}}function S(n,r,i,o){r=L.apply([],r);var t,e,s,a,u,c,l=0,h=n.length,f=h-1,p=r[0],d=C.isFunction(p);if(d||1<h&&"string"==typeof p&&!g.checkClone&&xt.test(p))return n.each(function(t){var e=n.eq(t);d&&(r[0]=p.call(this,t,e.html())),S(e,r,i,o)});if(h&&(e=(t=pt(r,n[0].ownerDocument,!1,n,o)).firstChild,1===t.childNodes.length&&(t=e),e||o)){for(a=(s=C.map(w(t,"script"),Ot)).length;l<h;l++)u=t,l!==f&&(u=C.clone(u,!0,!0),a&&C.merge(s,w(u,"script"))),i.call(n[l],u,l);if(a)for(c=s[s.length-1].ownerDocument,C.map(s,Et),l=0;l<a;l++)u=s[l],lt.test(u.type||"")&&!v.access(u,"globalEval")&&C.contains(c,u)&&(u.src?C._evalUrl&&C._evalUrl(u.src):C.globalEval(u.textContent.replace(kt,"")))}return n}function At(t,e,n){for(var r,i=e?C.filter(e,t):t,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||C.cleanData(w(r)),r.parentNode&&(n&&C.contains(r.ownerDocument,r)&&ht(w(r,"script")),r.parentNode.removeChild(r));return t}C.extend({htmlPrefilter:function(t){return t.replace(wt,"<$1></$2>")},clone:function(t,e,n){var r,i,o,s,a,u,c,l=t.cloneNode(!0),h=C.contains(t.ownerDocument,t);if(!(g.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||C.isXMLDoc(t)))for(s=w(l),r=0,i=(o=w(t)).length;r<i;r++)a=o[r],u=s[r],c=void 0,"input"===(c=u.nodeName.toLowerCase())&&ut.test(a.type)?u.checked=a.checked:"input"!==c&&"textarea"!==c||(u.defaultValue=a.defaultValue);if(e)if(n)for(o=o||w(t),s=s||w(l),r=0,i=o.length;r<i;r++)Tt(o[r],s[r]);else Tt(t,l);return 0<(s=w(l,"script")).length&&ht(s,!h&&w(t,"script")),l},cleanData:function(t){for(var e,n,r,i=C.event.special,o=0;void 0!==(n=t[o]);o++)if(m(n)){if(e=n[v.expando]){if(e.events)for(r in e.events)i[r]?C.event.remove(n,r):C.removeEvent(n,r,e.handle);n[v.expando]=void 0}n[u.expando]&&(n[u.expando]=void 0)}}}),C.fn.extend({domManip:S,detach:function(t){return At(this,t,!0)},remove:function(t){return At(this,t)},text:function(t){return h(this,function(t){return void 0===t?C.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return S(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||St(this,t).appendChild(t)})},prepend:function(){return S(this,arguments,function(t){var e;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(e=St(this,t)).insertBefore(t,e.firstChild)})},before:function(){return S(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return S(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(C.cleanData(w(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return C.clone(this,t,e)})},html:function(t){return h(this,function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!_t.test(t)&&!b[(ct.exec(t)||["",""])[1].toLowerCase()]){t=C.htmlPrefilter(t);try{for(;n<r;n++)1===(e=this[n]||{}).nodeType&&(C.cleanData(w(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var n=[];return S(this,arguments,function(t){var e=this.parentNode;C.inArray(this,n)<0&&(C.cleanData(w(this)),e&&e.replaceChild(t,this))},n)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,s){C.fn[t]=function(t){for(var e,n=[],r=C(t),i=r.length-1,o=0;o<=i;o++)e=o===i?this:this.clone(!0),C(r[o])[s](e),F.apply(n,e.get());return this.pushStack(n)}});var It,jt={HTML:"block",BODY:"block"};function Pt(t,e){t=C(e.createElement(t)).appendTo(e.body),e=C.css(t[0],"display");return t.detach(),e}function Nt(t){var e=x,n=jt[t];return n||("none"!==(n=Pt(t,e))&&n||((e=(It=(It||C("<iframe frameborder='0' width='0' height='0'/>")).appendTo(e.documentElement))[0].contentDocument).write(),e.close(),n=Pt(t,e),It.detach()),jt[t]=n),n}function Dt(t){var e=t.ownerDocument.defaultView;return(e=e&&e.opener?e:_).getComputedStyle(t)}function Rt(t,e,n,r){var i,o={};for(i in e)o[i]=t.style[i],t.style[i]=e[i];for(i in n=n.apply(t,r||[]),e)t.style[i]=o[i];return n}var Mt,o,Wt,Lt,s,p,Ft=/^margin/,Ht=new RegExp("^("+t+")(?!px)[a-z%]+$","i"),O=x.documentElement;function Bt(){p.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",p.innerHTML="",O.appendChild(s);var t=_.getComputedStyle(p);Mt="1%"!==t.top,Lt="2px"===t.marginLeft,o="4px"===t.width,p.style.marginRight="50%",Wt="4px"===t.marginRight,O.removeChild(s)}function E(t,e,n){var r,i,o=t.style;return""!==(i=(n=n||Dt(t))?n.getPropertyValue(e)||n[e]:void 0)&&void 0!==i||C.contains(t.ownerDocument,t)||(i=C.style(t,e)),n&&!g.pixelMarginRight()&&Ht.test(i)&&Ft.test(e)&&(t=o.width,e=o.minWidth,r=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=t,o.minWidth=e,o.maxWidth=r),void 0!==i?i+"":i}function qt(t,e){return{get:function(){return t()?void delete this.get:(this.get=e).apply(this,arguments)}}}s=x.createElement("div"),(p=x.createElement("div")).style&&(p.style.backgroundClip="content-box",p.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===p.style.backgroundClip,s.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",s.appendChild(p),C.extend(g,{pixelPosition:function(){return Bt(),Mt},boxSizingReliable:function(){return null==o&&Bt(),o},pixelMarginRight:function(){return null==o&&Bt(),Wt},reliableMarginLeft:function(){return null==o&&Bt(),Lt},reliableMarginRight:function(){var t,e=p.appendChild(x.createElement("div"));return e.style.cssText=p.style.cssText="-webkit-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",e.style.marginRight=e.style.width="0",p.style.width="1px",O.appendChild(s),t=!parseFloat(_.getComputedStyle(e).marginRight),O.removeChild(s),p.removeChild(e),t}}));var zt=/^(none|table(?!-c[ea]).+)/,Ut={position:"absolute",visibility:"hidden",display:"block"},Gt={letterSpacing:"0",fontWeight:"400"},Vt=["Webkit","O","Moz","ms"],$t=x.createElement("div").style;function Xt(t){if(t in $t)return t;for(var e=t[0].toUpperCase()+t.slice(1),n=Vt.length;n--;)if((t=Vt[n]+e)in $t)return t}function Yt(t,e,n){var r=f.exec(e);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):e}function Kt(t,e,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===e?1:0,s=0;o<4;o+=2)"margin"===n&&(s+=C.css(t,n+a[o],!0,i)),r?("content"===n&&(s-=C.css(t,"padding"+a[o],!0,i)),"margin"!==n&&(s-=C.css(t,"border"+a[o]+"Width",!0,i))):(s+=C.css(t,"padding"+a[o],!0,i),"padding"!==n&&(s+=C.css(t,"border"+a[o]+"Width",!0,i)));return s}function Qt(t,e,n){var r=!0,i="width"===e?t.offsetWidth:t.offsetHeight,o=Dt(t),s="border-box"===C.css(t,"boxSizing",!1,o);if(i<=0||null==i){if(((i=E(t,e,o))<0||null==i)&&(i=t.style[e]),Ht.test(i))return i;r=s&&(g.boxSizingReliable()||i===t.style[e]),i=parseFloat(i)||0}return i+Kt(t,e,n||(s?"border":"content"),r,o)+"px"}function Jt(t,e){for(var n,r,i,o=[],s=0,a=t.length;s<a;s++)(r=t[s]).style&&(o[s]=v.get(r,"olddisplay"),n=r.style.display,e?(o[s]||"none"!==n||(r.style.display=""),""===r.style.display&&y(r)&&(o[s]=v.access(r,"olddisplay",Nt(r.nodeName)))):(i=y(r),"none"===n&&i||v.set(r,"olddisplay",i?n:C.css(r,"display"))));for(s=0;s<a;s++)!(r=t[s]).style||e&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=e?o[s]||"":"none");return t}function T(t,e,n,r,i){return new T.prototype.init(t,e,n,r,i)}C.extend({cssHooks:{opacity:{get:function(t,e){if(e)return""===(e=E(t,"opacity"))?"1":e}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(t,e,n,r){var i,o,s,a,u;if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style)return a=C.camelCase(e),u=t.style,e=C.cssProps[a]||(C.cssProps[a]=Xt(a)||a),s=C.cssHooks[e]||C.cssHooks[a],void 0===n?s&&"get"in s&&void 0!==(i=s.get(t,!1,r))?i:u[e]:("string"===(o=typeof n)&&(i=f.exec(n))&&i[1]&&(n=at(t,e,i),o="number"),void(null!=n&&n==n&&("number"===o&&(n+=i&&i[3]||(C.cssNumber[a]?"":"px")),g.clearCloneStyle||""!==n||0!==e.indexOf("background")||(u[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,r))||(u[e]=n))))},css:function(t,e,n,r){var i,o=C.camelCase(e);return e=C.cssProps[o]||(C.cssProps[o]=Xt(o)||o),"normal"===(i=void 0===(i=(o=C.cssHooks[e]||C.cssHooks[o])&&"get"in o?o.get(t,!0,n):i)?E(t,e,r):i)&&e in Gt&&(i=Gt[e]),(""===n||n)&&(o=parseFloat(i),!0===n||isFinite(o))?o||0:i}}),C.each(["height","width"],function(t,i){C.cssHooks[i]={get:function(t,e,n){return e?zt.test(C.css(t,"display"))&&0===t.offsetWidth?Rt(t,Ut,function(){return Qt(t,i,n)}):Qt(t,i,n):void 0},set:function(t,e,n){var r=n&&Dt(t),n=n&&Kt(t,i,n,"border-box"===C.css(t,"boxSizing",!1,r),r);return n&&(r=f.exec(e))&&"px"!==(r[3]||"px")&&(t.style[i]=e,e=C.css(t,i)),Yt(0,e,n)}}}),C.cssHooks.marginLeft=qt(g.reliableMarginLeft,function(t,e){return e?(parseFloat(E(t,"marginLeft"))||t.getBoundingClientRect().left-Rt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px":void 0}),C.cssHooks.marginRight=qt(g.reliableMarginRight,function(t,e){return e?Rt(t,{display:"inline-block"},E,[t,"marginRight"]):void 0}),C.each({margin:"",padding:"",border:"Width"},function(i,o){C.cssHooks[i+o]={expand:function(t){for(var e=0,n={},r="string"==typeof t?t.split(" "):[t];e<4;e++)n[i+a[e]+o]=r[e]||r[e-2]||r[0];return n}},Ft.test(i)||(C.cssHooks[i+o].set=Yt)}),C.fn.extend({css:function(t,e){return h(this,function(t,e,n){var r,i,o={},s=0;if(C.isArray(e)){for(r=Dt(t),i=e.length;s<i;s++)o[e[s]]=C.css(t,e[s],!1,r);return o}return void 0!==n?C.style(t,e,n):C.css(t,e)},t,e,1<arguments.length)},show:function(){return Jt(this,!0)},hide:function(){return Jt(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){y(this)?C(this).show():C(this).hide()})}}),((C.Tween=T).prototype={constructor:T,init:function(t,e,n,r,i,o){this.elem=t,this.prop=n,this.easing=i||C.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=o||(C.cssNumber[n]?"":"px")},cur:function(){var t=T.propHooks[this.prop];return(t&&t.get?t:T.propHooks._default).get(this)},run:function(t){var e,n=T.propHooks[this.prop];return this.options.duration?this.pos=e=C.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:T.propHooks._default).set(this),this}}).init.prototype=T.prototype,(T.propHooks={_default:{get:function(t){return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(t=C.css(t.elem,t.prop,""))&&"auto"!==t?t:0},set:function(t){C.fx.step[t.prop]?C.fx.step[t.prop](t):1!==t.elem.nodeType||null==t.elem.style[C.cssProps[t.prop]]&&!C.cssHooks[t.prop]?t.elem[t.prop]=t.now:C.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=T.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},C.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},C.fx=T.prototype.init,C.fx.step={};var A,Zt,I,te=/^(?:toggle|show|hide)$/,ee=/queueHooks$/;function ne(){return _.setTimeout(function(){A=void 0}),A=C.now()}function re(t,e){var n,r=0,i={height:t};for(e=e?1:0;r<4;r+=2-e)i["margin"+(n=a[r])]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function ie(t,e,n){for(var r,i=(j.tweeners[e]||[]).concat(j.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,e,t))return r}function j(i,t,e){var n,o,r,s,a,u,c,l=0,h=j.prefilters.length,f=C.Deferred().always(function(){delete p.elem}),p=function(){if(o)return!1;for(var t=A||ne(),t=Math.max(0,d.startTime+d.duration-t),e=1-(t/d.duration||0),n=0,r=d.tweens.length;n<r;n++)d.tweens[n].run(e);return f.notifyWith(i,[d,e,t]),e<1&&r?t:(f.resolveWith(i,[d]),!1)},d=f.promise({elem:i,props:C.extend({},t),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},e),originalProperties:t,originalOptions:e,startTime:A||ne(),duration:e.duration,tweens:[],createTween:function(t,e){e=C.Tween(i,d.opts,t,e,d.opts.specialEasing[t]||d.opts.easing);return d.tweens.push(e),e},stop:function(t){var e=0,n=t?d.tweens.length:0;if(!o){for(o=!0;e<n;e++)d.tweens[e].run(1);t?(f.notifyWith(i,[d,1,0]),f.resolveWith(i,[d,t])):f.rejectWith(i,[d,t])}return this}}),g=d.props,m=g,v=d.opts.specialEasing;for(r in m)if(s=C.camelCase(r),a=v[s],u=m[r],C.isArray(u)&&(a=u[1],u=m[r]=u[0]),r!==s&&(m[s]=u,delete m[r]),c=C.cssHooks[s],c&&"expand"in c)for(r in u=c.expand(u),delete m[s],u)r in m||(m[r]=u[r],v[r]=a);else v[s]=a;for(;l<h;l++)if(n=j.prefilters[l].call(d,i,g,d.opts))return C.isFunction(n.stop)&&(C._queueHooks(d.elem,d.opts.queue).stop=C.proxy(n.stop,n)),n;return C.map(g,ie,d),C.isFunction(d.opts.start)&&d.opts.start.call(i,d),C.fx.timer(C.extend(p,{elem:i,anim:d,queue:d.opts.queue})),d.progress(d.opts.progress).done(d.opts.done,d.opts.complete).fail(d.opts.fail).always(d.opts.always)}C.Animation=C.extend(j,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return at(n.elem,t,f.exec(e),n),n}]},tweener:function(t,e){for(var n,r=0,i=(t=C.isFunction(t)?(e=t,["*"]):t.match(k)).length;r<i;r++)n=t[r],j.tweeners[n]=j.tweeners[n]||[],j.tweeners[n].unshift(e)},prefilters:[function(e,t,n){var r,i,o,s,a,u,c,l=this,h={},f=e.style,p=e.nodeType&&y(e),d=v.get(e,"fxshow");for(r in n.queue||(null==(a=C._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,u=a.empty.fire,a.empty.fire=function(){a.unqueued||u()}),a.unqueued++,l.always(function(){l.always(function(){a.unqueued--,C.queue(e,"fx").length||a.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],"inline"===("none"===(c=C.css(e,"display"))?v.get(e,"olddisplay")||Nt(e.nodeName):c)&&"none"===C.css(e,"float")&&(f.display="inline-block")),n.overflow&&(f.overflow="hidden",l.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]})),t)if(i=t[r],te.exec(i)){if(delete t[r],o=o||"toggle"===i,i===(p?"hide":"show")){if("show"!==i||!d||void 0===d[r])continue;p=!0}h[r]=d&&d[r]||C.style(e,r)}else c=void 0;if(C.isEmptyObject(h))"inline"===("none"===c?Nt(e.nodeName):c)&&(f.display=c);else for(r in d?"hidden"in d&&(p=d.hidden):d=v.access(e,"fxshow",{}),o&&(d.hidden=!p),p?C(e).show():l.done(function(){C(e).hide()}),l.done(function(){for(var t in v.remove(e,"fxshow"),h)C.style(e,t,h[t])}),h)s=ie(p?d[r]:0,r,l),r in d||(d[r]=s.start,p&&(s.end=s.start,s.start="width"===r||"height"===r?1:0))}],prefilter:function(t,e){e?j.prefilters.unshift(t):j.prefilters.push(t)}}),C.speed=function(t,e,n){var r=t&&"object"==typeof t?C.extend({},t):{complete:n||!n&&e||C.isFunction(t)&&t,duration:t,easing:n&&e||e&&!C.isFunction(e)&&e};return r.duration=C.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in C.fx.speeds?C.fx.speeds[r.duration]:C.fx.speeds._default,null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){C.isFunction(r.old)&&r.old.call(this),r.queue&&C.dequeue(this,r.queue)},r},C.fn.extend({fadeTo:function(t,e,n,r){return this.filter(y).css("opacity",0).show().end().animate({opacity:e},t,n,r)},animate:function(e,t,n,r){function i(){var t=j(this,C.extend({},e),s);(o||v.get(this,"finish"))&&t.stop(!0)}var o=C.isEmptyObject(e),s=C.speed(t,n,r);return i.finish=i,o||!1===s.queue?this.each(i):this.queue(s.queue,i)},stop:function(i,t,o){function s(t){var e=t.stop;delete t.stop,e(o)}return"string"!=typeof i&&(o=t,t=i,i=void 0),t&&!1!==i&&this.queue(i||"fx",[]),this.each(function(){var t=!0,e=null!=i&&i+"queueHooks",n=C.timers,r=v.get(this);if(e)r[e]&&r[e].stop&&s(r[e]);else for(e in r)r[e]&&r[e].stop&&ee.test(e)&&s(r[e]);for(e=n.length;e--;)n[e].elem!==this||null!=i&&n[e].queue!==i||(n[e].anim.stop(o),t=!1,n.splice(e,1));!t&&o||C.dequeue(this,i)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var t,e=v.get(this),n=e[s+"queue"],r=e[s+"queueHooks"],i=C.timers,o=n?n.length:0;for(e.finish=!0,C.queue(this,s,[]),r&&r.stop&&r.stop.call(this,!0),t=i.length;t--;)i[t].elem===this&&i[t].queue===s&&(i[t].anim.stop(!0),i.splice(t,1));for(t=0;t<o;t++)n[t]&&n[t].finish&&n[t].finish.call(this);delete e.finish})}}),C.each(["toggle","show","hide"],function(t,r){var i=C.fn[r];C.fn[r]=function(t,e,n){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(re(r,!0),t,e,n)}}),C.each({slideDown:re("show"),slideUp:re("hide"),slideToggle:re("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,r){C.fn[t]=function(t,e,n){return this.animate(r,t,e,n)}}),C.timers=[],C.fx.tick=function(){var t,e=0,n=C.timers;for(A=C.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||C.fx.stop(),A=void 0},C.fx.timer=function(t){C.timers.push(t),t()?C.fx.start():C.timers.pop()},C.fx.interval=13,C.fx.start=function(){Zt=Zt||_.setInterval(C.fx.tick,C.fx.interval)},C.fx.stop=function(){_.clearInterval(Zt),Zt=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(r,t){return r=C.fx&&C.fx.speeds[r]||r,this.queue(t=t||"fx",function(t,e){var n=_.setTimeout(t,r);e.stop=function(){_.clearTimeout(n)}})},I=x.createElement("input"),e=x.createElement("select"),t=e.appendChild(x.createElement("option")),I.type="checkbox",g.checkOn=""!==I.value,g.optSelected=t.selected,e.disabled=!0,g.optDisabled=!t.disabled,(I=x.createElement("input")).value="t",I.type="radio",g.radioValue="t"===I.value;var oe,P=C.expr.attrHandle,se=(C.fn.extend({attr:function(t,e){return h(this,C.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){C.removeAttr(this,t)})}}),C.extend({attr:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?C.prop(t,e,n):(1===o&&C.isXMLDoc(t)||(e=e.toLowerCase(),i=C.attrHooks[e]||(C.expr.match.bool.test(e)?oe:void 0)),void 0!==n?null===n?void C.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):!(i&&"get"in i&&null!==(r=i.get(t,e)))&&null==(r=C.find.attr(t,e))?void 0:r)},attrHooks:{type:{set:function(t,e){var n;if(!g.radioValue&&"radio"===e&&C.nodeName(t,"input"))return n=t.value,t.setAttribute("type",e),n&&(t.value=n),e}}},removeAttr:function(t,e){var n,r,i=0,o=e&&e.match(k);if(o&&1===t.nodeType)for(;n=o[i++];)r=C.propFix[n]||n,C.expr.match.bool.test(n)&&(t[r]=!1),t.removeAttribute(n)}}),oe={set:function(t,e,n){return!1===e?C.removeAttr(t,n):t.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),function(t,e){var o=P[e]||C.find.attr;P[e]=function(t,e,n){var r,i;return n||(i=P[e],P[e]=r,r=null!=o(t,e,n)?e.toLowerCase():null,P[e]=i),r}}),/^(?:input|select|textarea|button)$/i),ae=/^(?:a|area)$/i,ue=(C.fn.extend({prop:function(t,e){return h(this,C.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[C.propFix[t]||t]})}}),C.extend({prop:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&C.isXMLDoc(t)||(e=C.propFix[e]||e,i=C.propHooks[e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},propHooks:{tabIndex:{get:function(t){var e=C.find.attr(t,"tabindex");return e?parseInt(e,10):se.test(t.nodeName)||ae.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(C.propHooks.selected={get:function(t){t=t.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(t){t=t.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){C.propFix[this.toLowerCase()]=this}),/[\t\r\n\f]/g);function N(t){return t.getAttribute&&t.getAttribute("class")||""}C.fn.extend({addClass:function(e){var t,n,r,i,o,s,a=0;if(C.isFunction(e))return this.each(function(t){C(this).addClass(e.call(this,t,N(this)))});if("string"==typeof e&&e)for(t=e.match(k)||[];n=this[a++];)if(s=N(n),r=1===n.nodeType&&(" "+s+" ").replace(ue," ")){for(o=0;i=t[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");s!==(s=C.trim(r))&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,r,i,o,s,a=0;if(C.isFunction(e))return this.each(function(t){C(this).removeClass(e.call(this,t,N(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(k)||[];n=this[a++];)if(s=N(n),r=1===n.nodeType&&(" "+s+" ").replace(ue," ")){for(o=0;i=t[o++];)for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");s!==(s=C.trim(r))&&n.setAttribute("class",s)}return this},toggleClass:function(i,e){var o=typeof i;return"boolean"==typeof e&&"string"==o?e?this.addClass(i):this.removeClass(i):C.isFunction(i)?this.each(function(t){C(this).toggleClass(i.call(this,t,N(this),e),e)}):this.each(function(){var t,e,n,r;if("string"==o)for(e=0,n=C(this),r=i.match(k)||[];t=r[e++];)n.hasClass(t)?n.removeClass(t):n.addClass(t);else void 0!==i&&"boolean"!=o||((t=N(this))&&v.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",!t&&!1!==i&&v.get(this,"__className__")||""))})},hasClass:function(t){for(var e,n=0,r=" "+t+" ";e=this[n++];)if(1===e.nodeType&&-1<(" "+N(e)+" ").replace(ue," ").indexOf(r))return!0;return!1}});var ce=/\r/g,le=/[\x20\t\r\n\f]+/g,he=(C.fn.extend({val:function(e){var n,t,r,i=this[0];return arguments.length?(r=C.isFunction(e),this.each(function(t){1===this.nodeType&&(null==(t=r?e.call(this,t,C(this).val()):e)?t="":"number"==typeof t?t+="":C.isArray(t)&&(t=C.map(t,function(t){return null==t?"":t+""})),(n=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,t,"value")||(this.value=t))})):i?(n=C.valHooks[i.type]||C.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(t=n.get(i,"value"))?t:"string"==typeof(t=i.value)?t.replace(ce,""):null==t?"":t:void 0}}),C.extend({valHooks:{option:{get:function(t){var e=C.find.attr(t,"value");return null!=e?e:C.trim(C.text(t)).replace(le," ")}},select:{get:function(t){for(var e,n=t.options,r=t.selectedIndex,i="select-one"===t.type||r<0,o=i?null:[],s=i?r+1:n.length,a=r<0?s:i?r:0;a<s;a++)if(((e=n[a]).selected||a===r)&&(g.optDisabled?!e.disabled:null===e.getAttribute("disabled"))&&(!e.parentNode.disabled||!C.nodeName(e.parentNode,"optgroup"))){if(e=C(e).val(),i)return e;o.push(e)}return o},set:function(t,e){for(var n,r,i=t.options,o=C.makeArray(e),s=i.length;s--;)((r=i[s]).selected=-1<C.inArray(C.valHooks.option.get(r),o))&&(n=!0);return n||(t.selectedIndex=-1),o}}}}),C.each(["radio","checkbox"],function(){C.valHooks[this]={set:function(t,e){return C.isArray(e)?t.checked=-1<C.inArray(C(t).val(),e):void 0}},g.checkOn||(C.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),/^(?:focusinfocus|focusoutblur)$/),D=(C.extend(C.event,{trigger:function(t,e,n,r){var i,o,s,a,u,c,l=[n||x],h=d.call(t,"type")?t.type:t,f=d.call(t,"namespace")?t.namespace.split("."):[],p=o=n=n||x;if(3!==n.nodeType&&8!==n.nodeType&&!he.test(h+C.event.triggered)&&(-1<h.indexOf(".")&&(h=(f=h.split(".")).shift(),f.sort()),a=h.indexOf(":")<0&&"on"+h,(t=t[C.expando]?t:new C.Event(h,"object"==typeof t&&t)).isTrigger=r?2:3,t.namespace=f.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),e=null==e?[t]:C.makeArray(e,[t]),c=C.event.special[h]||{},r||!c.trigger||!1!==c.trigger.apply(n,e))){if(!r&&!c.noBubble&&!C.isWindow(n)){for(s=c.delegateType||h,he.test(s+h)||(p=p.parentNode);p;p=p.parentNode)l.push(p),o=p;o===(n.ownerDocument||x)&&l.push(o.defaultView||o.parentWindow||_)}for(i=0;(p=l[i++])&&!t.isPropagationStopped();)t.type=1<i?s:c.bindType||h,(u=(v.get(p,"events")||{})[t.type]&&v.get(p,"handle"))&&u.apply(p,e),(u=a&&p[a])&&u.apply&&m(p)&&(t.result=u.apply(p,e),!1===t.result&&t.preventDefault());return t.type=h,r||t.isDefaultPrevented()||c._default&&!1!==c._default.apply(l.pop(),e)||!m(n)||a&&C.isFunction(n[h])&&!C.isWindow(n)&&((o=n[a])&&(n[a]=null),n[C.event.triggered=h](),C.event.triggered=void 0,o&&(n[a]=o)),t.result}},simulate:function(t,e,n){n=C.extend(new C.Event,n,{type:t,isSimulated:!0});C.event.trigger(n,null,e)}}),C.fn.extend({trigger:function(t,e){return this.each(function(){C.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];return n?C.event.trigger(t,e,n,!0):void 0}}),C.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(t,n){C.fn[n]=function(t,e){return 0<arguments.length?this.on(n,null,t,e):this.trigger(n)}}),C.fn.extend({hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),g.focusin="onfocusin"in _,g.focusin||C.each({focus:"focusin",blur:"focusout"},function(n,r){function i(t){C.event.simulate(r,t.target,C.event.fix(t))}C.event.special[r]={setup:function(){var t=this.ownerDocument||this,e=v.access(t,r);e||t.addEventListener(n,i,!0),v.access(t,r,(e||0)+1)},teardown:function(){var t=this.ownerDocument||this,e=v.access(t,r)-1;e?v.access(t,r,e):(t.removeEventListener(n,i,!0),v.remove(t,r))}}}),_.location),fe=C.now(),pe=/\?/,de=(C.parseJSON=function(t){return JSON.parse(t+"")},C.parseXML=function(t){var e;if(!t||"string"!=typeof t)return null;try{e=(new _.DOMParser).parseFromString(t,"text/xml")}catch(t){e=void 0}return e&&!e.getElementsByTagName("parsererror").length||C.error("Invalid XML: "+t),e},/#.*$/),ge=/([?&])_=[^&]*/,me=/^(.*?):[ \t]*([^\r\n]*)$/gm,ve=/^(?:GET|HEAD)$/,ye=/^\/\//,be={},we={},_e="*/".concat("*"),xe=x.createElement("a");function Ce(o){return function(t,e){"string"!=typeof t&&(e=t,t="*");var n,r=0,i=t.toLowerCase().match(k)||[];if(C.isFunction(e))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(e)):(o[n]=o[n]||[]).push(e)}}function ke(e,r,i,o){var s={},a=e===we;function u(t){var n;return s[t]=!0,C.each(e[t]||[],function(t,e){e=e(r,i,o);return"string"!=typeof e||a||s[e]?a?!(n=e):void 0:(r.dataTypes.unshift(e),u(e),!1)}),n}return u(r.dataTypes[0])||!s["*"]&&u("*")}function Se(t,e){var n,r,i=C.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((i[n]?t:r=r||{})[n]=e[n]);return r&&C.extend(!0,t,r),t}xe.href=D.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:D.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(D.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":_e,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":C.parseJSON,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Se(Se(t,C.ajaxSettings),e):Se(C.ajaxSettings,t)},ajaxPrefilter:Ce(be),ajaxTransport:Ce(we),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0);var u,c,l,n,h,f,r,p=C.ajaxSetup({},e=e||{}),d=p.context||p,g=p.context&&(d.nodeType||d.jquery)?C(d):C.event,m=C.Deferred(),v=C.Callbacks("once memory"),y=p.statusCode||{},i={},o={},b=0,s="canceled",w={readyState:0,getResponseHeader:function(t){var e;if(2===b){if(!n)for(n={};e=me.exec(l);)n[e[1].toLowerCase()]=e[2];e=n[t.toLowerCase()]}return null==e?null:e},getAllResponseHeaders:function(){return 2===b?l:null},setRequestHeader:function(t,e){var n=t.toLowerCase();return b||(t=o[n]=o[n]||t,i[t]=e),this},overrideMimeType:function(t){return b||(p.mimeType=t),this},statusCode:function(t){if(t)if(b<2)for(var e in t)y[e]=[y[e],t[e]];else w.always(t[w.status]);return this},abort:function(t){t=t||s;return u&&u.abort(t),a(0,t),this}};if(m.promise(w).complete=v.add,w.success=w.done,w.error=w.fail,p.url=((t||p.url||D.href)+"").replace(de,"").replace(ye,D.protocol+"//"),p.type=e.method||e.type||p.method||p.type,p.dataTypes=C.trim(p.dataType||"*").toLowerCase().match(k)||[""],null==p.crossDomain){t=x.createElement("a");try{t.href=p.url,t.href=t.href,p.crossDomain=xe.protocol+"//"+xe.host!=t.protocol+"//"+t.host}catch(t){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=C.param(p.data,p.traditional)),ke(be,p,e,w),2!==b){for(r in(f=C.event&&p.global)&&0==C.active++&&C.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!ve.test(p.type),c=p.url,p.hasContent||(p.data&&(c=p.url+=(pe.test(c)?"&":"?")+p.data,delete p.data),!1===p.cache&&(p.url=ge.test(c)?c.replace(ge,"$1_="+fe++):c+(pe.test(c)?"&":"?")+"_="+fe++)),p.ifModified&&(C.lastModified[c]&&w.setRequestHeader("If-Modified-Since",C.lastModified[c]),C.etag[c]&&w.setRequestHeader("If-None-Match",C.etag[c])),(p.data&&p.hasContent&&!1!==p.contentType||e.contentType)&&w.setRequestHeader("Content-Type",p.contentType),w.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+_e+"; q=0.01":""):p.accepts["*"]),p.headers)w.setRequestHeader(r,p.headers[r]);if(p.beforeSend&&(!1===p.beforeSend.call(d,w,p)||2===b))return w.abort();for(r in s="abort",{success:1,error:1,complete:1})w[r](p[r]);if(u=ke(we,p,e,w)){if(w.readyState=1,f&&g.trigger("ajaxSend",[w,p]),2===b)return w;p.async&&0<p.timeout&&(h=_.setTimeout(function(){w.abort("timeout")},p.timeout));try{b=1,u.send(i,a)}catch(t){if(!(b<2))throw t;a(-1,t)}}else a(-1,"No Transport")}return w;function a(t,e,n,r){var i,o,s,a=e;2!==b&&(b=2,h&&_.clearTimeout(h),u=void 0,l=r||"",w.readyState=0<t?4:0,r=200<=t&&t<300||304===t,n&&(s=function(t,e,n){for(var r,i,o,s,a=t.contents,u=t.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||t.converters[i+" "+u[0]]){o=i;break}s=s||i}o=o||s}return o?(o!==u[0]&&u.unshift(o),n[o]):void 0}(p,w,n)),s=function(t,e,n,r){var i,o,s,a,u,c={},l=t.dataTypes.slice();if(l[1])for(s in t.converters)c[s.toLowerCase()]=t.converters[s];for(o=l.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!u&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),u=o,o=l.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(s=c[u+" "+o]||c["* "+o]))for(i in c)if(a=i.split(" "),a[1]===o&&(s=c[u+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[i]:!0!==c[i]&&(o=a[0],l.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+u+" to "+o}}}return{state:"success",data:e}}(p,s,w,r),r?(p.ifModified&&((n=w.getResponseHeader("Last-Modified"))&&(C.lastModified[c]=n),(n=w.getResponseHeader("etag"))&&(C.etag[c]=n)),204===t||"HEAD"===p.type?a="nocontent":304===t?a="notmodified":(a=s.state,i=s.data,r=!(o=s.error))):(o=a,!t&&a||(a="error",t<0&&(t=0))),w.status=t,w.statusText=(e||a)+"",r?m.resolveWith(d,[i,a,w]):m.rejectWith(d,[w,a,o]),w.statusCode(y),y=void 0,f&&g.trigger(r?"ajaxSuccess":"ajaxError",[w,p,r?i:o]),v.fireWith(d,[w,a]),f&&(g.trigger("ajaxComplete",[w,p]),--C.active||C.event.trigger("ajaxStop")))}},getJSON:function(t,e,n){return C.get(t,e,n,"json")},getScript:function(t,e){return C.get(t,void 0,e,"script")}}),C.each(["get","post"],function(t,i){C[i]=function(t,e,n,r){return C.isFunction(e)&&(r=r||n,n=e,e=void 0),C.ajax(C.extend({url:t,type:i,dataType:r,data:e,success:n},C.isPlainObject(t)&&t))}}),C._evalUrl=function(t){return C.ajax({url:t,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},C.fn.extend({wrapAll:function(e){var t;return C.isFunction(e)?this.each(function(t){C(this).wrapAll(e.call(this,t))}):(this[0]&&(t=C(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this)},wrapInner:function(n){return C.isFunction(n)?this.each(function(t){C(this).wrapInner(n.call(this,t))}):this.each(function(){var t=C(this),e=t.contents();e.length?e.wrapAll(n):t.append(n)})},wrap:function(e){var n=C.isFunction(e);return this.each(function(t){C(this).wrapAll(n?e.call(this,t):e)})},unwrap:function(){return this.parent().each(function(){C.nodeName(this,"body")||C(this).replaceWith(this.childNodes)}).end()}}),C.expr.filters.hidden=function(t){return!C.expr.filters.visible(t)},C.expr.filters.visible=function(t){return 0<t.offsetWidth||0<t.offsetHeight||0<t.getClientRects().length};var Oe=/%20/g,Ee=/\[\]$/,Te=/\r?\n/g,Ae=/^(?:submit|button|image|reset|file)$/i,Ie=/^(?:input|select|textarea|keygen)/i;C.param=function(t,e){function n(t,e){e=C.isFunction(e)?e():null==e?"":e,i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(e)}var r,i=[];if(void 0===e&&(e=C.ajaxSettings&&C.ajaxSettings.traditional),C.isArray(t)||t.jquery&&!C.isPlainObject(t))C.each(t,function(){n(this.name,this.value)});else for(r in t)!function n(r,t,i,o){if(C.isArray(t))C.each(t,function(t,e){i||Ee.test(r)?o(r,e):n(r+"["+("object"==typeof e&&null!=e?t:"")+"]",e,i,o)});else if(i||"object"!==C.type(t))o(r,t);else for(var e in t)n(r+"["+e+"]",t[e],i,o)}(r,t[r],e,n);return i.join("&").replace(Oe,"+")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=C.prop(this,"elements");return t?C.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!C(this).is(":disabled")&&Ie.test(this.nodeName)&&!Ae.test(t)&&(this.checked||!ut.test(t))}).map(function(t,e){var n=C(this).val();return null==n?null:C.isArray(n)?C.map(n,function(t){return{name:e.name,value:t.replace(Te,"\r\n")}}):{name:e.name,value:n.replace(Te,"\r\n")}}).get()}}),C.ajaxSettings.xhr=function(){try{return new _.XMLHttpRequest}catch(t){}};var je={0:200,1223:204},R=C.ajaxSettings.xhr(),Pe=(g.cors=!!R&&"withCredentials"in R,g.ajax=R=!!R,C.ajaxTransport(function(i){var o,s;return g.cors||R&&!i.crossDomain?{send:function(t,e){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||t["X-Requested-With"]||(t["X-Requested-With"]="XMLHttpRequest"),t)r.setRequestHeader(n,t[n]);o=function(t){return function(){o&&(o=s=r.onload=r.onerror=r.onabort=r.onreadystatechange=null,"abort"===t?r.abort():"error"===t?"number"!=typeof r.status?e(0,"error"):e(r.status,r.statusText):e(je[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),s=r.onerror=o("error"),void 0!==r.onabort?r.onabort=s:r.onreadystatechange=function(){4===r.readyState&&_.setTimeout(function(){o&&s()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(t){if(o)throw t}},abort:function(){o&&o()}}:void 0}),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return C.globalEval(t),t}}}),C.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),C.ajaxTransport("script",function(n){var r,i;if(n.crossDomain)return{send:function(t,e){r=C("<script>").prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(t){r.remove(),i=null,t&&e("error"===t.type?404:200,t.type)}),x.head.appendChild(r[0])},abort:function(){i&&i()}}}),[]),Ne=/(=)\?(?=&|$)|\?\?/,De=(C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Pe.pop()||C.expando+"_"+fe++;return this[t]=!0,t}}),C.ajaxPrefilter("json jsonp",function(t,e,n){var r,i,o,s=!1!==t.jsonp&&(Ne.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ne.test(t.data)&&"data");return s||"jsonp"===t.dataTypes[0]?(r=t.jsonpCallback=C.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(Ne,"$1"+r):!1!==t.jsonp&&(t.url+=(pe.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return o||C.error(r+" was not called"),o[0]},t.dataTypes[0]="json",i=_[r],_[r]=function(){o=arguments},n.always(function(){void 0===i?C(_).removeProp(r):_[r]=i,t[r]&&(t.jsonpCallback=e.jsonpCallback,Pe.push(r)),o&&C.isFunction(i)&&i(o[0]),o=i=void 0}),"script"):void 0}),C.parseHTML=function(t,e,n){if(!t||"string"!=typeof t)return null;"boolean"==typeof e&&(n=e,e=!1),e=e||x;var r=X.exec(t),n=!n&&[];return r?[e.createElement(r[1])]:(r=pt([t],e,n),n&&n.length&&C(n).remove(),C.merge([],r.childNodes))},C.fn.load);function Re(t){return C.isWindow(t)?t:9===t.nodeType&&t.defaultView}C.fn.load=function(t,e,n){var r,i,o,s,a;return"string"!=typeof t&&De?De.apply(this,arguments):(s=this,-1<(a=t.indexOf(" "))&&(r=C.trim(t.slice(a)),t=t.slice(0,a)),C.isFunction(e)?(n=e,e=void 0):e&&"object"==typeof e&&(i="POST"),0<s.length&&C.ajax({url:t,type:i||"GET",dataType:"html",data:e}).done(function(t){o=arguments,s.html(r?C("<div>").append(C.parseHTML(t)).find(r):t)}).always(n&&function(t,e){s.each(function(){n.apply(this,o||[t.responseText,e,t])})}),this)},C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){C.fn[e]=function(t){return this.on(e,t)}}),C.expr.filters.animated=function(e){return C.grep(C.timers,function(t){return e===t.elem}).length},C.offset={setOffset:function(t,e,n){var r,i,o,s,a=C.css(t,"position"),u=C(t),c={};"static"===a&&(t.style.position="relative"),o=u.offset(),r=C.css(t,"top"),s=C.css(t,"left"),a=("absolute"===a||"fixed"===a)&&-1<(r+s).indexOf("auto")?(i=(a=u.position()).top,a.left):(i=parseFloat(r)||0,parseFloat(s)||0),null!=(e=C.isFunction(e)?e.call(t,n,C.extend({},o)):e).top&&(c.top=e.top-o.top+i),null!=e.left&&(c.left=e.left-o.left+a),"using"in e?e.using.call(t,c):u.css(c)}},C.fn.extend({offset:function(e){var t,n,r,i;return arguments.length?void 0===e?this:this.each(function(t){C.offset.setOffset(this,e,t)}):(r={top:0,left:0},(i=(n=this[0])&&n.ownerDocument)?(t=i.documentElement,C.contains(t,n)?(r=n.getBoundingClientRect(),n=Re(i),{top:r.top+n.pageYOffset-t.clientTop,left:r.left+n.pageXOffset-t.clientLeft}):r):void 0)},position:function(){var t,e,n,r;if(this[0])return n=this[0],r={top:0,left:0},"fixed"===C.css(n,"position")?e=n.getBoundingClientRect():(t=this.offsetParent(),e=this.offset(),(r=C.nodeName(t[0],"html")?r:t.offset()).top+=C.css(t[0],"borderTopWidth",!0),r.left+=C.css(t[0],"borderLeftWidth",!0)),{top:e.top-r.top-C.css(n,"marginTop",!0),left:e.left-r.left-C.css(n,"marginLeft",!0)}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===C.css(t,"position");)t=t.offsetParent;return t||O})}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,i){var o="pageYOffset"===i;C.fn[e]=function(t){return h(this,function(t,e,n){var r=Re(t);return void 0===n?r?r[i]:t[e]:void(r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):t[e]=n)},e,t,arguments.length)}}),C.each(["top","left"],function(t,n){C.cssHooks[n]=qt(g.pixelPosition,function(t,e){return e?(e=E(t,n),Ht.test(e)?C(t).position()[n]+"px":e):void 0})}),C.each({Height:"height",Width:"width"},function(o,s){C.each({padding:"inner"+o,content:s,"":"outer"+o},function(r,t){C.fn[t]=function(t,e){var n=arguments.length&&(r||"boolean"!=typeof t),i=r||(!0===t||!0===e?"margin":"border");return h(this,function(t,e,n){var r;return C.isWindow(t)?t.document.documentElement["client"+o]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+o],r["scroll"+o],t.body["offset"+o],r["offset"+o],r["client"+o])):void 0===n?C.css(t,e,i):C.style(t,e,n,i)},s,n?t:void 0,n,null)}})}),C.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,r){return this.on(e,t,n,r)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},size:function(){return this.length}}),C.fn.andSelf=C.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return C});var Me=_.jQuery,We=_.$;return C.noConflict=function(t){return _.$===C&&(_.$=We),t&&_.jQuery===C&&(_.jQuery=Me),C},M||(_.jQuery=_.$=C),C}),!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.clipboard=e():t.clipboard=e()}(this,function(){return n=[function(t,e,n){"use strict";function a(t){var e=new v,t=function(n,t,r){p("listener called"),n.success=!0,t.forEach(function(t,e){r.clipboardData.setData(e,t),e===m&&r.clipboardData.getData(e)!=t&&(p("setting text/plain failed"),n.success=!1)}),r.preventDefault()}.bind(this,e,t);document.addEventListener("copy",t);try{document.execCommand("copy")}finally{document.removeEventListener("copy",t)}return e.success}function u(t,e){c(t);t=a(e);return l(),t}function c(t){var e=document.getSelection(),n=document.createRange();n.selectNodeContents(t),e.removeAllRanges(),e.addRange(n)}function l(){document.getSelection().removeAllRanges()}function h(){return"undefined"==typeof ClipboardEvent&&void 0!==window.clipboardData&&void 0!==window.clipboardData.setData}function r(){return new f(function(t,e){var n=window.clipboardData.getData("Text");""===n?e(new Error("Empty clipboard or could not read plain text from clipboard")):t(n)})}Object.defineProperty(e,"__esModule",{value:!0});var i=n(1),o=n(5),f="undefined"==typeof Promise?i.Promise:Promise,p=function(t){},d=!0,g=function(){(console.warn||console.log).call(arguments)}.bind(console,"[clipboard-polyfill]"),m="text/plain",n=(s.setDebugLog=function(t){p=t},s.suppressWarnings=function(){d=!1,o.suppressDTWarnings()},s.write=function(s){return d&&!s.getData(m)&&g("clipboard.write() was called without a `text/plain` data type. On some platforms, this may result in an empty clipboard. Call clipboard.suppressWarnings() to suppress this warning."),new f(function(t,e){var n,r,i,o;h()?function(t){if(void 0!==(t=t.getData(m)))return window.clipboardData.setData("Text",t);throw"No `text/plain` value was specified."}(s)?t():e(new Error("Copying failed, possibly because the user rejected it.")):a(s)?(p("regular execCopy worked"),t()):-1<navigator.userAgent.indexOf("Edge")?(p('UA "Edge" => assuming success'),t()):u(document.body,s)?(p("copyUsingTempSelection worked"),t()):(n=s,(r=document.createElement("div")).setAttribute("style","-webkit-user-select: text !important"),r.textContent="temporary element",document.body.appendChild(r),n=u(r,n),document.body.removeChild(r),n?(p("copyUsingTempElem worked"),t()):void 0!==(r=s.getData(m))&&(n=r,p("copyTextUsingDOM"),(r=document.createElement("div")).setAttribute("style","-webkit-user-select: text !important"),(i=r).attachShadow&&(p("Using shadow DOM."),i=r.attachShadow({mode:"open"})),(o=document.createElement("span")).innerText=n,i.appendChild(o),document.body.appendChild(r),c(o),n=document.execCommand("copy"),l(),document.body.removeChild(r),n)?(p("copyTextUsingDOM worked"),t()):e(new Error("Copy command failed.")))})},s.writeText=function(t){var e;return navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(t):((e=new o.DT).setData(m,t),this.write(e))},s.read=function(){return new f(function(n,t){h()?r().then(function(t){return n((t=t,(e=new o.DT).setData(m,t),e));var e},t):t("Read is not supported in your browser.")})},s.readText=function(){return navigator.clipboard&&navigator.clipboard.readText?navigator.clipboard.readText():h()?r():new f(function(t,e){e("Read is not supported in your browser.")})},s.DT=o.DT,s);function s(){}e.default=n;var v=function(){this.success=!1};t.exports=n},function(t,e,st){!function(it,ot){t.exports=function(){"use strict";function n(t){var e=typeof t;return null!==t&&("object"===e||"function"===e)}function c(t){return"function"==typeof t}function t(t){C=t}function M(t){k=t}function W(){return void 0!==x?function(){x(r)}:e()}function e(){var t=setTimeout;return function(){return t(r,1)}}function r(){for(var t=0;t<_;t+=2)(0,T[t])(T[t+1]),T[t]=void 0,T[t+1]=void 0;_=0}function s(t,e){var n=arguments,r=this,i=new this.constructor(u);void 0===i[A]&&y(i);var o=r._state;return o?function(){var t=n[o-1];k(function(){return v(o,i,t,r._result)})}():g(r,i,t,e),i}function a(t){var e=this;if(t&&"object"==typeof t&&t.constructor===e)return t;var n=new e(u);return f(n,t),n}function u(){}function L(){return new TypeError("You cannot resolve a promise with itself")}function F(){return new TypeError("A promises callback cannot return that same promise.")}function l(t){try{return t.then}catch(t){return N.error=t,N}}function H(t,e,n,r){try{t.call(e,n,r)}catch(t){return t}}function B(t,r,i){k(function(e){var n=!1,t=H(i,r,function(t){n||(n=!0,r!==t?f(e,t):p(e,t))},function(t){n||(n=!0,d(e,t))},"Settle: "+(e._label||" unknown promise"));!n&&t&&(n=!0,d(e,t))},t)}function q(e,t){t._state===j?p(e,t._result):t._state===P?d(e,t._result):g(t,void 0,function(t){return f(e,t)},function(t){return d(e,t)})}function h(t,e,n){e.constructor===t.constructor&&n===s&&e.constructor.resolve===a?q(t,e):n===N?(d(t,N.error),N.error=null):void 0===n?p(t,e):c(n)?B(t,e,n):p(t,e)}function f(t,e){t===e?d(t,L()):n(e)?h(t,e,l(e)):p(t,e)}function z(t){t._onerror&&t._onerror(t._result),m(t)}function p(t,e){t._state===I&&(t._result=e,t._state=j,0!==t._subscribers.length&&k(m,t))}function d(t,e){t._state===I&&(t._state=P,t._result=e,k(z,t))}function g(t,e,n,r){var i=t._subscribers,o=i.length;t._onerror=null,i[o]=e,i[o+j]=n,i[o+P]=r,0===o&&t._state&&k(m,t)}function m(t){var e=t._subscribers,n=t._state;if(0!==e.length){for(var r=void 0,i=void 0,o=t._result,s=0;s<e.length;s+=3)r=e[s],i=e[s+n],r?v(n,r,i,o):i(o);t._subscribers.length=0}}function i(){this.error=null}function U(t,e){try{return t(e)}catch(t){return D.error=t,D}}function v(t,e,n,r){var i=c(n),o=void 0,s=void 0,a=void 0,u=void 0;if(i){if(o=U(n,r),o===D?(u=!0,s=o.error,o.error=null):a=!0,e===o)return void d(e,F())}else o=r,a=!0;e._state!==I||(i&&a?f(e,o):u?d(e,s):t===j?p(e,o):t===P&&d(e,o))}function G(e,t){try{t(function(t){f(e,t)},function(t){d(e,t)})}catch(t){d(e,t)}}function V(){return R++}function y(t){t[A]=R++,t._state=void 0,t._result=void 0,t._subscribers=[]}function o(t,e){this._instanceConstructor=t,this.promise=new t(u),this.promise[A]||y(this.promise),w(e)?(this.length=e.length,this._remaining=e.length,this._result=new Array(this.length),0===this.length?p(this.promise,this._result):(this.length=this.length||0,this._enumerate(e),0===this._remaining&&p(this.promise,this._result))):d(this.promise,$())}function $(){return new Error("Array Methods must be provided an Array")}function X(t){return new o(this,t).promise}function Y(i){var o=this;return new o(w(i)?function(t,e){for(var n=i.length,r=0;r<n;r++)o.resolve(i[r]).then(t,e)}:function(t,e){return e(new TypeError("You must pass an array to race."))})}function K(t){var e=this,n=new e(u);return d(n,t),n}function Q(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function J(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function b(t){this[A]=V(),this._result=this._state=void 0,this._subscribers=[],u!==t&&("function"!=typeof t&&Q(),this instanceof b?G(this,t):J())}function Z(){var t=void 0;if(void 0!==ot)t=ot;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var n=null;try{n=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===n&&!e.cast)return}t.Promise=b}var tt=void 0,tt,w=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},_=0,x=void 0,C=void 0,k=function(t,e){T[_]=t,T[_+1]=e,2===(_+=2)&&(C?C(r):rt())},S="undefined"!=typeof window?window:void 0,O=S||{},E=O.MutationObserver||O.WebKitMutationObserver,et="undefined"==typeof self&&void 0!==it&&"[object process]"==={}.toString.call(it),nt="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,T=new Array(1e3),rt=void 0,rt=et?function(){return function(){return it.nextTick(r)}}():E?function(){var t=0,e=new E(r),n=document.createTextNode("");return e.observe(n,{characterData:!0}),function(){n.data=t=++t%2}}():nt?function(){var t=new MessageChannel;return t.port1.onmessage=r,function(){return t.port2.postMessage(0)}}():void 0===S?function(){try{var t=st(4);return x=t.runOnLoop||t.runOnContext,W()}catch(t){return e()}}():e(),A=Math.random().toString(36).substring(16),I=void 0,j=1,P=2,N=new i,D=new i,R=0;return o.prototype._enumerate=function(t){for(var e=0;this._state===I&&e<t.length;e++)this._eachEntry(t[e],e)},o.prototype._eachEntry=function(e,t){var n=this._instanceConstructor,r=n.resolve;if(r===a){var i=l(e);if(i===s&&e._state!==I)this._settledAt(e._state,t,e._result);else if("function"!=typeof i)this._remaining--,this._result[t]=e;else if(n===b){var o=new n(u);h(o,e,i),this._willSettleAt(o,t)}else this._willSettleAt(new n(function(t){return t(e)}),t)}else this._willSettleAt(r(e),t)},o.prototype._settledAt=function(t,e,n){var r=this.promise;r._state===I&&(this._remaining--,t===P?d(r,n):this._result[e]=n),0===this._remaining&&p(r,this._result)},o.prototype._willSettleAt=function(t,e){var n=this;g(t,void 0,function(t){return n._settledAt(j,e,t)},function(t){return n._settledAt(P,e,t)})},b.all=X,b.race=Y,b.resolve=a,b.reject=K,b._setScheduler=t,b._setAsap=M,b._asap=k,b.prototype={constructor:b,then:s,catch:function(t){return this.then(null,t)}},b.polyfill=Z,b.Promise=b}()}.call(e,st(2),st(3))},function(t,e){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function i(e){if(c===setTimeout)return setTimeout(e,0);if((c===n||!c)&&setTimeout)return(c=setTimeout)(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}function o(){p&&h&&(p=!1,h.length?f=h.concat(f):d=-1,f.length&&s())}function s(){if(!p){var t=i(o);p=!0;for(var e=f.length;e;){for(h=f,f=[];++d<e;)h&&h[d].run();d=-1,e=f.length}h=null,p=!1,function(e){if(l===clearTimeout)return clearTimeout(e);if((l===r||!l)&&clearTimeout)return(l=clearTimeout)(e);try{l(e)}catch(t){try{return l.call(null,e)}catch(t){return l.call(this,e)}}}(t)}}function a(t,e){this.fun=t,this.array=e}function u(){}var c,l,t=t.exports={};try{c="function"==typeof setTimeout?setTimeout:n}catch(t){c=n}try{l="function"==typeof clearTimeout?clearTimeout:r}catch(t){l=r}var h,f=[],p=!1,d=-1;t.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];f.push(new a(t,e)),1!==f.length||p||i(s)},a.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=u,t.addListener=u,t.once=u,t.off=u,t.removeListener=u,t.removeAllListeners=u,t.emit=u,t.prependListener=u,t.prependOnceListener=u,t.listeners=function(t){return[]},t.binding=function(t){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(t){throw new Error("process.chdir is not supported")},t.umask=function(){return 0}},function(t,e){var n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e){},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=["text/plain","text/html"],i=function(){(console.warn||console.log).call(arguments)}.bind(console,"[clipboard-polyfill]"),o=!0,s=(e.suppressDTWarnings=function(){o=!1},a.prototype.setData=function(t,e){o&&-1===r.indexOf(t)&&i("Unknown data type: "+t,"Call clipboard.suppressWarnings() to suppress this warning."),this.m[t]=e},a.prototype.getData=function(t){return this.m[t]},a.prototype.forEach=function(t){for(var e in this.m)t(this.m[e],e)},a);function a(){this.m={}}e.DT=s}],i={},r.m=n,r.c=i,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=0);function r(t){var e;return(i[t]||(e=i[t]={i:t,l:!1,exports:{}},n[t].call(e.exports,e,e.exports,r),e.l=!0,e)).exports}var n,i}),!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports&&"object"==typeof module?module.exports=t(require("jquery")):t(jQuery)}(function(It,jt){"use strict";var Pt={beforeShow:e,move:e,change:e,show:e,hide:e,color:!1,flat:!1,showInput:!1,allowEmpty:!1,showButtons:!0,clickoutFiresChange:!0,showInitial:!1,showPalette:!1,showPaletteOnly:!1,hideAfterPaletteSelect:!1,togglePaletteOnly:!1,showSelectionPalette:!0,localStorageKey:!1,appendTo:"body",maxSelectionSize:7,cancelText:"cancel",chooseText:"choose",togglePaletteMoreText:"more",togglePaletteLessText:"less",clearText:"Clear Color Selection",noColorSelectedText:"No Color Selected",preferredFormat:!1,className:"",containerClassName:"",replacerClassName:"",showAlpha:!1,theme:"sp-light",palette:[["#ffffff","#000000","#ff0000","#ff8000","#ffff00","#008000","#0000ff","#4b0082","#9400d3"]],selectionPalette:[],disabled:!1,offset:null},Nt=[],Dt=!!/msie/i.exec(window.navigator.userAgent),Rt=((o=document.createElement("div").style).cssText="background-color:rgba(0,0,0,.5)",t(o.backgroundColor,"rgba")||t(o.backgroundColor,"hsla")),Ht=["<div class='sp-replacer'>","<div class='sp-preview'><div class='sp-preview-inner'></div></div>","<div class='sp-dd'>&#9660;</div>","</div>"].join(""),Bt=function(){var t="";if(Dt)for(var e=1;e<=6;e++)t+="<div class='sp-"+e+"'></div>";return["<div class='sp-container sp-hidden'>","<div class='sp-palette-container'>","<div class='sp-palette sp-thumb sp-cf'></div>","<div class='sp-palette-button-container sp-cf'>","<button type='button' class='sp-palette-toggle'></button>","</div>","</div>","<div class='sp-picker-container'>","<div class='sp-top sp-cf'>","<div class='sp-fill'></div>","<div class='sp-top-inner'>","<div class='sp-color'>","<div class='sp-sat'>","<div class='sp-val'>","<div class='sp-dragger'></div>","</div>","</div>","</div>","<div class='sp-clear sp-clear-display'>","</div>","<div class='sp-hue'>","<div class='sp-slider'></div>",t,"</div>","</div>","<div class='sp-alpha'><div class='sp-alpha-inner'><div class='sp-alpha-handle'></div></div></div>","</div>","<div class='sp-input-container sp-cf'>","<input class='sp-input formulaInputFocus' type='text' spellcheck='false'  />","</div>","<div class='sp-initial sp-thumb sp-cf'></div>","<div class='sp-button-container sp-cf'>","<a class='sp-cancel' href='#'></a>","<button type='button' class='sp-choose'></button>","</div>","</div>","</div>"].join("")}();function t(t,e){return!!~(""+t).indexOf(e)}function Mt(t,e,n,r){for(var i=[],o=0;o<t.length;o++){var s,a,u,c=t[o];c?(a=(s=tinycolor(c)).toHsl().l<.5?"sp-thumb-el sp-thumb-dark":"sp-thumb-el sp-thumb-light",a+=tinycolor.equals(e,c)?" sp-thumb-active":"",c=s.toString(r.preferredFormat||"rgb"),u=Rt?"background-color:"+s.toRgbString():"filter:"+s.toFilter(),i.push('<span title="'+c+'" data-color="'+s.toRgbString()+'" class="'+a+'"><span class="sp-thumb-inner" style="'+u+';" /></span>')):i.push(It("<div />").append(It('<span data-color="" style="background-color:transparent;" class="sp-clear-display"></span>').attr("title",r.noColorSelectedText)).html())}return"<div class='sp-cf "+n+"'>"+i.join("")+"</div>"}function p(t,e){i=t,(e=It.extend({},Pt,e)).callbacks={move:Wt(e.move,i),change:Wt(e.change,i),show:Wt(e.show,i),hide:Wt(e.hide,i),beforeShow:Wt(e.beforeShow,i)};var M,W,n,a=e,u=a.flat,L=a.showSelectionPalette,r=a.localStorageKey,i=a.theme,o=a.callbacks,F=(M=R,function(){var t=this,e=arguments;W=W||setTimeout(function(){W=null,M.apply(t,e)},10)}),c=!10,H=!1,l=0,h=0,f=0,B=0,q=0,z=0,U=0,s=0,p=0,d=0,g=1,G=[],m=[],V={},v=a.selectionPalette.slice(0),$=a.maxSelectionSize,X="sp-dragging",y=null,b=t.ownerDocument,w=(b.body,It(t)),Y=!1,_=It(Bt,b).addClass(i),K=_.find(".sp-picker-container"),Q=_.find(".sp-color"),J=_.find(".sp-dragger"),Z=_.find(".sp-hue"),tt=_.find(".sp-slider"),x=_.find(".sp-alpha-inner"),et=_.find(".sp-alpha"),nt=_.find(".sp-alpha-handle"),C=_.find(".sp-input"),rt=_.find(".sp-palette"),it=_.find(".sp-initial"),e=_.find(".sp-cancel"),t=_.find(".sp-clear"),ot=_.find(".sp-choose"),st=_.find(".sp-palette-toggle"),at=w.is("input"),ut=at&&"color"===w.attr("type")&&Ft(),ct=at&&!u,k=ct?It(Ht).addClass(i).addClass(a.className).addClass(a.replacerClassName):It([]),lt=ct?k:w,S=k.find(".sp-preview-inner"),i=a.color||at&&w.val(),ht=!1,O=a.preferredFormat,ft=!a.showButtons||a.clickoutFiresChange,E=!i,T=a.allowEmpty&&!ut;function pt(){if(a.showPaletteOnly&&(a.showPalette=!0),st.text(a.showPaletteOnly?a.togglePaletteMoreText:a.togglePaletteLessText),a.palette){G=a.palette.slice(0),m=It.isArray(G[0])?G:[G],V={};for(var t=0;t<m.length;t++)for(var e=0;e<m[t].length;e++){var n=tinycolor(m[t][e]).toRgbString();V[n]=!0}}_.toggleClass("sp-flat",u),_.toggleClass("sp-input-disabled",!a.showInput),_.toggleClass("sp-alpha-enabled",a.showAlpha),_.toggleClass("sp-clear-enabled",T),_.toggleClass("sp-buttons-disabled",!a.showButtons),_.toggleClass("sp-palette-buttons-disabled",!a.togglePaletteOnly),_.toggleClass("sp-palette-disabled",!a.showPalette),_.toggleClass("sp-palette-only",a.showPaletteOnly),_.toggleClass("sp-initial-disabled",!a.showInitial),_.addClass(a.className).addClass(a.containerClassName),R()}function dt(){if(r&&window.localStorage){try{var t=window.localStorage[r].split(",#");1<t.length&&(delete window.localStorage[r],It.each(t,function(t,e){gt(e)}))}catch(t){}try{v=window.localStorage[r].split(";")}catch(t){}}}function gt(t){if(L){var e=tinycolor(t).toRgbString();if(!V[e]&&-1===It.inArray(e,v))for(v.push(e);v.length>$;)v.shift();if(r&&window.localStorage)try{window.localStorage[r]=v.join(";")}catch(t){}}}function mt(){var n=j(),t=It.map(m,function(t,e){return Mt(t,n,"sp-palette-row sp-palette-row-"+e,a)});dt(),v&&t.push(Mt(function(){var t=[];if(a.showPalette)for(var e=0;e<v.length;e++){var n=tinycolor(v[e]).toRgbString();V[n]||t.push(v[e])}return t.reverse().slice(0,a.maxSelectionSize)}(),n,"sp-palette-row sp-palette-row-selection",a)),rt.html(t.join(""))}function vt(){var t,e;a.showInitial&&(t=ht,e=j(),it.html(Mt([t,e],e,"sp-palette-row-initial",a)))}function yt(){(h<=0||l<=0||B<=0)&&R(),H=!0,_.addClass(X),y=null,w.trigger("dragstart.spectrum",[j()])}function bt(){H=!1,_.removeClass(X),w.trigger("dragstop.spectrum",[j()])}function wt(){var t=C.val();null!==t&&""!==t||!T?(t=tinycolor(t)).isValid()?(I(t),D(!0)):C.addClass("sp-validation-error"):(I(null),D(!0))}function _t(){(c?A:xt)()}function xt(){var t=It.Event("beforeShow.spectrum");if(c)R();else if(w.trigger(t,[j()]),!1!==o.beforeShow(j())&&!t.isDefaultPrevented()){for(var e=0;e<Nt.length;e++)Nt[e]&&Nt[e].hide();c=!0,It(b).bind("keydown.spectrum",Ct),It(b).bind("click.spectrum",kt),It(window).bind("resize.spectrum",F),k.addClass("sp-active"),_.removeClass("sp-hidden"),R(),N(),ht=j(),vt(),o.show(ht),w.trigger("show.spectrum",[ht])}}function Ct(t){27===t.keyCode&&A()}function kt(t){2==t.button||H||(ft?D(!0):St(),A())}function A(){c&&!u&&(c=!1,It(b).unbind("keydown.spectrum",Ct),It(b).unbind("click.spectrum",kt),It(window).unbind("resize.spectrum",F),k.removeClass("sp-active"),_.addClass("sp-hidden"),o.hide(j()),w.trigger("hide.spectrum",[j()]))}function St(){I(ht,!0)}function I(t,e){var n;tinycolor.equals(t,j())?N():(!t&&T?E=!0:(E=!1,t=(n=tinycolor(t)).toHsv(),s=t.h%360/360,p=t.s,d=t.v,g=t.a),N(),n&&n.isValid()&&!e&&(O=a.preferredFormat||n.getFormat()))}function j(t){return t=t||{},T&&E?null:tinycolor.fromRatio({h:s,s:p,v:d,a:Math.round(100*g)/100},{format:t.format||O})}function P(){N(),o.move(j()),w.trigger("move.spectrum",[j()])}function N(){C.removeClass("sp-validation-error"),Ot();var t,e,n,r=tinycolor.fromRatio({h:s,s:1,v:1}),r=(Q.css("background-color",r.toHexString()),O),i=j({format:r=!(g<1)||0===g&&"name"===O||"hex"!==O&&"hex3"!==O&&"hex6"!==O&&"name"!==O?O:"rgb"}),o="";S.removeClass("sp-clear-display"),S.css("background-color","transparent"),!i&&T?S.addClass("sp-clear-display"):(t=i.toHexString(),e=i.toRgbString(),Rt||1===i.alpha?S.css("background-color",e):(S.css("background-color","transparent"),S.css("filter",i.toFilter())),a.showAlpha&&((e=i.toRgb()).a=0,n="linear-gradient(left, "+(e=tinycolor(e).toRgbString())+", "+t+")",Dt?x.css("filter",tinycolor(e).toFilter({gradientType:1},t)):(x.css("background","-webkit-"+n),x.css("background","-moz-"+n),x.css("background","-ms-"+n),x.css("background","linear-gradient(to right, "+e+", "+t+")"))),o=i.toString(r)),a.showInput&&C.val(o),a.showPalette&&mt(),vt()}function Ot(){var t=p,e=d;T&&E?(nt.hide(),tt.hide(),J.hide()):(nt.show(),tt.show(),J.show(),t=t*l,e=h-e*h,t=Math.max(-f,Math.min(l-f,t-f)),e=Math.max(-f,Math.min(h-f,e-f)),J.css({top:e+"px",left:t+"px"}),e=g*q,nt.css({left:e-z/2+"px"}),t=s*B,tt.css({top:t-U+"px"}))}function D(t){var e=j(),n="";e&&(n=e.toString(O),gt(e)),at&&w.val(n),t&&(o.change(e),w.trigger("change",[e]))}function R(){var t,e,n,r,i,o,s;c&&(l=Q.width(),h=Q.height(),f=J.height(),Z.width(),B=Z.height(),U=tt.height(),q=et.width(),z=nt.width(),u||(_.css("position","absolute"),a.offset?_.offset(a.offset):_.offset((t=lt,e=(s=_).outerWidth(),n=s.outerHeight(),r=t.outerHeight(),i=(o=(s=s[0].ownerDocument).documentElement).clientWidth+It(s).scrollLeft(),o=o.clientHeight+It(s).scrollTop(),(s=t.offset()).top+=r,s.left-=Math.min(s.left,s.left+e>i&&e<i?Math.abs(s.left+e-i):0),s.top-=Math.min(s.top,s.top+n>o&&n<o?Math.abs(+(n+r)):0),s))),Ot(),a.showPalette&&mt(),w.trigger("reflow.spectrum"))}function Et(){A(),Y=!0,w.attr("disabled",!0),lt.addClass("sp-disabled")}function Tt(t){return t.data&&t.data.ignore?(I(It(t.target).closest(".sp-thumb-el").data("color")),P()):(I(It(t.target).closest(".sp-thumb-el").data("color")),P(),D(!0),a.hideAfterPaletteSelect&&A()),!1}Dt&&_.find("*:not(input)").attr("unselectable","on"),pt(),ct&&w.after(k).hide(),T||t.hide(),u?w.after(_).hide():(n=1!==(n="parent"===a.appendTo?w.parent():It(a.appendTo)).length?It("body"):n).append(_),dt(),lt.bind("click.spectrum touchstart.spectrum",function(t){Y||_t(),t.stopPropagation(),It(t.target).is("input")||t.preventDefault()}),!w.is(":disabled")&&!0!==a.disabled||Et(),_.click(qt),C.change(wt),C.bind("paste",function(){setTimeout(wt,1)}),C.keydown(function(t){13==t.keyCode&&wt()}),e.text(a.cancelText),e.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),St(),A()}),t.attr("title",a.clearText),t.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),E=!0,P(),u&&D(!0)}),ot.text(a.chooseText),ot.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),Dt&&C.is(":focus")&&C.trigger("change"),C.hasClass("sp-validation-error")||(D(!0),A())}),st.text(a.showPaletteOnly?a.togglePaletteMoreText:a.togglePaletteLessText),st.bind("click.spectrum",function(t){t.stopPropagation(),t.preventDefault(),a.showPaletteOnly=!a.showPaletteOnly,a.showPaletteOnly||u||_.css("left","-="+(K.outerWidth(!0)+5)),pt()}),Lt(et,function(t,e,n){g=t/q,E=!1,n.shiftKey&&(g=Math.round(10*g)/10),P()},yt,bt),Lt(Z,function(t,e){s=parseFloat(e/B),E=!1,a.showAlpha||(g=1),P()},yt,bt),Lt(Q,function(t,e,n){n.shiftKey?y||(n=p*l,r=h-d*h,n=Math.abs(t-n)>Math.abs(e-r),y=n?"x":"y"):y=null;var r=!y||"y"===y;y&&"x"!==y||(p=parseFloat(t/l)),r&&(d=parseFloat((h-e)/h)),E=!1,a.showAlpha||(g=1),P()},yt,bt),i?(I(i),N(),O=a.preferredFormat||tinycolor(i).format,gt(i)):N(),u&&xt(),n=Dt?"mousedown.spectrum":"click.spectrum touchstart.spectrum",rt.delegate(".sp-thumb-el",n,Tt),it.delegate(".sp-thumb-el:nth-child(1)",n,{ignore:!0},Tt);var At={show:xt,hide:A,toggle:_t,reflow:R,option:function(t,e){return t===jt?It.extend({},a):e===jt?a[t]:(a[t]=e,"preferredFormat"===t&&(O=a.preferredFormat),void pt())},enable:function(){Y=!1,w.attr("disabled",!1),lt.removeClass("sp-disabled")},disable:Et,offset:function(t){a.offset=t,R()},set:function(t){I(t),D()},get:j,destroy:function(){w.show(),lt.unbind("click.spectrum touchstart.spectrum"),_.remove(),k.remove(),Nt[At.id]=null},container:_};return At.id=Nt.push(At)-1,At}function e(){}function qt(t){t.stopPropagation()}function Wt(t,e){var n=Array.prototype.slice,r=n.call(arguments,2);return function(){return t.apply(e,r.concat(n.call(arguments)))}}function Lt(r,i,e,t){i=i||function(){},e=e||function(){},t=t||function(){};var o=document,s=!1,a={},u=0,c=0,l="ontouchstart"in window,n={};function h(t){t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),t.returnValue=!1}function f(t){if(s){if(Dt&&o.documentMode<9&&!t.button)return p();var e=t.originalEvent&&t.originalEvent.touches&&t.originalEvent.touches[0],n=e&&e.pageX||t.pageX,e=e&&e.pageY||t.pageY,n=Math.max(0,Math.min(n-a.left,c)),e=Math.max(0,Math.min(e-a.top,u));l&&h(t),i.apply(r,[n,e,t])}}function p(){s&&(It(o).unbind(n),It(o.body).removeClass("sp-dragging"),setTimeout(function(){t.apply(r,arguments)},0)),s=!1}n.selectstart=h,n.dragstart=h,n["touchmove mousemove"]=f,n["touchend mouseup"]=p,It(r).bind("touchstart mousedown",function(t){(t.which?3==t.which:2==t.button)||s||!1!==e.apply(r,arguments)&&(s=!0,u=It(r).height(),c=It(r).width(),a=It(r).offset(),It(o).bind(n),It(o.body).addClass("sp-dragging"),f(t),h(t))})}function Ft(){return It.fn.spectrum.inputTypeColorSupport()}var v,y,b,w,_,x,C,n,k,r,i,o,S,s="spectrum.id";function O(t,e){var n,r,i,o,s,a,u,c,l,h,f,p,d,g;return e=e||{},(t=t||"")instanceof O?t:this instanceof O?(f={r:0,g:0,b:0},g=d=!(p=1),"object"==typeof(n="string"==typeof(n=t)?function(t){t=t.replace(v,"").replace(y,"").toLowerCase();var e,n=!1;if(k[t])t=k[t],n=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};return(e=S.rgb.exec(t))?{r:e[1],g:e[2],b:e[3]}:(e=S.rgba.exec(t))?{r:e[1],g:e[2],b:e[3],a:e[4]}:(e=S.hsl.exec(t))?{h:e[1],s:e[2],l:e[3]}:(e=S.hsla.exec(t))?{h:e[1],s:e[2],l:e[3],a:e[4]}:(e=S.hsv.exec(t))?{h:e[1],s:e[2],v:e[3]}:(e=S.hsva.exec(t))?{h:e[1],s:e[2],v:e[3],a:e[4]}:(e=S.hex8.exec(t))?{a:T(e[1])/255,r:T(e[2]),g:T(e[3]),b:T(e[4]),format:n?"name":"hex8"}:(e=S.hex6.exec(t))?{r:T(e[1]),g:T(e[2]),b:T(e[3]),format:n?"name":"hex"}:!!(e=S.hex3.exec(t))&&{r:T(e[1]+""+e[1]),g:T(e[2]+""+e[2]),b:T(e[3]+""+e[3]),format:n?"name":"hex"}}(n):n)&&(n.hasOwnProperty("r")&&n.hasOwnProperty("g")&&n.hasOwnProperty("b")?(c=n.r,l=n.g,h=n.b,f={r:255*E(c,255),g:255*E(l,255),b:255*E(h,255)},d=!0,g="%"===String(n.r).substr(-1)?"prgb":"rgb"):n.hasOwnProperty("h")&&n.hasOwnProperty("s")&&n.hasOwnProperty("v")?(n.s=A(n.s),n.v=A(n.v),c=n.h,l=n.s,h=n.v,c=6*E(c,360),l=E(l,100),h=E(h,100),s=w.floor(c),f={r:255*[h,u=h*(1-(c-=s)*l),a=h*(1-l),a,c=h*(1-(1-c)*l),h][l=s%6],g:255*[c,h,h,u,a,a][l],b:255*[a,a,c,h,h,u][l]},d=!0,g="hsv"):n.hasOwnProperty("h")&&n.hasOwnProperty("s")&&n.hasOwnProperty("l")&&(n.s=A(n.s),n.l=A(n.l),s=n.h,a=n.s,u=n.l,s=E(s,360),a=E(a,100),u=E(u,100),0===a?r=i=o=u:(r=m(a=2*u-(u=u<.5?u*(1+a):u+a-u*a),u,s+1/3),i=m(a,u,s),o=m(a,u,s-1/3)),f={r:255*r,g:255*i,b:255*o},d=!0,g="hsl"),n.hasOwnProperty("a")&&(p=n.a)),p=H(p),r={ok:d,format:n.format||g,r:x(255,C(f.r,0)),g:x(255,C(f.g,0)),b:x(255,C(f.b,0)),a:p},this._originalInput=t,this._r=r.r,this._g=r.g,this._b=r.b,this._a=r.a,this._roundA=_(100*this._a)/100,this._format=e.format||r.format,this._gradientType=e.gradientType,this._r<1&&(this._r=_(this._r)),this._g<1&&(this._g=_(this._g)),this._b<1&&(this._b=_(this._b)),this._ok=r.ok,void(this._tc_id=b++)):new O(t,e);function m(t,e,n){return n<0&&(n+=1),1<n&&--n,n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}}function a(t,e,n){t=E(t,255),e=E(e,255),n=E(n,255);var r,i=C(t,e,n),o=x(t,e,n),s=(i+o)/2;if(i==o)r=u=0;else{var a=i-o,u=.5<s?a/(2-i-o):a/(i+o);switch(i){case t:r=(e-n)/a+(e<n?6:0);break;case e:r=(n-t)/a+2;break;case n:r=(t-e)/a+4}r/=6}return{h:r,s:u,l:s}}function u(t,e,n){t=E(t,255),e=E(e,255),n=E(n,255);var r,i=C(t,e,n),o=x(t,e,n),s=i,a=i-o,u=0===i?0:a/i;if(i==o)r=0;else{switch(i){case t:r=(e-n)/a+(e<n?6:0);break;case e:r=(n-t)/a+2;break;case n:r=(t-e)/a+4}r/=6}return{h:r,s:u,v:s}}function c(t,e,n,r){t=[f(_(t).toString(16)),f(_(e).toString(16)),f(_(n).toString(16))];return r&&t[0].charAt(0)==t[0].charAt(1)&&t[1].charAt(0)==t[1].charAt(1)&&t[2].charAt(0)==t[2].charAt(1)?t[0].charAt(0)+t[1].charAt(0)+t[2].charAt(0):t.join("")}function l(t,e,n,r){return[f(Math.round(255*parseFloat(r)).toString(16)),f(_(t).toString(16)),f(_(e).toString(16)),f(_(n).toString(16))].join("")}function d(t,e){e=0===e?0:e||10;t=O(t).toHsl();return t.s-=e/100,t.s=h(t.s),O(t)}function g(t,e){e=0===e?0:e||10;t=O(t).toHsl();return t.s+=e/100,t.s=h(t.s),O(t)}function m(t){return O(t).desaturate(100)}function I(t,e){e=0===e?0:e||10;t=O(t).toHsl();return t.l+=e/100,t.l=h(t.l),O(t)}function j(t,e){e=0===e?0:e||10;t=O(t).toRgb();return t.r=C(0,x(255,t.r-_(-e/100*255))),t.g=C(0,x(255,t.g-_(-e/100*255))),t.b=C(0,x(255,t.b-_(-e/100*255))),O(t)}function P(t,e){e=0===e?0:e||10;t=O(t).toHsl();return t.l-=e/100,t.l=h(t.l),O(t)}function N(t,e){t=O(t).toHsl(),e=(_(t.h)+e)%360;return t.h=e<0?360+e:e,O(t)}function D(t){t=O(t).toHsl();return t.h=(t.h+180)%360,O(t)}function R(t){var e=O(t).toHsl(),n=e.h;return[O(t),O({h:(n+120)%360,s:e.s,l:e.l}),O({h:(n+240)%360,s:e.s,l:e.l})]}function M(t){var e=O(t).toHsl(),n=e.h;return[O(t),O({h:(n+90)%360,s:e.s,l:e.l}),O({h:(n+180)%360,s:e.s,l:e.l}),O({h:(n+270)%360,s:e.s,l:e.l})]}function W(t){var e=O(t).toHsl(),n=e.h;return[O(t),O({h:(n+72)%360,s:e.s,l:e.l}),O({h:(n+216)%360,s:e.s,l:e.l})]}function L(t,e,n){e=e||6,n=n||30;var r=O(t).toHsl(),i=360/n,o=[O(t)];for(r.h=(r.h-(i*e>>1)+720)%360;--e;)r.h=(r.h+i)%360,o.push(O(r));return o}function F(t,e){e=e||6;for(var t=O(t).toHsv(),n=t.h,r=t.s,i=t.v,o=[],s=1/e;e--;)o.push(O({h:n,s:r,v:i})),i=(i+s)%1;return o}function H(t){return t=parseFloat(t),t=isNaN(t)||t<0||1<t?1:t}function E(t,e){var n="string"==typeof(t="string"==typeof(n=t)&&-1!=n.indexOf(".")&&1===parseFloat(n)?"100%":t)&&-1!=t.indexOf("%");return t=x(e,C(0,parseFloat(t))),n&&(t=parseInt(t*e,10)/100),w.abs(t-e)<1e-6?1:t%e/parseFloat(e)}function h(t){return x(1,C(0,t))}function T(t){return parseInt(t,16)}function f(t){return 1==t.length?"0"+t:""+t}function A(t){return t=t<=1?100*t+"%":t}It.fn.spectrum=function(n,t){var r,i;return"string"==typeof n?(r=this,i=Array.prototype.slice.call(arguments,1),this.each(function(){var t=Nt[It(this).data(s)];if(t){var e=t[n];if(!e)throw new Error("Spectrum: no such method: '"+n+"'");"get"==n?r=t.get():"container"==n?r=t.container:"option"==n?r=t.option.apply(t,i):"destroy"==n?(t.destroy(),It(this).removeData(s)):e.apply(t,i)}}),r):this.spectrum("destroy").each(function(){var t=p(this,It.extend({},n,It(this).data()));It(this).data(s,t.id)})},It.fn.spectrum.load=!0,It.fn.spectrum.loadOpts={},It.fn.spectrum.draggable=Lt,It.fn.spectrum.defaults=Pt,It.fn.spectrum.inputTypeColorSupport=function t(){var e;return void 0===t._cachedResult&&(e=It("<input type='color'/>")[0],t._cachedResult="color"===e.type&&""!==e.value),t._cachedResult},It.spectrum={},It.spectrum.localization={},It.spectrum.palettes={},It.fn.spectrum.processNativeColorInputs=function(){var t=It("input[type=color]");t.length&&!Ft()&&t.spectrum({preferredFormat:"hex6"})},v=/^[\s,#]+/,y=/\s+$/,b=0,w=Math,_=w.round,x=w.min,C=w.max,n=w.random,O.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},setAlpha:function(t){return this._a=H(t),this._roundA=_(100*this._a)/100,this},toHsv:function(){var t=u(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=u(this._r,this._g,this._b),e=_(360*t.h),n=_(100*t.s),t=_(100*t.v);return 1==this._a?"hsv("+e+", "+n+"%, "+t+"%)":"hsva("+e+", "+n+"%, "+t+"%, "+this._roundA+")"},toHsl:function(){var t=a(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=a(this._r,this._g,this._b),e=_(360*t.h),n=_(100*t.s),t=_(100*t.l);return 1==this._a?"hsl("+e+", "+n+"%, "+t+"%)":"hsla("+e+", "+n+"%, "+t+"%, "+this._roundA+")"},toHex:function(t){return c(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(){return l(this._r,this._g,this._b,this._a)},toHex8String:function(){return"#"+this.toHex8()},toRgb:function(){return{r:_(this._r),g:_(this._g),b:_(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+_(this._r)+", "+_(this._g)+", "+_(this._b)+")":"rgba("+_(this._r)+", "+_(this._g)+", "+_(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:_(100*E(this._r,255))+"%",g:_(100*E(this._g,255))+"%",b:_(100*E(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+_(100*E(this._r,255))+"%, "+_(100*E(this._g,255))+"%, "+_(100*E(this._b,255))+"%)":"rgba("+_(100*E(this._r,255))+"%, "+_(100*E(this._g,255))+"%, "+_(100*E(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(r[c(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+l(this._r,this._g,this._b,this._a),n=e;return"progid:DXImageTransform.Microsoft.gradient("+(this._gradientType?"GradientType = 1, ":"")+"startColorstr="+e+",endColorstr="+(n=t?O(t).toHex8String():n)+")"},toString:function(t){var e=!!t,n=(t=t||this._format,!1),r=this._a<1&&0<=this._a;return e||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"name"!==t?("rgb"===t&&(n=this.toRgbString()),"prgb"===t&&(n=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(n=this.toHexString()),"hex3"===t&&(n=this.toHexString(!0)),"hex8"===t&&(n=this.toHex8String()),"name"===t&&(n=this.toName()),"hsl"===t&&(n=this.toHslString()),(n="hsv"===t?this.toHsvString():n)||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},_applyModification:function(t,e){t=t.apply(null,[this].concat([].slice.call(e)));return this._r=t._r,this._g=t._g,this._b=t._b,this.setAlpha(t._a),this},lighten:function(){return this._applyModification(I,arguments)},brighten:function(){return this._applyModification(j,arguments)},darken:function(){return this._applyModification(P,arguments)},desaturate:function(){return this._applyModification(d,arguments)},saturate:function(){return this._applyModification(g,arguments)},greyscale:function(){return this._applyModification(m,arguments)},spin:function(){return this._applyModification(N,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(L,arguments)},complement:function(){return this._applyCombination(D,arguments)},monochromatic:function(){return this._applyCombination(F,arguments)},splitcomplement:function(){return this._applyCombination(W,arguments)},triad:function(){return this._applyCombination(R,arguments)},tetrad:function(){return this._applyCombination(M,arguments)}},O.fromRatio=function(t,e){if("object"==typeof t){var n,r={};for(n in t)t.hasOwnProperty(n)&&(r[n]="a"===n?t[n]:A(t[n]));t=r}return O(t,e)},O.equals=function(t,e){return!(!t||!e)&&O(t).toRgbString()==O(e).toRgbString()},O.random=function(){return O.fromRatio({r:n(),g:n(),b:n()})},O.mix=function(t,e,n){n=0===n?0:n||50;var t=O(t).toRgb(),e=O(e).toRgb(),n=n/100,r=2*n-1,i=e.a-t.a,i=1-(r=(1+(r*i==-1?r:(r+i)/(1+r*i)))/2),r={r:e.r*r+t.r*i,g:e.g*r+t.g*i,b:e.b*r+t.b*i,a:e.a*n+t.a*(1-n)};return O(r)},O.readability=function(t,e){var t=O(t),e=O(e),n=t.toRgb(),r=e.toRgb(),t=t.getBrightness(),e=e.getBrightness(),n=Math.max(n.r,r.r)-Math.min(n.r,r.r)+Math.max(n.g,r.g)-Math.min(n.g,r.g)+Math.max(n.b,r.b)-Math.min(n.b,r.b);return{brightness:Math.abs(t-e),color:n}},O.isReadable=function(t,e){t=O.readability(t,e);return 125<t.brightness&&500<t.color},O.mostReadable=function(t,e){for(var n=null,r=0,i=!1,o=0;o<e.length;o++){var s=O.readability(t,e[o]),a=125<s.brightness&&500<s.color,s=s.brightness/125*3+s.color/500;(a&&!i||a&&i&&r<s||!a&&!i&&r<s)&&(i=a,r=s,n=O(e[o]))}return n},k=O.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},r=O.hexNames=function(t){var e,n={};for(e in t)t.hasOwnProperty(e)&&(n[t[e]]=e);return n}(k),i="[\\s|\\(]+("+(o="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+o+")[,|\\s]+("+o+")\\s*\\)?",o="[\\s|\\(]+("+o+")[,|\\s]+("+o+")[,|\\s]+("+o+")[,|\\s]+("+o+")\\s*\\)?",S={rgb:new RegExp("rgb"+i),rgba:new RegExp("rgba"+o),hsl:new RegExp("hsl"+i),hsla:new RegExp("hsla"+o),hsv:new RegExp("hsv"+i),hsva:new RegExp("hsva"+o),hex3:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex8:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/},window.tinycolor=O,It(function(){It.fn.spectrum.load&&It.fn.spectrum.processNativeColorInputs()})}),!function(){var t=function(x){x.ui=x.ui||{},x.ui.version="1.12.1";var i,r,C,k,o,s,a,u,c,n,l=0,h=Array.prototype.slice;function S(t,e,n){return[parseFloat(t[0])*(c.test(t[0])?e/100:1),parseFloat(t[1])*(c.test(t[1])?n/100:1)]}function O(t,e){return parseInt(x.css(t,e),10)||0}x.cleanData=(i=x.cleanData,function(t){for(var e,n,r=0;null!=(n=t[r]);r++)try{(e=x._data(n,"events"))&&e.remove&&x(n).triggerHandler("remove")}catch(t){}i(t)}),x.widget=function(t,n,e){var r,i,o,s={},a=t.split(".")[0],u=a+"-"+(t=t.split(".")[1]);return e||(e=n,n=x.Widget),x.isArray(e)&&(e=x.extend.apply(null,[{}].concat(e))),x.expr[":"][u.toLowerCase()]=function(t){return!!x.data(t,u)},x[a]=x[a]||{},r=x[a][t],i=x[a][t]=function(t,e){if(!this._createWidget)return new i(t,e);arguments.length&&this._createWidget(t,e)},x.extend(i,r,{version:e.version,_proto:x.extend({},e),_childConstructors:[]}),(o=new n).options=x.widget.extend({},o.options),x.each(e,function(e,r){function i(){return n.prototype[e].apply(this,arguments)}function o(t){return n.prototype[e].apply(this,t)}x.isFunction(r)?s[e]=function(){var t,e=this._super,n=this._superApply;return this._super=i,this._superApply=o,t=r.apply(this,arguments),this._super=e,this._superApply=n,t}:s[e]=r}),i.prototype=x.widget.extend(o,{widgetEventPrefix:r&&o.widgetEventPrefix||t},s,{constructor:i,namespace:a,widgetName:t,widgetFullName:u}),r?(x.each(r._childConstructors,function(t,e){var n=e.prototype;x.widget(n.namespace+"."+n.widgetName,i,e._proto)}),delete r._childConstructors):n._childConstructors.push(i),x.widget.bridge(t,i),i},x.widget.extend=function(t){for(var e,n,r=h.call(arguments,1),i=0,o=r.length;i<o;i++)for(e in r[i])n=r[i][e],r[i].hasOwnProperty(e)&&void 0!==n&&(x.isPlainObject(n)?t[e]=x.isPlainObject(t[e])?x.widget.extend({},t[e],n):x.widget.extend({},n):t[e]=n);return t},x.widget.bridge=function(o,e){var s=e.prototype.widgetFullName||o;x.fn[o]=function(n){var t="string"==typeof n,r=h.call(arguments,1),i=this;return t?this.length||"instance"!==n?this.each(function(){var t,e=x.data(this,s);return"instance"===n?(i=e,!1):e?x.isFunction(e[n])&&"_"!==n.charAt(0)?(t=e[n].apply(e,r))!==e&&void 0!==t?(i=t&&t.jquery?i.pushStack(t.get()):t,!1):void 0:x.error("no such method '"+n+"' for "+o+" widget instance"):x.error("cannot call methods on "+o+" prior to initialization; attempted to call method '"+n+"'")}):i=void 0:(r.length&&(n=x.widget.extend.apply(null,[n].concat(r))),this.each(function(){var t=x.data(this,s);t?(t.option(n||{}),t._init&&t._init()):x.data(this,s,new e(n,this))})),i}},x.Widget=function(){},x.Widget._childConstructors=[],x.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(t,e){e=x(e||this.defaultElement||this)[0],this.element=x(e),this.uuid=l++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=x(),this.hoverable=x(),this.focusable=x(),this.classesElementLookup={},e!==this&&(x.data(e,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===e&&this.destroy()}}),this.document=x(e.style?e.ownerDocument:e.document||e),this.window=x(this.document[0].defaultView||this.document[0].parentWindow)),this.options=x.widget.extend({},this.options,this._getCreateOptions(),t),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:x.noop,_create:x.noop,_init:x.noop,destroy:function(){var n=this;this._destroy(),x.each(this.classesElementLookup,function(t,e){n._removeClass(e,t)}),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:x.noop,widget:function(){return this.element},option:function(t,e){var n,r,i,o=t;if(0===arguments.length)return x.widget.extend({},this.options);if("string"==typeof t)if(o={},t=(n=t.split(".")).shift(),n.length){for(r=o[t]=x.widget.extend({},this.options[t]),i=0;i<n.length-1;i++)r[n[i]]=r[n[i]]||{},r=r[n[i]];if(t=n.pop(),1===arguments.length)return void 0===r[t]?null:r[t];r[t]=e}else{if(1===arguments.length)return void 0===this.options[t]?null:this.options[t];o[t]=e}return this._setOptions(o),this},_setOptions:function(t){for(var e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return"classes"===t&&this._setOptionClasses(e),this.options[t]=e,"disabled"===t&&this._setOptionDisabled(e),this},_setOptionClasses:function(t){var e,n,r;for(e in t)r=this.classesElementLookup[e],t[e]!==this.options.classes[e]&&r&&r.length&&(n=x(r.get()),this._removeClass(r,e),n.addClass(this._classes({element:n,keys:e,classes:t,add:!0})))},_setOptionDisabled:function(t){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!t),t&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(i){var o=[],s=this;function t(t,e){for(var n,r=0;r<t.length;r++)n=s.classesElementLookup[t[r]]||x(),n=i.add?x(x.unique(n.get().concat(i.element.get()))):x(n.not(i.element).get()),s.classesElementLookup[t[r]]=n,o.push(t[r]),e&&i.classes[t[r]]&&o.push(i.classes[t[r]])}return i=x.extend({element:this.element,classes:this.options.classes||{}},i),this._on(i.element,{remove:"_untrackClassesElement"}),i.keys&&t(i.keys.match(/\S+/g)||[],!0),i.extra&&t(i.extra.match(/\S+/g)||[]),o.join(" ")},_untrackClassesElement:function(n){var r=this;x.each(r.classesElementLookup,function(t,e){-1!==x.inArray(n.target,e)&&(r.classesElementLookup[t]=x(e.not(n.target).get()))})},_removeClass:function(t,e,n){return this._toggleClass(t,e,n,!1)},_addClass:function(t,e,n){return this._toggleClass(t,e,n,!0)},_toggleClass:function(t,e,n,r){var i="string"==typeof t||null===t,e={extra:i?e:n,keys:i?t:e,element:i?this.element:t,add:r="boolean"==typeof r?r:n};return e.element.toggleClass(this._classes(e),r),this},_on:function(i,o,t){var s,a=this;"boolean"!=typeof i&&(t=o,o=i,i=!1),t?(o=s=x(o),this.bindings=this.bindings.add(o)):(t=o,o=this.element,s=this.widget()),x.each(t,function(t,e){function n(){if(i||!0!==a.options.disabled&&!x(this).hasClass("ui-state-disabled"))return("string"==typeof e?a[e]:e).apply(a,arguments)}"string"!=typeof e&&(n.guid=e.guid=e.guid||n.guid||x.guid++);var t=t.match(/^([\w:-]*)\s*(.*)$/),r=t[1]+a.eventNamespace,t=t[2];t?s.on(r,t,n):o.on(r,n)})},_off:function(t,e){e=(e||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,t.off(e).off(e),this.bindings=x(this.bindings.not(t).get()),this.focusable=x(this.focusable.not(t).get()),this.hoverable=x(this.hoverable.not(t).get())},_delay:function(t,e){var n=this;return setTimeout(function(){return("string"==typeof t?n[t]:t).apply(n,arguments)},e||0)},_hoverable:function(t){this.hoverable=this.hoverable.add(t),this._on(t,{mouseenter:function(t){this._addClass(x(t.currentTarget),null,"ui-state-hover")},mouseleave:function(t){this._removeClass(x(t.currentTarget),null,"ui-state-hover")}})},_focusable:function(t){this.focusable=this.focusable.add(t),this._on(t,{focusin:function(t){this._addClass(x(t.currentTarget),null,"ui-state-focus")},focusout:function(t){this._removeClass(x(t.currentTarget),null,"ui-state-focus")}})},_trigger:function(t,e,n){var r,i,o=this.options[t];if(n=n||{},(e=x.Event(e)).type=(t===this.widgetEventPrefix?t:this.widgetEventPrefix+t).toLowerCase(),e.target=this.element[0],i=e.originalEvent)for(r in i)r in e||(e[r]=i[r]);return this.element.trigger(e,n),!(x.isFunction(o)&&!1===o.apply(this.element[0],[e].concat(n))||e.isDefaultPrevented())}},x.each({show:"fadeIn",hide:"fadeOut"},function(o,s){x.Widget.prototype["_"+o]=function(e,t,n){var r,i=(t="string"==typeof t?{effect:t}:t)?!0!==t&&"number"!=typeof t&&t.effect||s:o;"number"==typeof(t=t||{})&&(t={duration:t}),r=!x.isEmptyObject(t),t.complete=n,t.delay&&e.delay(t.delay),r&&x.effects&&x.effects.effect[i]?e[o](t):i!==o&&e[i]?e[i](t.duration,t.easing,n):e.queue(function(t){x(this)[o](),n&&n.call(e[0]),t()})}}),x.widget,C=Math.max,k=Math.abs,o=/left|center|right/,s=/top|center|bottom/,a=/[\+\-]\d+(\.[\d]+)?%?/,u=/^\w+/,c=/%$/,n=x.fn.position,x.position={scrollbarWidth:function(){var t,e,n;return void 0!==r?r:(n=(e=x("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>")).children()[0],x("body").append(e),t=n.offsetWidth,e.css("overflow","scroll"),t===(n=n.offsetWidth)&&(n=e[0].clientWidth),e.remove(),r=t-n)},getScrollInfo:function(t){var e=t.isWindow||t.isDocument?"":t.element.css("overflow-x"),n=t.isWindow||t.isDocument?"":t.element.css("overflow-y"),e="scroll"===e||"auto"===e&&t.width<t.element[0].scrollWidth;return{width:"scroll"===n||"auto"===n&&t.height<t.element[0].scrollHeight?x.position.scrollbarWidth():0,height:e?x.position.scrollbarWidth():0}},getWithinInfo:function(t){var e=x(t||window),n=x.isWindow(e[0]),r=!!e[0]&&9===e[0].nodeType;return{element:e,isWindow:n,isDocument:r,offset:n||r?{left:0,top:0}:x(t).offset(),scrollLeft:e.scrollLeft(),scrollTop:e.scrollTop(),width:e.outerWidth(),height:e.outerHeight()}}},x.fn.position=function(h){if(!h||!h.of)return n.apply(this,arguments);h=x.extend({},h);var f,p,d,g,m,t,v=x(h.of),y=x.position.getWithinInfo(h.within),b=x.position.getScrollInfo(y),w=(h.collision||"flip").split(" "),_={},e=9===(e=(t=v)[0]).nodeType?{width:t.width(),height:t.height(),offset:{top:0,left:0}}:x.isWindow(e)?{width:t.width(),height:t.height(),offset:{top:t.scrollTop(),left:t.scrollLeft()}}:e.preventDefault?{width:0,height:0,offset:{top:e.pageY,left:e.pageX}}:{width:t.outerWidth(),height:t.outerHeight(),offset:t.offset()};return v[0].preventDefault&&(h.at="left top"),p=e.width,d=e.height,m=x.extend({},g=e.offset),x.each(["my","at"],function(){var t,e,n=(h[this]||"").split(" ");(n=1===n.length?o.test(n[0])?n.concat(["center"]):s.test(n[0])?["center"].concat(n):["center","center"]:n)[0]=o.test(n[0])?n[0]:"center",n[1]=s.test(n[1])?n[1]:"center",t=a.exec(n[0]),e=a.exec(n[1]),_[this]=[t?t[0]:0,e?e[0]:0],h[this]=[u.exec(n[0])[0],u.exec(n[1])[0]]}),1===w.length&&(w[1]=w[0]),"right"===h.at[0]?m.left+=p:"center"===h.at[0]&&(m.left+=p/2),"bottom"===h.at[1]?m.top+=d:"center"===h.at[1]&&(m.top+=d/2),f=S(_.at,p,d),m.left+=f[0],m.top+=f[1],this.each(function(){var n,t,s=x(this),a=s.outerWidth(),u=s.outerHeight(),e=O(this,"marginLeft"),r=O(this,"marginTop"),i=a+e+O(this,"marginRight")+b.width,o=u+r+O(this,"marginBottom")+b.height,c=x.extend({},m),l=S(_.my,s.outerWidth(),s.outerHeight());"right"===h.my[0]?c.left-=a:"center"===h.my[0]&&(c.left-=a/2),"bottom"===h.my[1]?c.top-=u:"center"===h.my[1]&&(c.top-=u/2),c.left+=l[0],c.top+=l[1],n={marginLeft:e,marginTop:r},x.each(["left","top"],function(t,e){x.ui.position[w[t]]&&x.ui.position[w[t]][e](c,{targetWidth:p,targetHeight:d,elemWidth:a,elemHeight:u,collisionPosition:n,collisionWidth:i,collisionHeight:o,offset:[f[0]+l[0],f[1]+l[1]],my:h.my,at:h.at,within:y,elem:s})}),h.using&&(t=function(t){var e=g.left-c.left,n=e+p-a,r=g.top-c.top,i=r+d-u,o={target:{element:v,left:g.left,top:g.top,width:p,height:d},element:{element:s,left:c.left,top:c.top,width:a,height:u},horizontal:n<0?"left":0<e?"right":"center",vertical:i<0?"top":0<r?"bottom":"middle"};p<a&&k(e+n)<p&&(o.horizontal="center"),d<u&&k(r+i)<d&&(o.vertical="middle"),C(k(e),k(n))>C(k(r),k(i))?o.important="horizontal":o.important="vertical",h.using.call(this,t,o)}),s.offset(x.extend(c,{using:t}))})},x.ui.position={fit:{left:function(t,e){var n,r=e.within,i=r.isWindow?r.scrollLeft:r.offset.left,r=r.width,o=t.left-e.collisionPosition.marginLeft,s=i-o,a=o+e.collisionWidth-r-i;e.collisionWidth>r?0<s&&a<=0?(n=t.left+s+e.collisionWidth-r-i,t.left+=s-n):t.left=!(0<a&&s<=0)&&a<s?i+r-e.collisionWidth:i:0<s?t.left+=s:0<a?t.left-=a:t.left=C(t.left-o,t.left)},top:function(t,e){var n,r=e.within,r=r.isWindow?r.scrollTop:r.offset.top,i=e.within.height,o=t.top-e.collisionPosition.marginTop,s=r-o,a=o+e.collisionHeight-i-r;e.collisionHeight>i?0<s&&a<=0?(n=t.top+s+e.collisionHeight-i-r,t.top+=s-n):t.top=!(0<a&&s<=0)&&a<s?r+i-e.collisionHeight:r:0<s?t.top+=s:0<a?t.top-=a:t.top=C(t.top-o,t.top)}},flip:{left:function(t,e){var n=e.within,r=n.offset.left+n.scrollLeft,i=n.width,n=n.isWindow?n.scrollLeft:n.offset.left,o=t.left-e.collisionPosition.marginLeft,s=o-n,o=o+e.collisionWidth-i-n,a="left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0,u="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,c=-2*e.offset[0];s<0?((i=t.left+a+u+c+e.collisionWidth-i-r)<0||i<k(s))&&(t.left+=a+u+c):0<o&&(0<(r=t.left-e.collisionPosition.marginLeft+a+u+c-n)||k(r)<o)&&(t.left+=a+u+c)},top:function(t,e){var n=e.within,r=n.offset.top+n.scrollTop,i=n.height,n=n.isWindow?n.scrollTop:n.offset.top,o=t.top-e.collisionPosition.marginTop,s=o-n,o=o+e.collisionHeight-i-n,a="top"===e.my[1]?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0,u="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,c=-2*e.offset[1];s<0?((i=t.top+a+u+c+e.collisionHeight-i-r)<0||i<k(s))&&(t.top+=a+u+c):0<o&&(0<(r=t.top-e.collisionPosition.marginTop+a+u+c-n)||k(r)<o)&&(t.top+=a+u+c)}},flipfit:{left:function(){x.ui.position.flip.left.apply(this,arguments),x.ui.position.fit.left.apply(this,arguments)},top:function(){x.ui.position.flip.top.apply(this,arguments),x.ui.position.fit.top.apply(this,arguments)}}},x.ui.position,x.fn.form=function(){return"string"==typeof this[0].form?this.closest("form"):x(this[0].form)},x.ui.formResetMixin={_formResetHandler:function(){var e=x(this);setTimeout(function(){var t=e.data("ui-form-reset-instances");x.each(t,function(){this.refresh()})})},_bindFormResetHandler:function(){var t;this.form=this.element.form(),this.form.length&&((t=this.form.data("ui-form-reset-instances")||[]).length||this.form.on("reset.ui-form-reset",this._formResetHandler),t.push(this),this.form.data("ui-form-reset-instances",t))},_unbindFormResetHandler:function(){var t;this.form.length&&((t=this.form.data("ui-form-reset-instances")).splice(x.inArray(this,t),1),t.length?this.form.data("ui-form-reset-instances",t):this.form.removeData("ui-form-reset-instances").off("reset.ui-form-reset"))}},x.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},x.ui.escapeSelector=(e=/([!"#$%&'()*+,./:;<=>?@[\]^`{|}~])/g,function(t){return t.replace(e,"\\$1")}),x.fn.labels=function(){var t,e,n;return this[0].labels&&this[0].labels.length?this.pushStack(this[0].labels):(e=this.eq(0).parents("label"),(t=this.attr("id"))&&(n=(n=this.eq(0).parents().last()).add((n.length?n:this).siblings()),t="label[for='"+x.ui.escapeSelector(t)+"']",e=e.add(n.find(t).addBack(t))),this.pushStack(e))},x.fn.extend({uniqueId:(t=0,function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++t)})}),removeUniqueId:function(){return this.each(function(){/^ui-id-\d+$/.test(this.id)&&x(this).removeAttr("id")})}});var e,t,f,p=/ui-corner-([a-z]){2,6}/g,d=(x.widget("ui.controlgroup",{version:"1.12.1",defaultElement:"<div>",options:{direction:"horizontal",disabled:null,onlyVisible:!0,items:{button:"input[type=button], input[type=submit], input[type=reset], button, a",controlgroupLabel:".ui-controlgroup-label",checkboxradio:"input[type='checkbox'], input[type='radio']",selectmenu:"select",spinner:".ui-spinner-input"}},_create:function(){this._enhance()},_enhance:function(){this.element.attr("role","toolbar"),this.refresh()},_destroy:function(){this._callChildMethod("destroy"),this.childWidgets.removeData("ui-controlgroup-data"),this.element.removeAttr("role"),this.options.items.controlgroupLabel&&this.element.find(this.options.items.controlgroupLabel).find(".ui-controlgroup-label-contents").contents().unwrap()},_initWidgets:function(){var o=this,s=[];x.each(this.options.items,function(r,t){var e,i;if(t)return"controlgroupLabel"===r?((e=o.element.find(t)).each(function(){var t=x(this);t.children(".ui-controlgroup-label-contents").length||t.contents().wrapAll("<span class='ui-controlgroup-label-contents'></span>")}),o._addClass(e,null,"ui-widget ui-widget-content ui-state-default"),void(s=s.concat(e.get()))):void(x.fn[r]&&(i=o["_"+r+"Options"]?o["_"+r+"Options"]("middle"):{classes:{}},o.element.find(t).each(function(){var t=x(this),e=t[r]("instance"),n=x.widget.extend({},i);"button"===r&&t.parent(".ui-spinner").length||((e=e||t[r]()[r]("instance"))&&(n.classes=o._resolveClassesValues(n.classes,e)),t[r](n),n=t[r]("widget"),x.data(n[0],"ui-controlgroup-data",e||t[r]("instance")),s.push(n[0]))})))}),this.childWidgets=x(x.unique(s)),this._addClass(this.childWidgets,"ui-controlgroup-item")},_callChildMethod:function(e){this.childWidgets.each(function(){var t=x(this).data("ui-controlgroup-data");t&&t[e]&&t[e]()})},_updateCornerClass:function(t,e){e=this._buildSimpleOptions(e,"label").classes.label;this._removeClass(t,null,"ui-corner-top ui-corner-bottom ui-corner-left ui-corner-right ui-corner-all"),this._addClass(t,null,e)},_buildSimpleOptions:function(t,e){var n="vertical"===this.options.direction,r={classes:{}};return r.classes[e]={middle:"",first:"ui-corner-"+(n?"top":"left"),last:"ui-corner-"+(n?"bottom":"right"),only:"ui-corner-all"}[t],r},_spinnerOptions:function(t){t=this._buildSimpleOptions(t,"ui-spinner");return t.classes["ui-spinner-up"]="",t.classes["ui-spinner-down"]="",t},_buttonOptions:function(t){return this._buildSimpleOptions(t,"ui-button")},_checkboxradioOptions:function(t){return this._buildSimpleOptions(t,"ui-checkboxradio-label")},_selectmenuOptions:function(t){var e="vertical"===this.options.direction;return{width:!!e&&"auto",classes:{middle:{"ui-selectmenu-button-open":"","ui-selectmenu-button-closed":""},first:{"ui-selectmenu-button-open":"ui-corner-"+(e?"top":"tl"),"ui-selectmenu-button-closed":"ui-corner-"+(e?"top":"left")},last:{"ui-selectmenu-button-open":e?"":"ui-corner-tr","ui-selectmenu-button-closed":"ui-corner-"+(e?"bottom":"right")},only:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"}}[t]}},_resolveClassesValues:function(n,r){var i={};return x.each(n,function(t){var e=r.options.classes[t]||"",e=x.trim(e.replace(p,""));i[t]=(e+" "+n[t]).replace(/\s+/g," ")}),i},_setOption:function(t,e){"direction"===t&&this._removeClass("ui-controlgroup-"+this.options.direction),this._super(t,e),"disabled"!==t?this.refresh():this._callChildMethod(e?"disable":"enable")},refresh:function(){var i,o=this;this._addClass("ui-controlgroup ui-controlgroup-"+this.options.direction),"horizontal"===this.options.direction&&this._addClass(null,"ui-helper-clearfix"),this._initWidgets(),i=this.childWidgets,(i=this.options.onlyVisible?i.filter(":visible"):i).length&&(x.each(["first","last"],function(t,e){var n,r=i[e]().data("ui-controlgroup-data");r&&o["_"+r.widgetName+"Options"]?((n=o["_"+r.widgetName+"Options"](1===i.length?"only":e)).classes=o._resolveClassesValues(n.classes,r),r.element[r.widgetName](n)):o._updateCornerClass(i[e](),e)}),this._callChildMethod("refresh"))}}),x.widget("ui.checkboxradio",[x.ui.formResetMixin,{version:"1.12.1",options:{disabled:null,label:null,icon:!0,classes:{"ui-checkboxradio-label":"ui-corner-all","ui-checkboxradio-icon":"ui-corner-all"}},_getCreateOptions:function(){var t,e=this,n=this._super()||{};return this._readType(),t=this.element.labels(),this.label=x(t[t.length-1]),this.label.length||x.error("No label found for checkboxradio widget"),this.originalLabel="",this.label.contents().not(this.element[0]).each(function(){e.originalLabel+=3===this.nodeType?x(this).text():this.outerHTML}),this.originalLabel&&(n.label=this.originalLabel),null!=(t=this.element[0].disabled)&&(n.disabled=t),n},_create:function(){var t=this.element[0].checked;this._bindFormResetHandler(),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled),this._setOption("disabled",this.options.disabled),this._addClass("ui-checkboxradio","ui-helper-hidden-accessible"),this._addClass(this.label,"ui-checkboxradio-label","ui-button ui-widget"),"radio"===this.type&&this._addClass(this.label,"ui-checkboxradio-radio-label"),this.options.label&&this.options.label!==this.originalLabel?this._updateLabel():this.originalLabel&&(this.options.label=this.originalLabel),this._enhance(),t&&(this._addClass(this.label,"ui-checkboxradio-checked","ui-state-active"),this.icon&&this._addClass(this.icon,null,"ui-state-hover")),this._on({change:"_toggleClasses",focus:function(){this._addClass(this.label,null,"ui-state-focus ui-visual-focus")},blur:function(){this._removeClass(this.label,null,"ui-state-focus ui-visual-focus")}})},_readType:function(){var t=this.element[0].nodeName.toLowerCase();this.type=this.element[0].type,"input"===t&&/radio|checkbox/.test(this.type)||x.error("Can't create checkboxradio on element.nodeName="+t+" and element.type="+this.type)},_enhance:function(){this._updateIcon(this.element[0].checked)},widget:function(){return this.label},_getRadioGroup:function(){var t=this.element[0].name,e="input[name='"+x.ui.escapeSelector(t)+"']";return t?(this.form.length?x(this.form[0].elements).filter(e):x(e).filter(function(){return 0===x(this).form().length})).not(this.element):x([])},_toggleClasses:function(){var t=this.element[0].checked;this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",t),this.options.icon&&"checkbox"===this.type&&this._toggleClass(this.icon,null,"ui-icon-check ui-state-checked",t)._toggleClass(this.icon,null,"ui-icon-blank",!t),"radio"===this.type&&this._getRadioGroup().each(function(){var t=x(this).checkboxradio("instance");t&&t._removeClass(t.label,"ui-checkboxradio-checked","ui-state-active")})},_destroy:function(){this._unbindFormResetHandler(),this.icon&&(this.icon.remove(),this.iconSpace.remove())},_setOption:function(t,e){"label"===t&&!e||(this._super(t,e),"disabled"===t?(this._toggleClass(this.label,null,"ui-state-disabled",e),this.element[0].disabled=e):this.refresh())},_updateIcon:function(t){var e="ui-icon ui-icon-background ";this.options.icon?(this.icon||(this.icon=x("<span>"),this.iconSpace=x("<span> </span>"),this._addClass(this.iconSpace,"ui-checkboxradio-icon-space")),"checkbox"===this.type?(e+=t?"ui-icon-check ui-state-checked":"ui-icon-blank",this._removeClass(this.icon,null,t?"ui-icon-blank":"ui-icon-check")):e+="ui-icon-blank",this._addClass(this.icon,"ui-checkboxradio-icon",e),t||this._removeClass(this.icon,null,"ui-icon-check ui-state-checked"),this.icon.prependTo(this.label).after(this.iconSpace)):void 0!==this.icon&&(this.icon.remove(),this.iconSpace.remove(),delete this.icon)},_updateLabel:function(){var t=this.label.contents().not(this.element[0]);this.icon&&(t=t.not(this.icon[0])),(t=this.iconSpace?t.not(this.iconSpace[0]):t).remove(),this.label.append(this.options.label)},refresh:function(){var t=this.element[0].checked,e=this.element[0].disabled;this._updateIcon(t),this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",t),null!==this.options.label&&this._updateLabel(),e!==this.options.disabled&&this._setOptions({disabled:e})}}]),x.ui.checkboxradio,x.widget("ui.button",{version:"1.12.1",defaultElement:"<button>",options:{classes:{"ui-button":"ui-corner-all"},disabled:null,icon:null,iconPosition:"beginning",label:null,showLabel:!0},_getCreateOptions:function(){var t,e=this._super()||{};return this.isInput=this.element.is("input"),null!=(t=this.element[0].disabled)&&(e.disabled=t),this.originalLabel=this.isInput?this.element.val():this.element.html(),this.originalLabel&&(e.label=this.originalLabel),e},_create:function(){!this.option.showLabel&!this.options.icon&&(this.options.showLabel=!0),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled||!1),this.hasTitle=!!this.element.attr("title"),this.options.label&&this.options.label!==this.originalLabel&&(this.isInput?this.element.val(this.options.label):this.element.html(this.options.label)),this._addClass("ui-button","ui-widget"),this._setOption("disabled",this.options.disabled),this._enhance(),this.element.is("a")&&this._on({keyup:function(t){t.keyCode===x.ui.keyCode.SPACE&&(t.preventDefault(),this.element[0].click?this.element[0].click():this.element.trigger("click"))}})},_enhance:function(){this.element.is("button")||this.element.attr("role","button"),this.options.icon&&(this._updateIcon("icon",this.options.icon),this._updateTooltip())},_updateTooltip:function(){this.title=this.element.attr("title"),this.options.showLabel||this.title||this.element.attr("title",this.options.label)},_updateIcon:function(t,e){var t="iconPosition"!==t,n=t?this.options.iconPosition:e,r="top"===n||"bottom"===n;this.icon?t&&this._removeClass(this.icon,null,this.options.icon):(this.icon=x("<span>"),this._addClass(this.icon,"ui-button-icon","ui-icon"),this.options.showLabel||this._addClass("ui-button-icon-only")),t&&this._addClass(this.icon,null,e),this._attachIcon(n),r?(this._addClass(this.icon,null,"ui-widget-icon-block"),this.iconSpace&&this.iconSpace.remove()):(this.iconSpace||(this.iconSpace=x("<span> </span>"),this._addClass(this.iconSpace,"ui-button-icon-space")),this._removeClass(this.icon,null,"ui-wiget-icon-block"),this._attachIconSpace(n))},_destroy:function(){this.element.removeAttr("role"),this.icon&&this.icon.remove(),this.iconSpace&&this.iconSpace.remove(),this.hasTitle||this.element.removeAttr("title")},_attachIconSpace:function(t){this.icon[/^(?:end|bottom)/.test(t)?"before":"after"](this.iconSpace)},_attachIcon:function(t){this.element[/^(?:end|bottom)/.test(t)?"append":"prepend"](this.icon)},_setOptions:function(t){var e=(void 0===t.showLabel?this.options:t).showLabel,n=(void 0===t.icon?this.options:t).icon;e||n||(t.showLabel=!0),this._super(t)},_setOption:function(t,e){"icon"===t&&(e?this._updateIcon(t,e):this.icon&&(this.icon.remove(),this.iconSpace&&this.iconSpace.remove())),"iconPosition"===t&&this._updateIcon(t,e),"showLabel"===t&&(this._toggleClass("ui-button-icon-only",null,!e),this._updateTooltip()),"label"===t&&(this.isInput?this.element.val(e):(this.element.html(e),this.icon&&(this._attachIcon(this.options.iconPosition),this._attachIconSpace(this.options.iconPosition)))),this._super(t,e),"disabled"===t&&(this._toggleClass(null,"ui-state-disabled",e),(this.element[0].disabled=e)&&this.element.blur())},refresh:function(){var t=this.element.is("input, button")?this.element[0].disabled:this.element.hasClass("ui-button-disabled");t!==this.options.disabled&&this._setOptions({disabled:t}),this._updateTooltip()}}),!1!==x.uiBackCompat&&(x.widget("ui.button",x.ui.button,{options:{text:!0,icons:{primary:null,secondary:null}},_create:function(){this.options.showLabel&&!this.options.text&&(this.options.showLabel=this.options.text),!this.options.showLabel&&this.options.text&&(this.options.text=this.options.showLabel),this.options.icon||!this.options.icons.primary&&!this.options.icons.secondary?this.options.icon&&(this.options.icons.primary=this.options.icon):this.options.icons.primary?this.options.icon=this.options.icons.primary:(this.options.icon=this.options.icons.secondary,this.options.iconPosition="end"),this._super()},_setOption:function(t,e){"text"!==t?("showLabel"===t&&(this.options.text=e),"icon"===t&&(this.options.icons.primary=e),"icons"===t&&(e.primary?(this._super("icon",e.primary),this._super("iconPosition","beginning")):e.secondary&&(this._super("icon",e.secondary),this._super("iconPosition","end"))),this._superApply(arguments)):this._super("showLabel",e)}}),x.fn.button=(f=x.fn.button,function(){return!this.length||this.length&&"INPUT"!==this[0].tagName||this.length&&"INPUT"===this[0].tagName&&"checkbox"!==this.attr("type")&&"radio"!==this.attr("type")?f.apply(this,arguments):(x.ui.checkboxradio||x.error("Checkboxradio widget missing"),0===arguments.length?this.checkboxradio({icon:!1}):this.checkboxradio.apply(this,arguments))}),x.fn.buttonset=function(){return x.ui.controlgroup||x.error("Controlgroup widget missing"),"option"===arguments[0]&&"items"===arguments[1]&&arguments[2]?this.controlgroup.apply(this,[arguments[0],"items.button",arguments[2]]):"option"===arguments[0]&&"items"===arguments[1]?this.controlgroup.apply(this,[arguments[0],"items.button"]):("object"==typeof arguments[0]&&arguments[0].items&&(arguments[0].items={button:arguments[0].items}),this.controlgroup.apply(this,arguments))}),x.ui.button,x.ui.safeActiveElement=function(e){var n;try{n=e.activeElement}catch(t){n=e.body}return n=(n=n||e.body).nodeName?n:e.body},x.widget("ui.menu",{version:"1.12.1",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-caret-1-e"},items:"> *",menus:"ul",position:{my:"left top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element,this.mouseHandled=!1,this.element.uniqueId().attr({role:this.options.role,tabIndex:0}),this._addClass("ui-menu","ui-widget ui-widget-content"),this._on({"mousedown .ui-menu-item":function(t){t.preventDefault()},"click .ui-menu-item":function(t){var e=x(t.target),n=x(x.ui.safeActiveElement(this.document[0]));!this.mouseHandled&&e.not(".ui-state-disabled").length&&(this.select(t),t.isPropagationStopped()||(this.mouseHandled=!0),e.has(".ui-menu").length?this.expand(t):!this.element.is(":focus")&&n.closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer)))},"mouseenter .ui-menu-item":function(t){var e,n;this.previousFilter||(e=x(t.target).closest(".ui-menu-item"),n=x(t.currentTarget),e[0]===n[0]&&(this._removeClass(n.siblings().children(".ui-state-active"),null,"ui-state-active"),this.focus(t,n)))},mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(t,e){var n=this.active||this.element.find(this.options.items).eq(0);e||this.focus(t,n)},blur:function(t){this._delay(function(){x.contains(this.element[0],x.ui.safeActiveElement(this.document[0]))||this.collapseAll(t)})},keydown:"_keydown"}),this.refresh(),this._on(this.document,{click:function(t){this._closeOnDocumentClick(t)&&this.collapseAll(t),this.mouseHandled=!1}})},_destroy:function(){var t=this.element.find(".ui-menu-item").removeAttr("role aria-disabled").children(".ui-menu-item-wrapper").removeUniqueId().removeAttr("tabIndex role aria-haspopup");this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeAttr("role aria-labelledby aria-expanded aria-hidden aria-disabled tabIndex").removeUniqueId().show(),t.children().each(function(){var t=x(this);t.data("ui-menu-submenu-caret")&&t.remove()})},_keydown:function(t){var e,n,r,i=!0;switch(t.keyCode){case x.ui.keyCode.PAGE_UP:this.previousPage(t);break;case x.ui.keyCode.PAGE_DOWN:this.nextPage(t);break;case x.ui.keyCode.HOME:this._move("first","first",t);break;case x.ui.keyCode.END:this._move("last","last",t);break;case x.ui.keyCode.UP:this.previous(t);break;case x.ui.keyCode.DOWN:this.next(t);break;case x.ui.keyCode.LEFT:this.collapse(t);break;case x.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(t);break;case x.ui.keyCode.ENTER:case x.ui.keyCode.SPACE:this._activate(t);break;case x.ui.keyCode.ESCAPE:this.collapse(t);break;default:e=this.previousFilter||"",r=i=!1,n=96<=t.keyCode&&t.keyCode<=105?(t.keyCode-96).toString():String.fromCharCode(t.keyCode),clearTimeout(this.filterTimer),n===e?r=!0:n=e+n,e=this._filterMenuItems(n),(e=r&&-1!==e.index(this.active.next())?this.active.nextAll(".ui-menu-item"):e).length||(n=String.fromCharCode(t.keyCode),e=this._filterMenuItems(n)),e.length?(this.focus(t,e),this.previousFilter=n,this.filterTimer=this._delay(function(){delete this.previousFilter},1e3)):delete this.previousFilter}i&&t.preventDefault()},_activate:function(t){this.active&&!this.active.is(".ui-state-disabled")&&(this.active.children("[aria-haspopup='true']").length?this.expand(t):this.select(t))},refresh:function(){var t,e,r=this,i=this.options.icons.submenu,n=this.element.find(this.options.menus);this._toggleClass("ui-menu-icons",null,!!this.element.find(".ui-icon").length),t=n.filter(":not(.ui-menu)").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each(function(){var t=x(this),e=t.prev(),n=x("<span>").data("ui-menu-submenu-caret",!0);r._addClass(n,"ui-menu-icon","ui-icon "+i),e.attr("aria-haspopup","true").prepend(n),t.attr("aria-labelledby",e.attr("id"))}),this._addClass(t,"ui-menu","ui-widget ui-widget-content ui-front"),(t=n.add(this.element).find(this.options.items)).not(".ui-menu-item").each(function(){var t=x(this);r._isDivider(t)&&r._addClass(t,"ui-menu-divider","ui-widget-content")}),e=(n=t.not(".ui-menu-item, .ui-menu-divider")).children().not(".ui-menu").uniqueId().attr({tabIndex:-1,role:this._itemRole()}),this._addClass(n,"ui-menu-item")._addClass(e,"ui-menu-item-wrapper"),t.filter(".ui-state-disabled").attr("aria-disabled","true"),this.active&&!x.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(t,e){var n;"icons"===t&&(n=this.element.find(".ui-menu-icon"),this._removeClass(n,null,this.options.icons.submenu)._addClass(n,null,e.submenu)),this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",String(t)),this._toggleClass(null,"ui-state-disabled",!!t)},focus:function(t,e){var n;this.blur(t,t&&"focus"===t.type),this._scrollIntoView(e),this.active=e.first(),n=this.active.children(".ui-menu-item-wrapper"),this._addClass(n,null,"ui-state-active"),this.options.role&&this.element.attr("aria-activedescendant",n.attr("id")),n=this.active.parent().closest(".ui-menu-item").children(".ui-menu-item-wrapper"),this._addClass(n,null,"ui-state-active"),t&&"keydown"===t.type?this._close():this.timer=this._delay(function(){this._close()},this.delay),(n=e.children(".ui-menu")).length&&t&&/^mouse/.test(t.type)&&this._startOpening(n),this.activeMenu=e.parent(),this._trigger("focus",t,{item:e})},_scrollIntoView:function(t){var e,n,r;this._hasScroll()&&(e=parseFloat(x.css(this.activeMenu[0],"borderTopWidth"))||0,n=parseFloat(x.css(this.activeMenu[0],"paddingTop"))||0,e=t.offset().top-this.activeMenu.offset().top-e-n,n=this.activeMenu.scrollTop(),r=this.activeMenu.height(),t=t.outerHeight(),e<0?this.activeMenu.scrollTop(n+e):r<e+t&&this.activeMenu.scrollTop(n+e-r+t))},blur:function(t,e){e||clearTimeout(this.timer),this.active&&(this._removeClass(this.active.children(".ui-menu-item-wrapper"),null,"ui-state-active"),this._trigger("blur",t,{item:this.active}),this.active=null)},_startOpening:function(t){clearTimeout(this.timer),"true"===t.attr("aria-hidden")&&(this.timer=this._delay(function(){this._close(),this._open(t)},this.delay))},_open:function(t){var e=x.extend({of:this.active},this.options.position);clearTimeout(this.timer),this.element.find(".ui-menu").not(t.parents(".ui-menu")).hide().attr("aria-hidden","true"),t.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(e)},collapseAll:function(e,n){clearTimeout(this.timer),this.timer=this._delay(function(){var t=n?this.element:x(e&&e.target).closest(this.element.find(".ui-menu"));t.length||(t=this.element),this._close(t),this.blur(e),this._removeClass(t.find(".ui-state-active"),null,"ui-state-active"),this.activeMenu=t},this.delay)},_close:function(t){(t=t||(this.active?this.active.parent():this.element)).find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false")},_closeOnDocumentClick:function(t){return!x(t.target).closest(".ui-menu").length},_isDivider:function(t){return!/[^\-\u2014\u2013\s]/.test(t.text())},collapse:function(t){var e=this.active&&this.active.parent().closest(".ui-menu-item",this.element);e&&e.length&&(this._close(),this.focus(t,e))},expand:function(t){var e=this.active&&this.active.children(".ui-menu ").find(this.options.items).first();e&&e.length&&(this._open(e.parent()),this._delay(function(){this.focus(t,e)}))},next:function(t){this._move("next","first",t)},previous:function(t){this._move("prev","last",t)},isFirstItem:function(){return this.active&&!this.active.preval(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_move:function(t,e,n){var r;(r=this.active?"first"===t||"last"===t?this.active["first"===t?"prevAll":"nextAll"](".ui-menu-item").eq(-1):this.active[t+"All"](".ui-menu-item").eq(0):r)&&r.length&&this.active||(r=this.activeMenu.find(this.options.items)[e]()),this.focus(n,r)},nextPage:function(t){var e,n,r;this.active?this.isLastItem()||(this._hasScroll()?(n=this.active.offset().top,r=this.element.height(),this.active.nextAll(".ui-menu-item").each(function(){return(e=x(this)).offset().top-n-r<0}),this.focus(t,e)):this.focus(t,this.activeMenu.find(this.options.items)[this.active?"last":"first"]())):this.next(t)},previousPage:function(t){var e,n,r;this.active?this.isFirstItem()||(this._hasScroll()?(n=this.active.offset().top,r=this.element.height(),this.active.preval(".ui-menu-item").each(function(){return 0<(e=x(this)).offset().top-n+r}),this.focus(t,e)):this.focus(t,this.activeMenu.find(this.options.items).first())):this.next(t)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(t){this.active=this.active||x(t.target).closest(".ui-menu-item");var e={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(t,!0),this._trigger("select",t,e)},_filterMenuItems:function(t){var t=t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"),e=new RegExp("^"+t,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter(function(){return e.test(x.trim(x(this).children(".ui-menu-item-wrapper").text()))})}}),x.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),!1);function g(e){return function(){var t=this.element.val();e.apply(this,arguments),this._refresh(),t!==this.element.val()&&this._trigger("change")}}x(document).on("mouseup",function(){d=!1}),x.widget("ui.mouse",{version:"1.12.1",options:{cancel:"input, textarea, button, select, option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.on("mousedown."+this.widgetName,function(t){return e._mouseDown(t)}).on("click."+this.widgetName,function(t){if(!0===x.data(t.target,e.widgetName+".preventClickEvent"))return x.removeData(t.target,e.widgetName+".preventClickEvent"),t.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.off("."+this.widgetName),this._mouseMoveDelegate&&this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(t){var e,n,r;if(!d)return this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(t),n=1===(this._mouseDownEvent=t).which,r=!("string"!=typeof(e=this).options.cancel||!t.target.nodeName)&&x(t.target).closest(this.options.cancel).length,!(n&&!r&&this._mouseCapture(t))||(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){e.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=!1!==this._mouseStart(t),!this._mouseStarted)?(t.preventDefault(),!0):(!0===x.data(t.target,this.widgetName+".preventClickEvent")&&x.removeData(t.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return e._mouseMove(t)},this._mouseUpDelegate=function(t){return e._mouseUp(t)},this.document.on("mousemove."+this.widgetName,this._mouseMoveDelegate).on("mouseup."+this.widgetName,this._mouseUpDelegate),t.preventDefault(),d=!0))},_mouseMove:function(t){if(this._mouseMoved){if(x.ui.ie&&(!document.documentMode||document.documentMode<9)&&!t.button)return this._mouseUp(t);if(!t.which)if(t.originalEvent.altKey||t.originalEvent.ctrlKey||t.originalEvent.metaKey||t.originalEvent.shiftKey)this.ignoreMissingWhich=!0;else if(!this.ignoreMissingWhich)return this._mouseUp(t)}return(t.which||t.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(t),t.preventDefault()):(this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,t),this._mouseStarted?this._mouseDrag(t):this._mouseUp(t)),!this._mouseStarted)},_mouseUp:function(t){this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,t.target===this._mouseDownEvent.target&&x.data(t.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(t)),this._mouseDelayTimer&&(clearTimeout(this._mouseDelayTimer),delete this._mouseDelayTimer),this.ignoreMissingWhich=!1,d=!1,t.preventDefault()},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),x.widget("ui.selectmenu",[x.ui.formResetMixin,{version:"1.12.1",defaultElement:"<select>",options:{appendTo:null,classes:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"},disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:!1,change:null,close:null,focus:null,open:null,select:null},_create:function(){var t=this.element.uniqueId().attr("id");this.ids={element:t,button:t+"-button",menu:t+"-menu"},this._drawButton(),this._drawMenu(),this._bindFormResetHandler(),this._rendered=!1,this.menuItems=x()},_drawButton:function(){var t,e=this,n=this._parseOption(this.element.find("option:selected"),this.element[0].selectedIndex);this.labels=this.element.labels().attr("for",this.ids.button),this._on(this.labels,{click:function(t){this.button.focus(),t.preventDefault()}}),this.element.hide(),this.button=x("<span>",{tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true",title:this.element.attr("title")}).insertAfter(this.element),this._addClass(this.button,"ui-selectmenu-button ui-selectmenu-button-closed","ui-button ui-widget"),t=x("<span>").appendTo(this.button),this._addClass(t,"ui-selectmenu-icon","ui-icon "+this.options.icons.button),this.buttonItem=this._renderButtonItem(n).appendTo(this.button),!1!==this.options.width&&this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",function(){e._rendered||e._refreshMenu()})},_drawMenu:function(){var n=this;this.menu=x("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=x("<div>").append(this.menu),this._addClass(this.menuWrap,"ui-selectmenu-menu","ui-front"),this.menuWrap.appendTo(this._appendTo()),this.menuInstance=this.menu.menu({classes:{"ui-menu":"ui-corner-bottom"},role:"listbox",select:function(t,e){t.preventDefault(),n._setSelection(),n._select(e.item.data("ui-selectmenu-item"),t)},focus:function(t,e){e=e.item.data("ui-selectmenu-item");null!=n.focusIndex&&e.index!==n.focusIndex&&(n._trigger("focus",t,{item:e}),n.isOpen||n._select(e,t)),n.focusIndex=e.index,n.button.attr("aria-activedescendant",n.menuItems.eq(e.index).attr("id"))}}).menu("instance"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return!1},this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu(),this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(this._getSelectedItem().data("ui-selectmenu-item")||{})),null===this.options.width&&this._resizeButton()},_refreshMenu:function(){var t=this.element.find("option");this.menu.empty(),this._parseOptions(t),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup").find(".ui-menu-item-wrapper"),this._rendered=!0,t.length&&(t=this._getSelectedItem(),this.menuInstance.focus(null,t),this._setAria(t.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(t){this.options.disabled||(this._rendered?(this._removeClass(this.menu.find(".ui-state-active"),null,"ui-state-active"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.menuItems.length&&(this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",t)))},_position:function(){this.menuWrap.position(x.extend({of:this.button},this.options.position))},close:function(t){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",t))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderButtonItem:function(t){var e=x("<span>");return this._setText(e,t.label),this._addClass(e,"ui-selectmenu-text"),e},_renderMenu:function(r,t){var i=this,o="";x.each(t,function(t,e){var n;e.optgroup!==o&&(n=x("<li>",{text:e.optgroup}),i._addClass(n,"ui-selectmenu-optgroup","ui-menu-divider"+(e.element.parent("optgroup").prop("disabled")?" ui-state-disabled":"")),n.appendTo(r),o=e.optgroup),i._renderItemData(r,e)})},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-selectmenu-item",e)},_renderItem:function(t,e){var n=x("<li>"),r=x("<div>",{title:e.element.attr("title")});return e.disabled&&this._addClass(n,null,"ui-state-disabled"),this._setText(r,e.label),n.append(r).appendTo(t)},_setText:function(t,e){e?t.text(e):t.html("&#160;")},_move:function(t,e){var n,r=".ui-menu-item";this.isOpen?n=this.menuItems.eq(this.focusIndex).parent("li"):(n=this.menuItems.eq(this.element[0].selectedIndex).parent("li"),r+=":not(.ui-state-disabled)"),(n="first"===t||"last"===t?n["first"===t?"prevAll":"nextAll"](r).eq(-1):n[t+"All"](r).eq(0)).length&&this.menuInstance.focus(e,n)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex).parent("li")},_toggle:function(t){this[this.isOpen?"close":"open"](t)},_setSelection:function(){var t;this.range&&(window.getSelection?((t=window.getSelection()).removeAllRanges(),t.addRange(this.range)):this.range.select(),this.button.focus())},_documentClick:{mousedown:function(t){!this.isOpen||x(t.target).closest(".ui-selectmenu-menu, #"+x.ui.escapeSelector(this.ids.button)).length||this.close(t)}},_buttonEvents:{mousedown:function(){var t;window.getSelection?(t=window.getSelection()).rangeCount&&(this.range=t.getRangeAt(0)):this.range=document.selection.createRange()},click:function(t){this._setSelection(),this._toggle(t)},keydown:function(t){var e=!0;switch(t.keyCode){case x.ui.keyCode.TAB:case x.ui.keyCode.ESCAPE:this.close(t),e=!1;break;case x.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(t);break;case x.ui.keyCode.UP:t.altKey?this._toggle(t):this._move("prev",t);break;case x.ui.keyCode.DOWN:t.altKey?this._toggle(t):this._move("next",t);break;case x.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(t):this._toggle(t);break;case x.ui.keyCode.LEFT:this._move("prev",t);break;case x.ui.keyCode.RIGHT:this._move("next",t);break;case x.ui.keyCode.HOME:case x.ui.keyCode.PAGE_UP:this._move("first",t);break;case x.ui.keyCode.END:case x.ui.keyCode.PAGE_DOWN:this._move("last",t);break;default:this.menu.trigger(t),e=!1}e&&t.preventDefault()}},_selectFocusedItem:function(t){var e=this.menuItems.eq(this.focusIndex).parent("li");e.hasClass("ui-state-disabled")||this._select(e.data("ui-selectmenu-item"),t)},_select:function(t,e){var n=this.element[0].selectedIndex;this.element[0].selectedIndex=t.index,this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(t)),this._setAria(t),this._trigger("select",e,{item:t}),t.index!==n&&this._trigger("change",e,{item:t}),this.close(e)},_setAria:function(t){t=this.menuItems.eq(t.index).attr("id");this.button.attr({"aria-labelledby":t,"aria-activedescendant":t}),this.menu.attr("aria-activedescendant",t)},_setOption:function(t,e){var n;"icons"===t&&(n=this.button.find("span.ui-icon"),this._removeClass(n,null,this.options.icons.button)._addClass(n,null,e.button)),this._super(t,e),"appendTo"===t&&this.menuWrap.appendTo(this._appendTo()),"width"===t&&this._resizeButton()},_setOptionDisabled:function(t){this._super(t),this.menuInstance.option("disabled",t),this.button.attr("aria-disabled",t),this._toggleClass(this.button,null,"ui-state-disabled",t),this.element.prop("disabled",t),t?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)},_appendTo:function(){var t=this.options.appendTo;return t=(t=(t=t&&(t.jquery||t.nodeType?x(t):this.document.find(t).eq(0)))&&t[0]?t:this.element.closest(".ui-front, dialog")).length?t:this.document[0].body},_toggleAttr:function(){this.button.attr("aria-expanded",this.isOpen),this._removeClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"closed":"open"))._addClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"open":"closed"))._toggleClass(this.menuWrap,"ui-selectmenu-open",null,this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var t=this.options.width;!1!==t?(null===t&&(t=this.element.show().outerWidth(),this.element.hide()),this.button.outerWidth(t)):this.button.css("width","")},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){var t=this._super();return t.disabled=this.element.prop("disabled"),t},_parseOptions:function(t){var n=this,r=[];t.each(function(t,e){r.push(n._parseOption(x(e),t))}),this.items=r},_parseOption:function(t,e){var n=t.parent("optgroup");return{element:t,index:e,value:t.val(),label:t.text(),optgroup:n.attr("label")||"",disabled:n.prop("disabled")||t.prop("disabled")}},_destroy:function(){this._unbindFormResetHandler(),this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.labels.attr("for",this.ids.element)}}]),x.widget("ui.slider",x.ui.mouse,{version:"1.12.1",widgetEventPrefix:"slide",options:{animate:!1,classes:{"ui-slider":"ui-corner-all","ui-slider-handle":"ui-corner-all","ui-slider-range":"ui-corner-all ui-widget-header"},distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this._addClass("ui-slider ui-slider-"+this.orientation,"ui-widget ui-widget-content"),this._refresh(),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var t,e=this.options,n=this.element.find(".ui-slider-handle"),r=[],i=e.values&&e.values.length||1;for(n.length>i&&(n.slice(i).remove(),n=n.slice(0,i)),t=n.length;t<i;t++)r.push("<span tabindex='0'></span>");this.handles=n.add(x(r.join("")).appendTo(this.element)),this._addClass(this.handles,"ui-slider-handle","ui-state-default"),this.handle=this.handles.eq(0),this.handles.each(function(t){x(this).data("ui-slider-handle-index",t).attr("tabIndex",0)})},_createRange:function(){var t=this.options;t.range?(!0===t.range&&(t.values?t.values.length&&2!==t.values.length?t.values=[t.values[0],t.values[0]]:x.isArray(t.values)&&(t.values=t.values.slice(0)):t.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?(this._removeClass(this.range,"ui-slider-range-min ui-slider-range-max"),this.range.css({left:"",bottom:""})):(this.range=x("<div>").appendTo(this.element),this._addClass(this.range,"ui-slider-range")),"min"!==t.range&&"max"!==t.range||this._addClass(this.range,"ui-slider-range-"+t.range)):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this._mouseDestroy()},_mouseCapture:function(t){var n,r,i,o,e,s,a=this,u=this.options;return!u.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),e={x:t.pageX,y:t.pageY},n=this._normValueFromMouse(e),r=this._valueMax()-this._valueMin()+1,this.handles.each(function(t){var e=Math.abs(n-a.values(t));(e<r||r===e&&(t===a._lastChangedValue||a.values(t)===u.min))&&(r=e,i=x(this),o=t)}),!1!==this._start(t,o)&&(this._mouseSliding=!0,this._handleIndex=o,this._addClass(i,null,"ui-state-active"),i.trigger("focus"),e=i.offset(),s=!x(t.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=s?{left:0,top:0}:{left:t.pageX-e.left,top:t.pageY-e.top-i.height()/2-(parseInt(i.css("borderTopWidth"),10)||0)-(parseInt(i.css("borderBottomWidth"),10)||0)+(parseInt(i.css("marginTop"),10)||0)},this._animateOff=!0))},_mouseStart:function(){return!0},_mouseDrag:function(t){var e={x:t.pageX,y:t.pageY},e=this._normValueFromMouse(e);return this._slide(t,this._handleIndex,e),!1},_mouseStop:function(t){return this._removeClass(this.handles,null,"ui-state-active"),this._mouseSliding=!1,this._stop(t,this._handleIndex),this._change(t,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(t){var e,t="horizontal"===this.orientation?(e=this.elementSize.width,t.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(e=this.elementSize.height,t.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0));return(t=1<(t=t/e)?1:t)<0&&(t=0),"vertical"===this.orientation&&(t=1-t),e=this._valueMax()-this._valueMin(),t=this._valueMin()+t*e,this._trimAlignValue(t)},_uiHash:function(t,e,n){var r={handle:this.handles[t],handleIndex:t,value:void 0!==e?e:this.value()};return this._hasMultipleValues()&&(r.value=void 0!==e?e:this.values(t),r.values=n||this.values()),r},_hasMultipleValues:function(){return this.options.values&&this.options.values.length},_start:function(t,e){return this._trigger("start",t,this._uiHash(e))},_slide:function(t,e,n){var r,i=this.value(),o=this.values();this._hasMultipleValues()&&(r=this.values(e?0:1),i=this.values(e),2===this.options.values.length&&!0===this.options.range&&(n=0===e?Math.min(r,n):Math.max(r,n)),o[e]=n),n!==i&&!1!==this._trigger("slide",t,this._uiHash(e,n,o))&&(this._hasMultipleValues()?this.values(e,n):this.value(n))},_stop:function(t,e){this._trigger("stop",t,this._uiHash(e))},_change:function(t,e){this._keySliding||this._mouseSliding||(this._lastChangedValue=e,this._trigger("change",t,this._uiHash(e)))},value:function(t){return arguments.length?(this.options.value=this._trimAlignValue(t),this._refreshValue(),void this._change(null,0)):this._value()},values:function(t,e){var n,r,i;if(1<arguments.length)this.options.values[t]=this._trimAlignValue(e),this._refreshValue(),this._change(null,t);else{if(!arguments.length)return this._values();if(!x.isArray(t))return this._hasMultipleValues()?this._values(t):this.value();for(n=this.options.values,r=t,i=0;i<n.length;i+=1)n[i]=this._trimAlignValue(r[i]),this._change(null,i);this._refreshValue()}},_setOption:function(t,e){var n,r=0;switch("range"===t&&!0===this.options.range&&("min"===e?(this.options.value=this._values(0),this.options.values=null):"max"===e&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),x.isArray(this.options.values)&&(r=this.options.values.length),this._super(t,e),t){case"orientation":this._detectOrientation(),this._removeClass("ui-slider-horizontal ui-slider-vertical")._addClass("ui-slider-"+this.orientation),this._refreshValue(),this.options.range&&this._refreshRange(e),this.handles.css("horizontal"===e?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),n=r-1;0<=n;n--)this._change(null,n);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_setOptionDisabled:function(t){this._super(t),this._toggleClass(null,"ui-state-disabled",!!t)},_value:function(){var t=this.options.value;return this._trimAlignValue(t)},_values:function(t){var e,n,r;if(arguments.length)return e=this.options.values[t],this._trimAlignValue(e);if(this._hasMultipleValues()){for(n=this.options.values.slice(),r=0;r<n.length;r+=1)n[r]=this._trimAlignValue(n[r]);return n}return[]},_trimAlignValue:function(t){var e,n;return t<=this._valueMin()?this._valueMin():t>=this._valueMax()?this._valueMax():(e=0<this.options.step?this.options.step:1,n=t-(t=(t-this._valueMin())%e),2*Math.abs(t)>=e&&(n+=0<t?e:-e),parseFloat(n.toFixed(5)))},_calculateNewMax:function(){var t=this.options.max,e=this._valueMin(),n=this.options.step;(t=Math.round((t-e)/n)*n+e)>this.options.max&&(t-=n),this.max=parseFloat(t.toFixed(this._precision()))},_precision:function(){var t=this._precisionOf(this.options.step);return t=null!==this.options.min?Math.max(t,this._precisionOf(this.options.min)):t},_precisionOf:function(t){var t=t.toString(),e=t.indexOf(".");return-1===e?0:t.length-e-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshRange:function(t){"vertical"===t&&this.range.css({width:"",left:""}),"horizontal"===t&&this.range.css({height:"",bottom:""})},_refreshValue:function(){var e,n,t,r,i,o=this.options.range,s=this.options,a=this,u=!this._animateOff&&s.animate,c={};this._hasMultipleValues()?this.handles.each(function(t){n=(a.values(t)-a._valueMin())/(a._valueMax()-a._valueMin())*100,c["horizontal"===a.orientation?"left":"bottom"]=n+"%",x(this).stop(1,1)[u?"animate":"css"](c,s.animate),!0===a.options.range&&("horizontal"===a.orientation?(0===t&&a.range.stop(1,1)[u?"animate":"css"]({left:n+"%"},s.animate),1===t&&a.range[u?"animate":"css"]({width:n-e+"%"},{queue:!1,duration:s.animate})):(0===t&&a.range.stop(1,1)[u?"animate":"css"]({bottom:n+"%"},s.animate),1===t&&a.range[u?"animate":"css"]({height:n-e+"%"},{queue:!1,duration:s.animate}))),e=n}):(t=this.value(),r=this._valueMin(),i=this._valueMax(),n=i!==r?(t-r)/(i-r)*100:0,c["horizontal"===this.orientation?"left":"bottom"]=n+"%",this.handle.stop(1,1)[u?"animate":"css"](c,s.animate),"min"===o&&"horizontal"===this.orientation&&this.range.stop(1,1)[u?"animate":"css"]({width:n+"%"},s.animate),"max"===o&&"horizontal"===this.orientation&&this.range.stop(1,1)[u?"animate":"css"]({width:100-n+"%"},s.animate),"min"===o&&"vertical"===this.orientation&&this.range.stop(1,1)[u?"animate":"css"]({height:n+"%"},s.animate),"max"===o&&"vertical"===this.orientation&&this.range.stop(1,1)[u?"animate":"css"]({height:100-n+"%"},s.animate))},_handleEvents:{keydown:function(t){var e,n,r,i=x(t.target).data("ui-slider-handle-index");switch(t.keyCode){case x.ui.keyCode.HOME:case x.ui.keyCode.END:case x.ui.keyCode.PAGE_UP:case x.ui.keyCode.PAGE_DOWN:case x.ui.keyCode.UP:case x.ui.keyCode.RIGHT:case x.ui.keyCode.DOWN:case x.ui.keyCode.LEFT:if(t.preventDefault(),!this._keySliding&&(this._keySliding=!0,this._addClass(x(t.target),null,"ui-state-active"),!1===this._start(t,i)))return}switch(r=this.options.step,e=n=this._hasMultipleValues()?this.values(i):this.value(),t.keyCode){case x.ui.keyCode.HOME:n=this._valueMin();break;case x.ui.keyCode.END:n=this._valueMax();break;case x.ui.keyCode.PAGE_UP:n=this._trimAlignValue(e+(this._valueMax()-this._valueMin())/this.numPages);break;case x.ui.keyCode.PAGE_DOWN:n=this._trimAlignValue(e-(this._valueMax()-this._valueMin())/this.numPages);break;case x.ui.keyCode.UP:case x.ui.keyCode.RIGHT:if(e===this._valueMax())return;n=this._trimAlignValue(e+r);break;case x.ui.keyCode.DOWN:case x.ui.keyCode.LEFT:if(e===this._valueMin())return;n=this._trimAlignValue(e-r)}this._slide(t,i,n)},keyup:function(t){var e=x(t.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(t,e),this._change(t,e),this._removeClass(x(t.target),null,"ui-state-active"))}}}),x.widget("ui.spinner",{version:"1.12.1",defaultElement:"<input>",widgetEventPrefix:"spin",options:{classes:{"ui-spinner":"ui-corner-all","ui-spinner-down":"ui-corner-br","ui-spinner-up":"ui-corner-tr"},culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max),this._setOption("min",this.options.min),this._setOption("step",this.options.step),""!==this.value()&&this._value(this.element.val(),!0),this._draw(),this._on(this._events),this._refresh(),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var r=this._super(),i=this.element;return x.each(["min","max","step"],function(t,e){var n=i.attr(e);null!=n&&n.length&&(r[e]=n)}),r},_events:{keydown:function(t){this._start(t)&&this._keydown(t)&&t.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(t){this.cancelBlur?delete this.cancelBlur:(this._stop(),this._refresh(),this.previous!==this.element.val()&&this._trigger("change",t))},mousewheel:function(t,e){if(e){if(!this.spinning&&!this._start(t))return!1;this._spin((0<e?1:-1)*this.options.step,t),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay(function(){this.spinning&&this._stop(t)},100),t.preventDefault()}},"mousedown .ui-spinner-button":function(t){var e;function n(){this.element[0]!==x.ui.safeActiveElement(this.document[0])&&(this.element.trigger("focus"),this.previous=e,this._delay(function(){this.previous=e}))}e=this.element[0]===x.ui.safeActiveElement(this.document[0])?this.previous:this.element.val(),t.preventDefault(),n.call(this),this.cancelBlur=!0,this._delay(function(){delete this.cancelBlur,n.call(this)}),!1!==this._start(t)&&this._repeat(null,x(t.currentTarget).hasClass("ui-spinner-up")?1:-1,t)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(t){if(x(t.currentTarget).hasClass("ui-state-active"))return!1!==this._start(t)&&void this._repeat(null,x(t.currentTarget).hasClass("ui-spinner-up")?1:-1,t)},"mouseleave .ui-spinner-button":"_stop"},_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap("<span>").parent().append("<a></a><a></a>")},_draw:function(){this._enhance(),this._addClass(this.uiSpinner,"ui-spinner","ui-widget ui-widget-content"),this._addClass("ui-spinner-input"),this.element.attr("role","spinbutton"),this.buttons=this.uiSpinner.children("a").attr("tabIndex",-1).attr("aria-hidden",!0).button({classes:{"ui-button":""}}),this._removeClass(this.buttons,"ui-corner-all"),this._addClass(this.buttons.first(),"ui-spinner-button ui-spinner-up"),this._addClass(this.buttons.last(),"ui-spinner-button ui-spinner-down"),this.buttons.first().button({icon:this.options.icons.up,showLabel:!1}),this.buttons.last().button({icon:this.options.icons.down,showLabel:!1}),this.buttons.height()>Math.ceil(.5*this.uiSpinner.height())&&0<this.uiSpinner.height()&&this.uiSpinner.height(this.uiSpinner.height())},_keydown:function(t){var e=this.options,n=x.ui.keyCode;switch(t.keyCode){case n.UP:return this._repeat(null,1,t),!0;case n.DOWN:return this._repeat(null,-1,t),!0;case n.PAGE_UP:return this._repeat(null,e.page,t),!0;case n.PAGE_DOWN:return this._repeat(null,-e.page,t),!0}return!1},_start:function(t){return!(!this.spinning&&!1===this._trigger("start",t))&&(this.counter||(this.counter=1),this.spinning=!0)},_repeat:function(t,e,n){t=t||500,clearTimeout(this.timer),this.timer=this._delay(function(){this._repeat(40,e,n)},t),this._spin(e*this.options.step,n)},_spin:function(t,e){var n=this.value()||0;this.counter||(this.counter=1),n=this._adjustValue(n+t*this._increment(this.counter)),this.spinning&&!1===this._trigger("spin",e,{value:n})||(this._value(n),this.counter++)},_increment:function(t){var e=this.options.incremental;return e?x.isFunction(e)?e(t):Math.floor(t*t*t/5e4-t*t/500+17*t/200+1):1},_precision:function(){var t=this._precisionOf(this.options.step);return t=null!==this.options.min?Math.max(t,this._precisionOf(this.options.min)):t},_precisionOf:function(t){var t=t.toString(),e=t.indexOf(".");return-1===e?0:t.length-e-1},_adjustValue:function(t){var e,n=this.options,r=t-(e=null!==n.min?n.min:0);return t=e+Math.round(r/n.step)*n.step,t=parseFloat(t.toFixed(this._precision())),null!==n.max&&t>n.max?n.max:null!==n.min&&t<n.min?n.min:t},_stop:function(t){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",t))},_setOption:function(t,e){var n;"culture"===t||"numberFormat"===t?(n=this._parse(this.element.val()),this.options[t]=e,this.element.val(this._format(n))):("max"!==t&&"min"!==t&&"step"!==t||"string"==typeof e&&(e=this._parse(e)),"icons"===t&&(n=this.buttons.first().find(".ui-icon"),this._removeClass(n,null,this.options.icons.up),this._addClass(n,null,e.up),n=this.buttons.last().find(".ui-icon"),this._removeClass(n,null,this.options.icons.down),this._addClass(n,null,e.down)),this._super(t,e))},_setOptionDisabled:function(t){this._super(t),this._toggleClass(this.uiSpinner,null,"ui-state-disabled",!!t),this.element.prop("disabled",!!t),this.buttons.button(t?"disable":"enable")},_setOptions:g(function(t){this._super(t)}),_parse:function(t){return""===(t="string"==typeof t&&""!==t?window.Globalize&&this.options.numberFormat?Globalize.parseFloat(t,10,this.options.culture):+t:t)||isNaN(t)?null:t},_format:function(t){return""===t?"":window.Globalize&&this.options.numberFormat?Globalize.format(t,this.options.numberFormat,this.options.culture):t},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var t=this.value();return null!==t&&t===this._adjustValue(t)},_value:function(t,e){var n;""!==t&&null!==(n=this._parse(t))&&(e||(n=this._adjustValue(n)),t=this._format(n)),this.element.val(t),this._refresh()},_destroy:function(){this.element.prop("disabled",!1).removeAttr("autocomplete role aria-valuemin aria-valuemax aria-valuenow"),this.uiSpinner.replaceWith(this.element)},stepUp:g(function(t){this._stepUp(t)}),_stepUp:function(t){this._start()&&(this._spin((t||1)*this.options.step),this._stop())},stepDown:g(function(t){this._stepDown(t)}),_stepDown:function(t){this._start()&&(this._spin((t||1)*-this.options.step),this._stop())},pageUp:g(function(t){this._stepUp((t||1)*this.options.page)}),pageDown:g(function(t){this._stepDown((t||1)*this.options.page)}),value:function(t){if(!arguments.length)return this._parse(this.element.val());g(this._value).call(this,t)},widget:function(){return this.uiSpinner}}),!1!==x.uiBackCompat&&x.widget("ui.spinner",x.ui.spinner,{_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml())},_uiSpinnerHtml:function(){return"<span>"},_buttonHtml:function(){return"<a></a><a></a>"}}),x.ui.spinner};"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(),!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t:t(jQuery)}(function(l){function e(t){var e,n=t||window.event,r=g.call(arguments,1),i=0,o=0,s=0,a=0,u=0,c=0;if((t=l.event.fix(n)).type="mousewheel","detail"in n&&(s=-1*n.detail),"wheelDelta"in n&&(s=n.wheelDelta),"wheelDeltaY"in n&&(s=n.wheelDeltaY),"wheelDeltaX"in n&&(o=-1*n.wheelDeltaX),"axis"in n&&n.axis===n.HORIZONTAL_AXIS&&(o=-1*s,s=0),i=0===s?o:s,"deltaY"in n&&(i=s=-1*n.deltaY),"deltaX"in n&&(o=n.deltaX,0===s&&(i=-1*o)),0!==s||0!==o)return 1===n.deltaMode?(i*=e=l.data(this,"mousewheel-line-height"),s*=e,o*=e):2===n.deltaMode&&(i*=e=l.data(this,"mousewheel-page-height"),s*=e,o*=e),a=Math.max(Math.abs(s),Math.abs(o)),(!d||a<d)&&f(n,d=a)&&(d/=40),f(n,a)&&(i/=40,o/=40,s/=40),i=Math[1<=i?"floor":"ceil"](i/d),o=Math[1<=o?"floor":"ceil"](o/d),s=Math[1<=s?"floor":"ceil"](s/d),m.settings.normalizeOffset&&this.getBoundingClientRect&&(e=this.getBoundingClientRect(),u=t.clientX-e.left,c=t.clientY-e.top),t.deltaX=o,t.deltaY=s,t.deltaFactor=d,t.offsetX=u,t.offsetY=c,t.deltaMode=0,r.unshift(t,i,o,s),p&&clearTimeout(p),p=setTimeout(h,200),(l.event.dispatch||l.event.handle).apply(this,r)}function h(){d=null}function f(t,e){return m.settings.adjustOldDeltas&&"mousewheel"===t.type&&e%120==0}var p,d,t=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],n="onwheel"in document||9<=document.documentMode?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],g=Array.prototype.slice;if(l.event.fixHooks)for(var r=t.length;r;)l.event.fixHooks[t[--r]]=l.event.mouseHooks;var m=l.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var t=n.length;t;)this.addEventListener(n[--t],e,!1);else this.onmousewheel=e;l.data(this,"mousewheel-line-height",m.getLineHeight(this)),l.data(this,"mousewheel-page-height",m.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var t=n.length;t;)this.removeEventListener(n[--t],e,!1);else this.onmousewheel=null;l.removeData(this,"mousewheel-line-height"),l.removeData(this,"mousewheel-page-height")},getLineHeight:function(t){var t=l(t),e=t["offsetParent"in l.fn?"offsetParent":"parent"]();return e.length||(e=l("body")),parseInt(e.css("fontSize"),10)||parseInt(t.css("fontSize"),10)||16},getPageHeight:function(t){return l(t).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};l.fn.extend({mousewheel:function(t){return t?this.bind("mousewheel",t):this.trigger("mousewheel")},unmousewheel:function(t){return this.unbind("mousewheel",t)}})}),!function(t){var e;"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?e=window:"undefined"!=typeof global?e=global:"undefined"!=typeof self&&(e=self),e.html2canvas=t())}(function(){return function r(i,o,s){function a(n,t){if(!o[n]){if(!i[n]){var e="function"==typeof require&&require;if(!t&&e)return e(n,!0);if(u)return u(n,!0);t=new Error("Cannot find module '"+n+"'");throw t.code="MODULE_NOT_FOUND",t}e=o[n]={exports:{}};i[n][0].call(e.exports,function(t){var e=i[n][1][t];return a(e||t)},e,e.exports,r,i,o,s)}return o[n].exports}for(var u="function"==typeof require&&require,t=0;t<s.length;t++)a(s[t]);return a}({1:[function(t,E,T){!function(t){var e=this;function m(t){throw RangeError(g[t])}function n(t,e){for(var n=t.length;n--;)t[n]=e(t[n]);return t}function r(t,e){return n(t.split(p),e).join(".")}function v(t){for(var e,n,r=[],i=0,o=t.length;i<o;)55296<=(e=t.charCodeAt(i++))&&e<=56319&&i<o?56320==(64512&(n=t.charCodeAt(i++)))?r.push(((1023&e)<<10)+(1023&n)+65536):(r.push(e),i--):r.push(e);return r}function d(t){return n(t,function(t){var e="";return 65535<t&&(e+=S((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+S(t)}).join("")}function y(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function b(t,e,n){var r=0;for(t=n?k(t/l):t>>1,t+=k(t/e);C*x>>1<t;r+=_)t=k(t/C);return k(r+(C+1)*t/(t+c))}function i(t){var e,n,r,i,o,s,a,u=[],c=t.length,l=0,h=128,f=72,p=t.lastIndexOf("-");for(p<0&&(p=0),n=0;n<p;++n)128<=t.charCodeAt(n)&&m("not-basic"),u.push(t.charCodeAt(n));for(r=0<p?p+1:0;r<c;){for(i=l,o=1,s=_;c<=r&&m("invalid-input"),a=t.charCodeAt(r++),(_<=(a=a-48<10?a-22:a-65<26?a-65:a-97<26?a-97:_)||a>k((w-l)/o))&&m("overflow"),l+=a*o,!(a<(a=s<=f?1:f+x<=s?x:s-f));s+=_)o>k(w/(a=_-a))&&m("overflow"),o*=a;f=b(l-i,e=u.length+1,0==i),k(l/e)>w-h&&m("overflow"),h+=k(l/e),l%=e,u.splice(l++,0,h)}return d(u)}function o(t){for(var e,n,r,i,o,s,a,u,c,l,h=[],f=(t=v(t)).length,p=128,d=72,g=e=0;g<f;++g)(a=t[g])<128&&h.push(S(a));for(n=r=h.length,r&&h.push("-");n<f;){for(i=w,g=0;g<f;++g)p<=(a=t[g])&&a<i&&(i=a);for(i-p>k((w-e)/(u=n+1))&&m("overflow"),e+=(i-p)*u,p=i,g=0;g<f;++g)if((a=t[g])<p&&++e>w&&m("overflow"),a==p){for(o=e,s=_;!(o<(c=s<=d?1:d+x<=s?x:s-d));s+=_)h.push(S(y(c+(l=o-c)%(c=_-c),0))),o=k(l/c);h.push(S(y(o,0))),d=b(e,u,n==r),e=0,++n}++e,++p}return h.join("")}var s="object"==typeof T&&T,a="object"==typeof E&&E&&E.exports==s&&E,t="object"==typeof t&&t;t.global!==t&&t.window!==t||(e=t);var u,w=2147483647,_=36,x=26,c=38,l=700,h=/^xn--/,f=/[^ -~]/,p=/\x2E|\u3002|\uFF0E|\uFF61/g,g={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},C=_-1,k=Math.floor,S=String.fromCharCode,O={version:"1.2.4",ucs2:{decode:v,encode:d},decode:i,encode:o,toASCII:function(t){return r(t,function(t){return f.test(t)?"xn--"+o(t):t})},toUnicode:function(t){return r(t,function(t){return h.test(t)?i(t.slice(4).toLowerCase()):t})}};if(s&&!s.nodeType)if(a)a.exports=O;else for(u in O)O.hasOwnProperty(u)&&(s[u]=O[u]);else e.punycode=O}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e){function h(t,e){for(var n=3===t.nodeType?document.createTextNode(t.nodeValue):t.cloneNode(!1),r=t.firstChild;r;)!0!==e&&1===r.nodeType&&"SCRIPT"===r.nodeName||n.appendChild(h(r,e)),r=r.nextSibling;if(1===t.nodeType)if(n._scrollTop=t.scrollTop,n._scrollLeft=t.scrollLeft,"CANVAS"===t.nodeName){var i=t,o=n;try{o&&(o.width=i.width,o.height=i.height,o.getContext("2d").putImageData(i.getContext("2d").getImageData(0,0,i.width,i.height),0,0))}catch(t){s("Unable to copy canvas content from",i,t)}}else"TEXTAREA"!==t.nodeName&&"SELECT"!==t.nodeName||(n.value=t.value);return n}var s=t("./log");e.exports=function(o,t,e,n,s,a,u){var c=h(o.documentElement,s.javascriptEnabled),l=t.createElement("iframe");return l.className="html2canvas-container",l.style.visibility="hidden",l.style.position="fixed",l.style.left="-10000px",l.style.top="0px",l.style.border="0",l.width=e,l.height=n,l.scrolling="no",t.body.appendChild(l),new Promise(function(e){var t,n,r,i=l.contentWindow.document;l.contentWindow.onload=l.onload=function(){var t=setInterval(function(){0<i.body.childNodes.length&&(function t(e){if(1===e.nodeType){e.scrollTop=e._scrollTop,e.scrollLeft=e._scrollLeft;for(var n=e.firstChild;n;)t(n),n=n.nextSibling}}(i.documentElement),clearInterval(t),"view"===s.type&&(l.contentWindow.scrollTo(a,u),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||l.contentWindow.scrollY===u&&l.contentWindow.scrollX===a||(i.documentElement.style.top=-u+"px",i.documentElement.style.left=-a+"px",i.documentElement.style.position="absolute")),e(l))},50)},i.open(),i.write("<!DOCTYPE html><html></html>"),n=a,r=u,!(t=o).defaultView||n===t.defaultView.pageXOffset&&r===t.defaultView.pageYOffset||t.defaultView.scrollTo(n,r),i.replaceChild(i.adoptNode(c),i.documentElement),i.close()})}},{"./log":13}],3:[function(t,e){function n(t){this.r=0,this.g=0,this.b=0,this.a=null,this.fromArray(t)||this.namedColor(t)||this.rgb(t)||this.rgba(t)||this.hex6(t)||this.hex3(t)}n.prototype.darken=function(t){t=1-t;return new n([Math.round(this.r*t),Math.round(this.g*t),Math.round(this.b*t),this.a])},n.prototype.isTransparent=function(){return 0===this.a},n.prototype.isBlack=function(){return 0===this.r&&0===this.g&&0===this.b},n.prototype.fromArray=function(t){return Array.isArray(t)&&(this.r=Math.min(t[0],255),this.g=Math.min(t[1],255),this.b=Math.min(t[2],255),3<t.length&&(this.a=t[3])),Array.isArray(t)};var r=/^#([a-f0-9]{3})$/i,i=(n.prototype.hex3=function(t){return null!==(t=t.match(r))&&(this.r=parseInt(t[1][0]+t[1][0],16),this.g=parseInt(t[1][1]+t[1][1],16),this.b=parseInt(t[1][2]+t[1][2],16)),null!==t},/^#([a-f0-9]{6})$/i),o=(n.prototype.hex6=function(t){var e=null;return null!==(e=t.match(i))&&(this.r=parseInt(e[1].substring(0,2),16),this.g=parseInt(e[1].substring(2,4),16),this.b=parseInt(e[1].substring(4,6),16)),null!==e},/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/),s=(n.prototype.rgb=function(t){return null!==(t=t.match(o))&&(this.r=Number(t[1]),this.g=Number(t[2]),this.b=Number(t[3])),null!==t},/^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?\.?\d+)\s*\)$/),a=(n.prototype.rgba=function(t){return null!==(t=t.match(s))&&(this.r=Number(t[1]),this.g=Number(t[2]),this.b=Number(t[3]),this.a=Number(t[4])),null!==t},n.prototype.toString=function(){return null!==this.a&&1!==this.a?"rgba("+[this.r,this.g,this.b,this.a].join(",")+")":"rgb("+[this.r,this.g,this.b].join(",")+")"},n.prototype.namedColor=function(t){t=t.toLowerCase();var e=a[t];if(e)this.r=e[0],this.g=e[1],this.b=e[2];else if("transparent"===t)return!(this.r=this.g=this.b=this.a=0);return!!e},n.prototype.isColor=!0,{aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]});e.exports=n},{}],4:[function(t,e){function n(t,e){var n,r,i,o,s,a,u,c,l,h=_++;return(e=e||{}).logging&&(m.options.logging=!0,m.options.start=Date.now()),e.async=void 0===e.async||e.async,e.allowTaint=void 0!==e.allowTaint&&e.allowTaint,e.removeContainer=void 0===e.removeContainer||e.removeContainer,e.javascriptEnabled=void 0!==e.javascriptEnabled&&e.javascriptEnabled,e.imageTimeout=void 0===e.imageTimeout?1e4:e.imageTimeout,e.renderer="function"==typeof e.renderer?e.renderer:p,e.strict=!!e.strict,"string"==typeof t?"string"!=typeof e.proxy?Promise.reject("Proxy must be used when rendering url"):(n=null!=e.width?e.width:window.innerWidth,r=null!=e.height?e.height:window.innerHeight,y((o=t,(i=document.createElement("a")).href=o,i.href=i.href,i),e.proxy,document,n,r,e).then(function(t){return f(t.contentWindow.document.documentElement,t,e,n,r)})):((o=(void 0===t?[document.documentElement]:t.length?t:[t])[0]).setAttribute(w+h,h),s=o.ownerDocument,a=e,u=o.ownerDocument.defaultView.innerWidth,c=o.ownerDocument.defaultView.innerHeight,l=h,v(s,s,u,c,a,s.defaultView.pageXOffset,s.defaultView.pageYOffset).then(function(t){m("Document cloned");var e=w+l,n="["+e+"='"+l+"']",e=(s.querySelector(n).removeAttribute(e),t.contentWindow),r=e.document.querySelector(n);return Promise.resolve("function"!=typeof a.onclone||a.onclone(e.document)).then(function(){return f(r,t,a,u,c)})}).then(function(t){return"function"==typeof e.onrendered&&(m("options.onrendered is deprecated, html2canvas returns a Promise containing the canvas"),e.onrendered(t)),t}))}function f(n,r,i,t,e){var o=r.contentWindow,s=new h(o.document),a=new d(i,s),u=b(n),t="view"===i.type?t:(t=o.document,Math.max(Math.max(t.body.scrollWidth,t.documentElement.scrollWidth),Math.max(t.body.offsetWidth,t.documentElement.offsetWidth),Math.max(t.body.clientWidth,t.documentElement.clientWidth))),e="view"===i.type?e:(e=o.document,Math.max(Math.max(e.body.scrollHeight,e.documentElement.scrollHeight),Math.max(e.body.offsetHeight,e.documentElement.offsetHeight),Math.max(e.body.clientHeight,e.documentElement.clientHeight))),c=new i.renderer(t,e,a,i,document);return new g(n,c,s,a,i).ready.then(function(){var t,e;return m("Finished rendering"),t="view"===i.type?l(c.canvas,{width:c.canvas.width,height:c.canvas.height,top:0,left:0,x:0,y:0}):n===o.document.body||n===o.document.documentElement||null!=i.canvas?c.canvas:l(c.canvas,{width:(null!=i.width?i:u).width,height:(null!=i.height?i:u).height,top:u.top,left:u.left,x:0,y:0}),e=r,i.removeContainer&&(e.parentNode.removeChild(e),m("Cleaned up container")),t})}function l(t,e){var n=document.createElement("canvas"),r=Math.min(t.width-1,Math.max(0,e.left)),i=Math.min(t.width,Math.max(1,e.left+e.width)),o=Math.min(t.height-1,Math.max(0,e.top)),s=Math.min(t.height,Math.max(1,e.top+e.height)),i=(n.width=e.width,n.height=e.height,i-r),s=s-o;return m("Cropping canvas at:","left:",e.left,"top:",e.top,"width:",i,"height:",s),m("Resulting crop with width",e.width,"and height",e.height,"with x",r,"and y",o),n.getContext("2d").drawImage(t,r,o,i,s,e.x,e.y,i,s),n}var h=t("./support"),p=t("./renderers/canvas"),d=t("./imageloader"),g=t("./nodeparser"),r=t("./nodecontainer"),m=t("./log"),i=t("./utils"),v=t("./clone"),y=t("./proxy").loadUrlDocument,b=i.getBounds,w="data-html2canvas-node",_=0,t=(n.CanvasRenderer=p,n.NodeContainer=r,n.log=m,n.utils=i,"undefined"==typeof document||"function"!=typeof Object.create||"function"!=typeof document.createElement("canvas").getContext?function(){return Promise.reject("No canvas support")}:n);e.exports=t},{"./clone":2,"./imageloader":11,"./log":13,"./nodecontainer":14,"./nodeparser":15,"./proxy":16,"./renderers/canvas":20,"./support":22,"./utils":26}],5:[function(t,e){function r(t){var n;this.src=t,i("DummyImageContainer for",t),this.promise&&this.image||(i("Initiating DummyImageContainer"),r.prototype.image=new Image,n=this.image,r.prototype.promise=new Promise(function(t,e){n.onload=t,n.onerror=e,n.src=o(),!0===n.complete&&t(n)}))}var i=t("./log"),o=t("./utils").smallImage;e.exports=r},{"./log":13,"./utils":26}],6:[function(t,e){var s=t("./utils").smallImage;e.exports=function(t,e){var n=document.createElement("div"),r=document.createElement("img"),i=document.createElement("span"),o="Hidden Text";n.style.visibility="hidden",n.style.fontFamily=t,n.style.fontSize=e,n.style.margin=0,n.style.padding=0,document.body.appendChild(n),r.src=s(),r.width=1,r.height=1,r.style.margin=0,r.style.padding=0,r.style.verticalAlign="baseline",i.style.fontFamily=t,i.style.fontSize=e,i.style.margin=0,i.style.padding=0,i.appendChild(document.createTextNode(o)),n.appendChild(i),n.appendChild(r),t=r.offsetTop-i.offsetTop+1,n.removeChild(i),n.appendChild(document.createTextNode(o)),n.style.lineHeight="normal",r.style.verticalAlign="super",e=r.offsetTop-n.offsetTop+1,document.body.removeChild(n),this.baseline=t,this.lineWidth=1,this.middle=e}},{"./utils":26}],7:[function(t,e){function n(){this.data={}}var r=t("./font");n.prototype.getMetrics=function(t,e){return void 0===this.data[t+"-"+e]&&(this.data[t+"-"+e]=new r(t,e)),this.data[t+"-"+e]},e.exports=n},{"./font":6}],8:[function(o,t){function e(e,t,n){this.image=null,this.src=e;var r=this,i=s(e);this.promise=(t?new Promise(function(t){"about:blank"===e.contentWindow.document.URL||null==e.contentWindow.document.documentElement?e.contentWindow.onload=e.onload=function(){t(e)}:t(e)}):this.proxyLoad(n.proxy,i,n)).then(function(t){return o("./core")(t.contentWindow.document.documentElement,{type:"view",width:t.width,height:t.height,proxy:n.proxy,javascriptEnabled:n.javascriptEnabled,removeContainer:n.removeContainer,allowTaint:n.allowTaint,imageTimeout:n.imageTimeout/2})}).then(function(t){return r.image=t})}var s=o("./utils").getBounds,i=o("./proxy").loadUrlDocument;e.prototype.proxyLoad=function(t,e,n){var r=this.src;return i(r.src,t,r.ownerDocument,e.width,e.height,n)},t.exports=e},{"./core":4,"./proxy":16,"./utils":26}],9:[function(t,e){function n(t){this.src=t.value,this.colorStops=[],this.type=null,this.x0=.5,this.y0=.5,this.x1=.5,this.y1=.5,this.promise=Promise.resolve(!0)}n.TYPES={LINEAR:1,RADIAL:2},n.REGEXP_COLORSTOP=/^\s*(rgba?\(\s*\d{1,3},\s*\d{1,3},\s*\d{1,3}(?:,\s*[0-9\.]+)?\s*\)|[a-z]{3,20}|#[a-f0-9]{3,6})(?:\s+(\d{1,3}(?:\.\d+)?)(%|px)?)?(?:\s|$)/i,e.exports=n},{}],10:[function(t,e){e.exports=function(n,r){this.src=n,this.image=new Image;var i=this;this.tainted=null,this.promise=new Promise(function(t,e){i.image.onload=t,i.image.onerror=e,r&&(i.image.crossOrigin="anonymous"),i.image.src=n,!0===i.image.complete&&t(i.image)})}},{}],11:[function(t,e){function n(t,e){this.link=null,this.options=t,this.support=e,this.origin=this.getOrigin(window.location.href)}var o=t("./log"),r=t("./imagecontainer"),i=t("./dummyimagecontainer"),s=t("./proxyimagecontainer"),a=t("./framecontainer"),u=t("./svgcontainer"),c=t("./svgnodecontainer"),l=t("./lineargradientcontainer"),h=t("./webkitgradientcontainer"),f=t("./utils").bind;n.prototype.findImages=function(t){var e=[];return t.reduce(function(t,e){switch(e.node.nodeName){case"IMG":return t.concat([{args:[e.node.src],method:"url"}]);case"svg":case"IFRAME":return t.concat([{args:[e.node],method:e.node.nodeName}])}return t},[]).forEach(this.addImage(e,this.loadImage),this),e},n.prototype.findBackgroundImage=function(t,e){return e.parseBackgroundImages().filter(this.hasImageBackground).forEach(this.addImage(t,this.loadImage),this),t},n.prototype.addImage=function(n,r){return function(e){e.args.forEach(function(t){this.imageExists(n,t)||(n.splice(0,0,r.call(this,e)),o("Added image #"+n.length,"string"==typeof t?t.substring(0,100):t))},this)}},n.prototype.hasImageBackground=function(t){return"none"!==t.method},n.prototype.loadImage=function(t){var e;return"url"===t.method?(e=t.args[0],!this.isSVG(e)||this.support.svg||this.options.allowTaint?e.match(/data:image\/.*;base64,/i)?new r(e.replace(/url\(['"]{0,}|['"]{0,}\)$/gi,""),!1):this.isSameOrigin(e)||!0===this.options.allowTaint||this.isSVG(e)?new r(e,!1):this.support.cors&&!this.options.allowTaint&&this.options.useCORS?new r(e,!0):this.options.proxy?new s(e,this.options.proxy):new i(e):new u(e)):"linear-gradient"===t.method?new l(t):"gradient"===t.method?new h(t):"svg"===t.method?new c(t.args[0],this.support.svg):"IFRAME"===t.method?new a(t.args[0],this.isSameOrigin(t.args[0].src),this.options):new i(t)},n.prototype.isSVG=function(t){return"svg"===t.substring(t.length-3).toLowerCase()||u.prototype.isInline(t)},n.prototype.imageExists=function(t,e){return t.some(function(t){return t.src===e})},n.prototype.isSameOrigin=function(t){return this.getOrigin(t)===this.origin},n.prototype.getOrigin=function(t){var e=this.link||(this.link=document.createElement("a"));return e.href=t,e.href=e.href,e.protocol+e.hostname+e.port},n.prototype.getPromise=function(e){return this.timeout(e,this.options.imageTimeout).catch(function(){return new i(e.src).promise.then(function(t){e.image=t})})},n.prototype.get=function(e){var n=null;return this.images.some(function(t){return(n=t).src===e})?n:null},n.prototype.fetch=function(t){return this.images=t.reduce(f(this.findBackgroundImage,this),this.findImages(t)),this.images.forEach(function(e,n){e.promise.then(function(){o("Succesfully loaded image #"+(n+1),e)},function(t){o("Failed loading image #"+(n+1),e,t)})}),this.ready=Promise.all(this.images.map(this.getPromise,this)),o("Finished searching images"),this},n.prototype.timeout=function(n,r){var i,t=Promise.race([n.promise,new Promise(function(t,e){i=setTimeout(function(){o("Timed out loading image",n),e(n)},r)})]).then(function(t){return clearTimeout(i),t});return t.catch(function(){clearTimeout(i)}),t},e.exports=n},{"./dummyimagecontainer":5,"./framecontainer":8,"./imagecontainer":10,"./lineargradientcontainer":12,"./log":13,"./proxyimagecontainer":17,"./svgcontainer":23,"./svgnodecontainer":24,"./utils":26,"./webkitgradientcontainer":27}],12:[function(t,e){function n(t){r.apply(this,arguments),this.type=r.TYPES.LINEAR;var e=n.REGEXP_DIRECTION.test(t.args[0])||!r.REGEXP_COLORSTOP.test(t.args[0]);e?t.args[0].split(/\s+/).reverse().forEach(function(t,e){switch(t){case"left":this.x0=0,this.x1=1;break;case"top":this.y0=0,this.y1=1;break;case"right":this.x0=1,this.x1=0;break;case"bottom":this.y0=1,this.y1=0;break;case"to":var n=this.y0,r=this.x0;this.y0=this.y1,this.x0=this.x1,this.x1=r,this.y1=n;break;case"center":break;default:r=.01*parseFloat(t,10);isNaN(r)||(0===e?(this.y0=r,this.y1=1-this.y0):(this.x0=r,this.x1=1-this.x0))}},this):(this.y0=0,this.y1=1),this.colorStops=t.args.slice(e?1:0).map(function(t){var t=t.match(r.REGEXP_COLORSTOP),e=+t[2],n=0==e?"%":t[3];return{color:new i(t[1]),stop:"%"===n?e/100:null}}),null===this.colorStops[0].stop&&(this.colorStops[0].stop=0),null===this.colorStops[this.colorStops.length-1].stop&&(this.colorStops[this.colorStops.length-1].stop=1),this.colorStops.forEach(function(n,r){null===n.stop&&this.colorStops.slice(r).some(function(t,e){return null!==t.stop&&(n.stop=(t.stop-this.colorStops[r-1].stop)/(e+1)+this.colorStops[r-1].stop,!0)},this)},this)}var r=t("./gradientcontainer"),i=t("./color");n.prototype=Object.create(r.prototype),n.REGEXP_DIRECTION=/^\s*(?:to|left|right|top|bottom|center|\d{1,3}(?:\.\d+)?%?)(?:\s|$)/i,e.exports=n},{"./color":3,"./gradientcontainer":9}],13:[function(t,e){function n(){n.options.logging&&window.console&&window.console.log&&Function.prototype.bind.call(window.console.log,window.console).apply(window.console,[Date.now()-n.options.start+"ms","html2canvas:"].concat([].slice.call(arguments,0)))}n.options={logging:!1},e.exports=n},{}],14:[function(t,e){function n(t,e){this.node=t,this.parent=e,this.stack=null,this.bounds=null,this.borders=null,this.clip=[],this.backgroundClip=[],this.offsetBounds=null,this.visible=null,this.computedStyles=null,this.colors={},this.styles={},this.backgroundImages=null,this.transformData=null,this.transformMatrix=null,this.isPseudoElement=!1,this.opacity=null}function o(t){return-1!==t.toString().indexOf("%")}function r(t){return t.replace("px","")}function i(t){return parseFloat(t)}var s=t("./color"),t=t("./utils"),a=t.getBounds,u=t.parseBackgrounds,c=t.offsetBounds;n.prototype.cloneTo=function(t){t.visible=this.visible,t.borders=this.borders,t.bounds=this.bounds,t.clip=this.clip,t.backgroundClip=this.backgroundClip,t.computedStyles=this.computedStyles,t.styles=this.styles,t.backgroundImages=this.backgroundImages,t.opacity=this.opacity},n.prototype.getOpacity=function(){return null===this.opacity?this.opacity=this.cssFloat("opacity"):this.opacity},n.prototype.assignStack=function(t){(this.stack=t).children.push(this)},n.prototype.isElementVisible=function(){return this.node.nodeType===Node.TEXT_NODE?this.parent.visible:"none"!==this.css("display")&&"hidden"!==this.css("visibility")&&!this.node.hasAttribute("data-html2canvas-ignore")&&("INPUT"!==this.node.nodeName||"hidden"!==this.node.getAttribute("type"))},n.prototype.css=function(t){return this.computedStyles||(this.computedStyles=this.isPseudoElement?this.parent.computedStyle(this.before?":before":":after"):this.computedStyle(null)),this.styles[t]||(this.styles[t]=this.computedStyles[t])},n.prototype.prefixedCss=function(e){var n=this.css(e);return void 0===n&&["webkit","moz","ms","o"].some(function(t){return void 0!==(n=this.css(t+e.substr(0,1).toUpperCase()+e.substr(1)))},this),void 0===n?null:n},n.prototype.computedStyle=function(t){return this.node.ownerDocument.defaultView.getComputedStyle(this.node,t)},n.prototype.cssInt=function(t){t=parseInt(this.css(t),10);return isNaN(t)?0:t},n.prototype.color=function(t){return this.colors[t]||(this.colors[t]=new s(this.css(t)))},n.prototype.cssFloat=function(t){t=parseFloat(this.css(t));return isNaN(t)?0:t},n.prototype.fontWeight=function(){var t=this.css("fontWeight");switch(parseInt(t,10)){case 401:t="bold";break;case 400:t="normal"}return t},n.prototype.parseClip=function(){var t=this.css("clip").match(this.CLIP);return t?{top:parseInt(t[1],10),right:parseInt(t[2],10),bottom:parseInt(t[3],10),left:parseInt(t[4],10)}:null},n.prototype.parseBackgroundImages=function(){return this.backgroundImages||(this.backgroundImages=u(this.css("backgroundImage")))},n.prototype.cssList=function(t,e){t=(this.css(t)||"").split(",");return t=1===(t=(t=t[e||0]||t[0]||"auto").trim().split(" ")).length?[t[0],o(t[0])?"auto":t[0]]:t},n.prototype.parseBackgroundSize=function(t,e,n){var r,i,n=this.cssList("backgroundSize",n);if(o(n[0]))r=t.width*parseFloat(n[0])/100;else{if(/contain|cover/.test(n[0]))return t.width/t.height<(i=e.width/e.height)^"contain"===n[0]?{width:t.height*i,height:t.height}:{width:t.width,height:t.width/i};r=parseInt(n[0],10)}return i="auto"===n[0]&&"auto"===n[1]?e.height:"auto"===n[1]?r/e.width*e.height:o(n[1])?t.height*parseFloat(n[1])/100:parseInt(n[1],10),{width:r="auto"===n[0]?i/e.height*e.width:r,height:i}},n.prototype.parseBackgroundPosition=function(t,e,n,r){var n=this.cssList("backgroundPosition",n),i=o(n[0])?(t.width-(r||e).width)*(parseFloat(n[0])/100):parseInt(n[0],10),t="auto"===n[1]?i/e.width*e.height:o(n[1])?(t.height-(r||e).height)*parseFloat(n[1])/100:parseInt(n[1],10);return{left:i="auto"===n[0]?t/e.height*e.width:i,top:t}},n.prototype.parseBackgroundRepeat=function(t){return this.cssList("backgroundRepeat",t)[0]},n.prototype.parseTextShadows=function(){var t=this.css("textShadow"),e=[];if(t&&"none"!==t)for(var n=t.match(this.TEXT_SHADOW_PROPERTY),r=0;n&&r<n.length;r++){var i=n[r].match(this.TEXT_SHADOW_VALUES);e.push({color:new s(i[0]),offsetX:i[1]?parseFloat(i[1].replace("px","")):0,offsetY:i[2]?parseFloat(i[2].replace("px","")):0,blur:i[3]?i[3].replace("px",""):0})}return e},n.prototype.parseTransform=function(){var t,e;return this.transformData||(this.hasTransform()?(t=this.parseBounds(),(e=this.prefixedCss("transformOrigin").split(" ").map(r).map(i))[0]+=t.left,e[1]+=t.top,this.transformData={origin:e,matrix:this.parseTransformMatrix()}):this.transformData={origin:[0,0],matrix:[1,0,0,1,0,0]}),this.transformData},n.prototype.parseTransformMatrix=function(){var t;return this.transformMatrix||(t=(t=this.prefixedCss("transform"))?(t=t.match(this.MATRIX_PROPERTY))&&"matrix"===t[1]?t[2].split(",").map(function(t){return parseFloat(t.trim())}):t&&"matrix3d"===t[1]?[(t=t[2].split(",").map(function(t){return parseFloat(t.trim())}))[0],t[1],t[4],t[5],t[12],t[13]]:void 0:null,this.transformMatrix=t||[1,0,0,1,0,0]),this.transformMatrix},n.prototype.parseBounds=function(){return this.bounds||(this.bounds=(this.hasTransform()?c:a)(this.node))},n.prototype.hasTransform=function(){return"1,0,0,1,0,0"!==this.parseTransformMatrix().join(",")||this.parent&&this.parent.hasTransform()},n.prototype.getValue=function(){var t,e=this.node.value||"";return"SELECT"===this.node.tagName?e=(t=(t=this.node).options[t.selectedIndex||0])&&t.text||"":"password"===this.node.type&&(e=Array(e.length+1).join("•")),0===e.length?this.node.placeholder||"":e},n.prototype.MATRIX_PROPERTY=/(matrix|matrix3d)\((.+)\)/,n.prototype.TEXT_SHADOW_PROPERTY=/((rgba|rgb)\([^\)]+\)(\s-?\d+px){0,})/g,n.prototype.TEXT_SHADOW_VALUES=/(-?\d+px)|(#.+)|(rgb\(.+\))|(rgba\(.+\))/g,n.prototype.CLIP=/^rect\((\d+)px,? (\d+)px,? (\d+)px,? (\d+)px\)$/,e.exports=n},{"./color":3,"./utils":26}],15:[function(t,e){function n(t,e,n,r,i){E("Starting NodeParser"),this.renderer=e,this.options=i,this.range=null,this.support=n,this.renderQueue=[],this.stack=new N(!0,1,t.ownerDocument,null);var o,n=new A(t,null);i.background&&e.rectangle(0,0,e.width,e.height,new P(i.background)),t===t.ownerDocument.documentElement&&(o=new A(n.color("backgroundColor").isTransparent()?t.ownerDocument.body:t.ownerDocument.documentElement,null),e.rectangle(0,0,e.width,e.height,o.color("backgroundColor"))),n.visibile=n.isElementVisible(),this.createPseudoHideStyles(t.ownerDocument),this.disableAnimations(t.ownerDocument),this.nodes=O([n].concat(this.getChildren(n)).filter(function(t){return t.visible=t.isElementVisible()}).map(this.getPseudoElements,this)),this.fontMetrics=new W,E("Fetched nodes, total:",this.nodes.length),E("Calculate overflow clips"),this.calculateOverflowClips(),E("Start fetching images"),this.images=r.fetch(this.nodes.filter(x)),this.ready=this.images.ready.then(D(function(){return E("Images loaded, starting parsing"),E("Creating stacking contexts"),this.createStackingContexts(),E("Sorting stacking contexts"),this.sortStackingContexts(this.stack),this.parse(this.stack),E("Render queue created with "+this.renderQueue.length+" items"),new Promise(D(function(t){i.async?"function"==typeof i.async?i.async.call(this,this.renderQueue,t):0<this.renderQueue.length?(this.renderIndex=0,this.asyncRenderer(this.renderQueue,t)):t():(this.renderQueue.forEach(this.paint,this),t())},this))},this))}function r(t){return t.parent&&t.parent.clip.length}function a(){}function u(s,a,u,c){return s.map(function(t,e){if(0<t.width){var n=a.left,r=a.top,i=a.width,o=a.height-s[2].width;switch(e){case 0:o=s[0].width,t.args=h({c1:[n,r],c2:[n+i,r],c3:[n+i-s[1].width,r+o],c4:[n+s[3].width,r+o]},c[0],c[1],u.topLeftOuter,u.topLeftInner,u.topRightOuter,u.topRightInner);break;case 1:n=a.left+a.width-s[1].width,i=s[1].width,t.args=h({c1:[n+i,r],c2:[n+i,r+o+s[2].width],c3:[n,r+o],c4:[n,r+s[0].width]},c[1],c[2],u.topRightOuter,u.topRightInner,u.bottomRightOuter,u.bottomRightInner);break;case 2:r=r+a.height-s[2].width,o=s[2].width,t.args=h({c1:[n+i,r+o],c2:[n,r+o],c3:[n+s[3].width,r],c4:[n+i-s[3].width,r]},c[2],c[3],u.bottomRightOuter,u.bottomRightInner,u.bottomLeftOuter,u.bottomLeftInner);break;case 3:i=s[3].width,t.args=h({c1:[n,r+o+s[2].width],c2:[n,r],c3:[n+i,r+s[0].width],c4:[n+i,r+o]},c[3],c[0],u.bottomLeftOuter,u.bottomLeftInner,u.topLeftOuter,u.topLeftInner)}}return t})}function v(t,e,n,r){var i=(Math.sqrt(2)-1)/3*4,o=n*i,i=r*i,n=t+n,r=e+r;return{topLeft:l({x:t,y:r},{x:t,y:r-i},{x:n-o,y:e},{x:n,y:e}),topRight:l({x:t,y:e},{x:t+o,y:e},{x:n,y:r-i},{x:n,y:r}),bottomRight:l({x:n,y:e},{x:n,y:e+i},{x:t+o,y:r},{x:t,y:r}),bottomLeft:l({x:n,y:r},{x:n-o,y:r},{x:t,y:e+i},{x:t,y:e})}}function c(t,e,n){var r=t.left,i=t.top,o=t.width,t=t.height,s=e[0][0]<o/2?e[0][0]:o/2,a=e[0][1]<t/2?e[0][1]:t/2,u=e[1][0]<o/2?e[1][0]:o/2,c=e[1][1]<t/2?e[1][1]:t/2,l=e[2][0]<o/2?e[2][0]:o/2,h=e[2][1]<t/2?e[2][1]:t/2,f=e[3][0]<o/2?e[3][0]:o/2,e=e[3][1]<t/2?e[3][1]:t/2,p=o-u,d=t-h,g=o-l,m=t-e;return{topLeftOuter:v(r,i,s,a).topLeft.subdivide(.5),topLeftInner:v(r+n[3].width,i+n[0].width,Math.max(0,s-n[3].width),Math.max(0,a-n[0].width)).topLeft.subdivide(.5),topRightOuter:v(r+p,i,u,c).topRight.subdivide(.5),topRightInner:v(r+Math.min(p,o+n[3].width),i+n[0].width,p>o+n[3].width?0:u-n[3].width,c-n[0].width).topRight.subdivide(.5),bottomRightOuter:v(r+g,i+d,l,h).bottomRight.subdivide(.5),bottomRightInner:v(r+Math.min(g,o-n[3].width),i+Math.min(d,t+n[0].width),Math.max(0,l-n[1].width),h-n[2].width).bottomRight.subdivide(.5),bottomLeftOuter:v(r,i+m,f,e).bottomLeft.subdivide(.5),bottomLeftInner:v(r+n[3].width,i+m,Math.max(0,f-n[3].width),e-n[2].width).bottomLeft.subdivide(.5)}}function l(o,s,a,u){function c(t,e,n){return{x:t.x+(e.x-t.x)*n,y:t.y+(e.y-t.y)*n}}return{start:o,startControl:s,endControl:a,end:u,subdivide:function(t){var e=c(o,s,t),n=c(s,a,t),r=c(a,u,t),i=c(e,n,t),n=c(n,r,t),t=c(i,n,t);return[l(o,e,i,t),l(t,n,r,u)]},curveTo:function(t){t.push(["bezierCurve",s.x,s.y,a.x,a.y,u.x,u.y])},curveToReversed:function(t){t.push(["bezierCurve",a.x,a.y,s.x,s.y,o.x,o.y])}}}function h(t,e,n,r,i,o,s){var a=[];return 0<e[0]||0<e[1]?(a.push(["line",r[1].start.x,r[1].start.y]),r[1].curveTo(a)):a.push(["line",t.c1[0],t.c1[1]]),0<n[0]||0<n[1]?(a.push(["line",o[0].start.x,o[0].start.y]),o[0].curveTo(a),a.push(["line",s[0].end.x,s[0].end.y]),s[0].curveToReversed(a)):(a.push(["line",t.c2[0],t.c2[1]]),a.push(["line",t.c3[0],t.c3[1]])),0<e[0]||0<e[1]?(a.push(["line",i[1].end.x,i[1].end.y]),i[1].curveToReversed(a)):a.push(["line",t.c4[0],t.c4[1]]),a}function s(t,e,n,r,i,o,s){0<e[0]||0<e[1]?(t.push(["line",r[0].start.x,r[0].start.y]),r[0].curveTo(t),r[1].curveTo(t)):t.push(["line",o,s]),(0<n[0]||0<n[1])&&t.push(["line",i[0].start.x,i[0].start.y])}function f(t){return t.cssInt("zIndex")<0}function p(t){return 0<t.cssInt("zIndex")}function d(t){return 0===t.cssInt("zIndex")}function g(t){return-1!==["inline","inline-block","inline-table"].indexOf(t.css("display"))}function m(t){return t instanceof N}function y(t){return 0<t.node.data.trim().length}function i(t){return t.nodeType===Node.TEXT_NODE||t.nodeType===Node.ELEMENT_NODE}function b(t){return"static"!==t.css("position")}function w(t){return"none"!==t.css("float")}function _(t){var e=this;return function(){return!t.apply(e,arguments)}}function x(t){return t.node.nodeType===Node.ELEMENT_NODE}function o(t){return!0===t.isPseudoElement}function C(t){return t.node.nodeType===Node.TEXT_NODE}function k(t){return parseInt(t,10)}function S(t){return t.width}function M(t){return t.node.nodeType!==Node.ELEMENT_NODE||-1===["SCRIPT","HEAD","TITLE","OBJECT","BR","OPTION"].indexOf(t.node.nodeName)}function O(t){return[].concat.apply([],t)}var E=t("./log"),T=t("punycode"),A=t("./nodecontainer"),I=t("./textcontainer"),j=t("./pseudoelementcontainer"),W=t("./fontmetrics"),P=t("./color"),N=t("./stackingcontext"),t=t("./utils"),D=t.bind,L=t.getBounds,F=t.parseBackgrounds,H=t.offsetBounds,R=(n.prototype.calculateOverflowClips=function(){this.nodes.forEach(function(t){var e,n;x(t)?(o(t)&&t.appendToDOM(),t.borders=this.parseBorders(t),e="hidden"===t.css("overflow")?[t.borders.clip]:[],(n=t.parseClip())&&-1!==["absolute","fixed"].indexOf(t.css("position"))&&e.push([["rect",t.bounds.left+n.left,t.bounds.top+n.top,n.right-n.left,n.bottom-n.top]]),t.clip=r(t)?t.parent.clip.concat(e):e,t.backgroundClip="hidden"!==t.css("overflow")?t.clip.concat([t.borders.clip]):t.clip,o(t)&&t.cleanDOM()):C(t)&&(t.clip=r(t)?t.parent.clip:[]),o(t)||(t.bounds=null)},this)},n.prototype.asyncRenderer=function(t,e,n){n=n||Date.now(),this.paint(t[this.renderIndex++]),t.length===this.renderIndex?e():n+20>Date.now()?this.asyncRenderer(t,e,n):setTimeout(D(function(){this.asyncRenderer(t,e)},this),0)},n.prototype.createPseudoHideStyles=function(t){this.createStyles(t,"."+j.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+':before { content: "" !important; display: none !important; }.'+j.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER+':after { content: "" !important; display: none !important; }')},n.prototype.disableAnimations=function(t){this.createStyles(t,"* { -webkit-animation: none !important; -moz-animation: none !important; -o-animation: none !important; animation: none !important; -webkit-transition: none !important; -moz-transition: none !important; -o-transition: none !important; transition: none !important;}")},n.prototype.createStyles=function(t,e){var n=t.createElement("style");n.innerHTML=e,t.body.appendChild(n)},n.prototype.getPseudoElements=function(t){var e,n=[[t]];return t.node.nodeType===Node.ELEMENT_NODE&&(e=this.getPseudoElement(t,":before"),t=this.getPseudoElement(t,":after"),e&&n.push(e),t&&n.push(t)),O(n)},n.prototype.getPseudoElement=function(t,e){var n=t.computedStyle(e);if(!n||!n.content||"none"===n.content||"-moz-alt-content"===n.content||"none"===n.display)return null;i=n.content;for(var r=(r=i.substr(0,1))===i.substr(i.length-1)&&r.match(/'|"/)?i.substr(1,i.length-2):i,i="url"===r.substr(0,3),o=document.createElement(i?"img":"html2canvaspseudoelement"),t=new j(o,t,e),s=n.length-1;0<=s;s--){var a=n.item(s).replace(/(\-[a-z])/g,function(t){return t.toUpperCase().replace("-","")});o.style[a]=n[a]}return o.className=j.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+" "+j.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER,i?(o.src=F(r)[0].args[0],[t]):(e=document.createTextNode(r),o.appendChild(e),[t,new I(e,t)])},n.prototype.getChildren=function(n){return O([].filter.call(n.node.childNodes,i).map(function(t){var e=[new(t.nodeType===Node.TEXT_NODE?I:A)(t,n)].filter(M);return t.nodeType===Node.ELEMENT_NODE&&e.length&&"TEXTAREA"!==t.tagName?e[0].isElementVisible()?e.concat(this.getChildren(e[0])):[]:e},this))},n.prototype.newStackingContext=function(t,e){var n=new N(e,t.getOpacity(),t.node,t.parent);t.cloneTo(n),(e?n.getParentStack(this):n.parent.stack).contexts.push(n),t.stack=n},n.prototype.createStackingContexts=function(){this.nodes.forEach(function(t){var e,n;x(t)&&(this.isRootElement(t)||t.getOpacity()<1||(n=(e=t).css("position"),"auto"!==(-1!==["absolute","relative","fixed"].indexOf(n)?e.css("zIndex"):"auto"))||this.isBodyWithTransparentRoot(t)||t.hasTransform())?this.newStackingContext(t,!0):x(t)&&(b(t)&&d(t)||-1!==["inline-block","inline-table"].indexOf(t.css("display"))||w(t))?this.newStackingContext(t,!1):t.assignStack(t.parent.stack)},this)},n.prototype.isBodyWithTransparentRoot=function(t){return"BODY"===t.node.nodeName&&t.parent.color("backgroundColor").isTransparent()},n.prototype.isRootElement=function(t){return null===t.parent},n.prototype.sortStackingContexts=function(t){var n;t.contexts.sort((n=t.contexts.slice(0),function(t,e){return t.cssInt("zIndex")+n.indexOf(t)/n.length-(e.cssInt("zIndex")+n.indexOf(e)/n.length)})),t.contexts.forEach(this.sortStackingContexts,this)},n.prototype.parseTextBounds=function(r){return function(t,e,n){if("none"!==r.parent.css("textDecoration").substr(0,4)||0!==t.trim().length){if(this.support.rangeBounds&&!r.parent.hasTransform())return n=n.slice(0,e).join("").length,this.getRangeBounds(r.node,n,t.length);if(r.node&&"string"==typeof r.node.data)return e=r.node.splitText(t.length),n=this.getWrapperBounds(r.node,r.parent.hasTransform()),r.node=e,n}else this.support.rangeBounds&&!r.parent.hasTransform()||(r.node=r.node.splitText(t.length));return{}}},n.prototype.getWrapperBounds=function(t,e){var n=t.ownerDocument.createElement("html2canvaswrapper"),r=t.parentNode,i=t.cloneNode(!0),t=(n.appendChild(t.cloneNode(!0)),r.replaceChild(n,t),(e?H:L)(n));return r.replaceChild(i,n),t},n.prototype.getRangeBounds=function(t,e,n){var r=this.range||(this.range=t.ownerDocument.createRange());return r.setStart(t,e),r.setEnd(t,e+n),r.getBoundingClientRect()},n.prototype.parse=function(t){var e=t.contexts.filter(f),n=t.children.filter(x),r=n.filter(_(w)),i=r.filter(_(b)).filter(_(g)),n=n.filter(_(b)).filter(w),o=r.filter(_(b)).filter(g),r=t.contexts.concat(r.filter(b)).filter(d),s=t.children.filter(C).filter(y),t=t.contexts.filter(p);e.concat(i).concat(n).concat(o).concat(r).concat(s).concat(t).forEach(function(t){this.renderQueue.push(t),m(t)&&(this.parse(t),this.renderQueue.push(new a))},this)},n.prototype.paint=function(t){try{t instanceof a?this.renderer.ctx.restore():C(t)?(o(t.parent)&&t.parent.appendToDOM(),this.paintText(t),o(t.parent)&&t.parent.cleanDOM()):this.paintNode(t)}catch(t){if(E(t),this.options.strict)throw t}},n.prototype.paintNode=function(t){m(t)&&(this.renderer.setOpacity(t.opacity),this.renderer.ctx.save(),t.hasTransform()&&this.renderer.setTransform(t.parseTransform())),"INPUT"===t.node.nodeName&&"checkbox"===t.node.type?this.paintCheckbox(t):"INPUT"===t.node.nodeName&&"radio"===t.node.type?this.paintRadio(t):this.paintElement(t)},n.prototype.paintElement=function(e){var n=e.parseBounds();this.renderer.clip(e.backgroundClip,function(){this.renderer.renderBackground(e,n,e.borders.borders.map(S))},this),this.renderer.clip(e.clip,function(){this.renderer.renderBorders(e.borders.borders)},this),this.renderer.clip(e.backgroundClip,function(){switch(e.node.nodeName){case"svg":case"IFRAME":var t=this.images.get(e.node);t?this.renderer.renderImage(e,n,e.borders,t):E("Error loading <"+e.node.nodeName+">",e.node);break;case"IMG":t=this.images.get(e.node.src);t?this.renderer.renderImage(e,n,e.borders,t):E("Error loading <img>",e.node.src);break;case"CANVAS":this.renderer.renderImage(e,n,e.borders,{image:e.node});break;case"SELECT":case"INPUT":case"TEXTAREA":this.paintFormValue(e)}},this)},n.prototype.paintCheckbox=function(t){var e=t.parseBounds(),n=Math.min(e.width,e.height),r={width:n-1,height:n-1,top:e.top,left:e.left},e=[3,3],i=[e,e,e,e],o=[1,1,1,1].map(function(t){return{color:new P("#A5A5A5"),width:t}}),s=c(r,i,o);this.renderer.clip(t.backgroundClip,function(){this.renderer.rectangle(r.left+1,r.top+1,r.width-2,r.height-2,new P("#DEDEDE")),this.renderer.renderBorders(u(o,r,s,i)),t.node.checked&&(this.renderer.font(new P("#424242"),"normal","normal","bold",n-3+"px","arial"),this.renderer.text("✔",r.left+n/6,r.top+n-1))},this)},n.prototype.paintRadio=function(t){var e=t.parseBounds(),n=Math.min(e.width,e.height)-2;this.renderer.clip(t.backgroundClip,function(){this.renderer.circleStroke(e.left+1,e.top+1,n,new P("#DEDEDE"),1,new P("#A5A5A5")),t.node.checked&&this.renderer.circle(Math.ceil(e.left+n/4)+1,Math.ceil(e.top+n/4)+1,Math.floor(n/2),new P("#424242"))},this)},n.prototype.paintFormValue=function(e){var t,n,r,i=e.getValue();0<i.length&&(t=e.node.ownerDocument,n=t.createElement("html2canvaswrapper"),["lineHeight","textAlign","fontFamily","fontWeight","fontSize","color","paddingLeft","paddingTop","paddingRight","paddingBottom","width","height","borderLeftStyle","borderTopStyle","borderLeftWidth","borderTopWidth","boxSizing","whiteSpace","wordWrap"].forEach(function(t){try{n.style[t]=e.css(t)}catch(t){E("html2canvas: Parse: Exception caught in renderFormValue: "+t.message)}}),r=e.parseBounds(),n.style.position="fixed",n.style.left=r.left+"px",n.style.top=r.top+"px",n.textContent=i,t.body.appendChild(n),this.paintText(new I(n.firstChild,e)),t.body.removeChild(n))},n.prototype.paintText=function(n){n.applyTextTransform();var t=T.ucs2.decode(n.node.data),r=this.options.letterRendering&&!/^(normal|none|0px)$/.test(n.parent.css("letterSpacing"))||/[^\u0000-\u00ff]/.test(n.node.data)?t.map(function(t){return T.ucs2.encode([t])}):function(t){for(var e,n=[],r=0,i=!1;t.length;)-1!==[32,13,10,9,45].indexOf(t[r])===i?((e=t.splice(0,r)).length&&n.push(T.ucs2.encode(e)),i=!i,r=0):r++,r>=t.length&&((e=t.splice(0,r)).length&&n.push(T.ucs2.encode(e)));return n}(t),t=n.parent.fontWeight(),i=n.parent.css("fontSize"),o=n.parent.css("fontFamily"),e=n.parent.parseTextShadows();this.renderer.font(n.parent.color("color"),n.parent.css("fontStyle"),n.parent.css("fontVariant"),t,i,o),e.length?this.renderer.fontShadow(e[0].color,e[0].offsetX,e[0].offsetY,e[0].blur):this.renderer.clearShadow(),this.renderer.clip(n.parent.clip,function(){r.map(this.parseTextBounds(n),this).forEach(function(t,e){t&&(this.renderer.text(r[e],t.left,t.bottom),this.renderTextDecoration(n.parent,t,this.fontMetrics.getMetrics(o,i)))},this)},this)},n.prototype.renderTextDecoration=function(t,e,n){switch(t.css("textDecoration").split(" ")[0]){case"underline":this.renderer.rectangle(e.left,Math.round(e.top+n.baseline+n.lineWidth),e.width,1,t.color("color"));break;case"overline":this.renderer.rectangle(e.left,Math.round(e.top),e.width,1,t.color("color"));break;case"line-through":this.renderer.rectangle(e.left,Math.ceil(e.top+n.middle+n.lineWidth),e.width,1,t.color("color"))}},{inset:[["darken",.6],["darken",.1],["darken",.1],["darken",.6]]});n.prototype.parseBorders=function(i){var e,t=i.parseBounds(),n=(e=i,["TopLeft","TopRight","BottomRight","BottomLeft"].map(function(t){t=e.css("border"+t+"Radius").split(" ");return t.length<=1&&(t[1]=t[0]),t.map(k)})),r=["Top","Right","Bottom","Left"].map(function(t,e){var n=i.css("border"+t+"Style"),r=i.color("border"+t+"Color"),n=("inset"===n&&r.isBlack()&&(r=new P([255,255,255,r.a])),R[n]?R[n][e]:null);return{width:i.cssInt("border"+t+"Width"),color:n?r[n[0]](n[1]):r,args:null}}),o=c(t,n,r);return{clip:this.parseBackgroundClip(i,o,r,n,t),borders:u(r,t,o,n)}},n.prototype.parseBackgroundClip=function(t,e,n,r,i){var o=[];switch(t.css("backgroundClip")){case"content-box":case"padding-box":s(o,r[0],r[1],e.topLeftInner,e.topRightInner,i.left+n[3].width,i.top+n[0].width),s(o,r[1],r[2],e.topRightInner,e.bottomRightInner,i.left+i.width-n[1].width,i.top+n[0].width),s(o,r[2],r[3],e.bottomRightInner,e.bottomLeftInner,i.left+i.width-n[1].width,i.top+i.height-n[2].width),s(o,r[3],r[0],e.bottomLeftInner,e.topLeftInner,i.left+n[3].width,i.top+i.height-n[2].width);break;default:s(o,r[0],r[1],e.topLeftOuter,e.topRightOuter,i.left,i.top),s(o,r[1],r[2],e.topRightOuter,e.bottomRightOuter,i.left+i.width,i.top),s(o,r[2],r[3],e.bottomRightOuter,e.bottomLeftOuter,i.left+i.width,i.top+i.height),s(o,r[3],r[0],e.bottomLeftOuter,e.topLeftOuter,i.left,i.top+i.height)}return o},e.exports=n},{"./color":3,"./fontmetrics":7,"./log":13,"./nodecontainer":14,"./pseudoelementcontainer":18,"./stackingcontext":21,"./textcontainer":25,"./utils":26,punycode:1}],16:[function(t,e,n){function a(t,e,n){var r="withCredentials"in new XMLHttpRequest;return e?(t=u(e,t,e=s(r)),r?i(t):o(n,t,e).then(function(t){return h(t.content)})):Promise.reject("No proxy configured")}function o(i,o,s){return new Promise(function(e,n){function r(){delete window.html2canvas.proxy[s],i.body.removeChild(t)}var t=i.createElement("script");window.html2canvas.proxy[s]=function(t){r(),e(t)},t.src=o,t.onerror=function(t){r(),n(t)},i.body.appendChild(t)})}function s(t){return t?"":"html2canvas_"+Date.now()+"_"+ ++f+"_"+Math.round(1e5*Math.random())}function u(t,e,n){return t+"?url="+encodeURIComponent(e)+(n.length?"&callback=html2canvas.proxy."+n:"")}var i=t("./xhr"),r=t("./utils"),c=t("./log"),l=t("./clone"),h=r.decode64,f=0;n.Proxy=a,n.ProxyURL=function(t,e,n){var r="crossOrigin"in new Image,i=s(r),e=u(e,t,i);return r?Promise.resolve(e):o(n,e,i).then(function(t){return"data:"+t.type+";base64,"+t.content})},n.loadUrlDocument=function(t,e,n,r,i,o){return new a(t,e,window.document).then((s=t,function(e){var n,t=new DOMParser;try{n=t.parseFromString(e,"text/html")}catch(t){c("DOMParser not supported, falling back to createHTMLDocument"),n=document.implementation.createHTMLDocument("");try{n.open(),n.write(e),n.close()}catch(t){c("createHTMLDocument write not supported, falling back to document.body.innerHTML"),n.body.innerHTML=e}}t=n.querySelector("base");return t&&t.href.host||((e=n.createElement("base")).href=s,n.head.insertBefore(e,n.head.firstChild)),n})).then(function(t){return l(t,n,r,i,o,0,0)});var s}},{"./clone":2,"./log":13,"./utils":26,"./xhr":28}],17:[function(t,e){var o=t("./proxy").ProxyURL;e.exports=function(n,r){var t=document.createElement("a"),i=(t.href=n,n=t.href,this.src=n,this.image=new Image,this);this.promise=new Promise(function(t,e){i.image.crossOrigin="Anonymous",i.image.onload=t,i.image.onerror=e,new o(n,r,document).then(function(t){i.image.src=t}).catch(e)})}},{"./proxy":16}],18:[function(t,e){function n(t,e,n){r.call(this,t,e),this.isPseudoElement=!0,this.before=":before"===n}var r=t("./nodecontainer");n.prototype.cloneTo=function(t){n.prototype.cloneTo.call(this,t),t.isPseudoElement=!0,t.before=this.before},(n.prototype=Object.create(r.prototype)).appendToDOM=function(){this.before?this.parent.node.insertBefore(this.node,this.parent.node.firstChild):this.parent.node.appendChild(this.node),this.parent.node.className+=" "+this.getHideClass()},n.prototype.cleanDOM=function(){this.node.parentNode.removeChild(this.node),this.parent.node.className=this.parent.node.className.replace(this.getHideClass(),"")},n.prototype.getHideClass=function(){return this["PSEUDO_HIDE_ELEMENT_CLASS_"+(this.before?"BEFORE":"AFTER")]},n.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE="___html2canvas___pseudoelement_before",n.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER="___html2canvas___pseudoelement_after",e.exports=n},{"./nodecontainer":14}],19:[function(t,e){function n(t,e,n,r,i){this.width=t,this.height=e,this.images=n,this.options=r,this.document=i}var a=t("./log");n.prototype.renderImage=function(t,e,n,r){var i=t.cssInt("paddingLeft"),o=t.cssInt("paddingTop"),s=t.cssInt("paddingRight"),t=t.cssInt("paddingBottom"),n=n.borders,s=e.width-(n[1].width+n[3].width+i+s),t=e.height-(n[0].width+n[2].width+o+t);this.drawImage(r,0,0,r.image.width||s,r.image.height||t,e.left+i+n[3].width,e.top+o+n[0].width,s,t)},n.prototype.renderBackground=function(t,e,n){0<e.height&&0<e.width&&(this.renderBackgroundColor(t,e),this.renderBackgroundImage(t,e,n))},n.prototype.renderBackgroundColor=function(t,e){t=t.color("backgroundColor");t.isTransparent()||this.rectangle(e.left,e.top,e.width,e.height,t)},n.prototype.renderBorders=function(t){t.forEach(this.renderBorder,this)},n.prototype.renderBorder=function(t){t.color.isTransparent()||null===t.args||this.drawShape(t.args,t.color)},n.prototype.renderBackgroundImage=function(i,o,s){i.parseBackgroundImages().reverse().forEach(function(t,e,n){switch(t.method){case"url":var r=this.images.get(t.args[0]);r?this.renderBackgroundRepeating(i,o,r,n.length-(e+1),s):a("Error loading background-image",t.args[0]);break;case"linear-gradient":case"gradient":r=this.images.get(t.value);r?this.renderBackgroundGradient(r,o,s):a("Error loading background-image",t.args[0]);break;case"none":break;default:a("Unknown background-image type",t.args[0])}},this)},n.prototype.renderBackgroundRepeating=function(t,e,n,r,i){var o=t.parseBackgroundSize(e,n.image,r),s=t.parseBackgroundPosition(e,n.image,r,o);switch(t.parseBackgroundRepeat(r)){case"repeat-x":case"repeat no-repeat":this.backgroundRepeatShape(n,s,o,e,e.left+i[3],e.top+s.top+i[0],99999,o.height,i);break;case"repeat-y":case"no-repeat repeat":this.backgroundRepeatShape(n,s,o,e,e.left+s.left+i[3],e.top+i[0],o.width,99999,i);break;case"no-repeat":this.backgroundRepeatShape(n,s,o,e,e.left+s.left+i[3],e.top+s.top+i[0],o.width,o.height,i);break;default:this.renderBackgroundRepeat(n,s,o,{top:e.top,left:e.left},i[3],i[0])}},e.exports=n},{"./log":13}],20:[function(t,e){function n(t,e){i.apply(this,arguments),this.canvas=this.options.canvas||this.document.createElement("canvas"),this.options.canvas||(this.canvas.width=t,this.canvas.height=e),this.ctx=this.canvas.getContext("2d"),this.taintCtx=this.document.createElement("canvas").getContext("2d"),this.ctx.textBaseline="bottom",this.variables={},s("Initialized CanvasRenderer with size",t,"x",e)}function r(t){return 0<t.length}var i=t("../renderer"),o=t("../lineargradientcontainer"),s=t("../log");(n.prototype=Object.create(i.prototype)).setFillStyle=function(t){return this.ctx.fillStyle="object"==typeof t&&t.isColor?t.toString():t,this.ctx},n.prototype.rectangle=function(t,e,n,r,i){this.setFillStyle(i).fillRect(t,e,n,r)},n.prototype.circle=function(t,e,n,r){this.setFillStyle(r),this.ctx.beginPath(),this.ctx.arc(t+n/2,e+n/2,n/2,0,2*Math.PI,!0),this.ctx.closePath(),this.ctx.fill()},n.prototype.circleStroke=function(t,e,n,r,i,o){this.circle(t,e,n,r),this.ctx.strokeStyle=o.toString(),this.ctx.stroke()},n.prototype.drawShape=function(t,e){this.shape(t),this.setFillStyle(e).fill()},n.prototype.taints=function(e){if(null===e.tainted){this.taintCtx.drawImage(e.image,0,0);try{this.taintCtx.getImageData(0,0,1,1),e.tainted=!1}catch(t){this.taintCtx=document.createElement("canvas").getContext("2d"),e.tainted=!0}}return e.tainted},n.prototype.drawImage=function(t,e,n,r,i,o,s,a,u){this.taints(t)&&!this.options.allowTaint||this.ctx.drawImage(t.image,e,n,r,i,o,s,a,u)},n.prototype.clip=function(t,e,n){this.ctx.save(),t.filter(r).forEach(function(t){this.shape(t).clip()},this),e.call(n),this.ctx.restore()},n.prototype.shape=function(t){return this.ctx.beginPath(),t.forEach(function(t,e){("rect"===t[0]?this.ctx.rect:this.ctx[0===e?"moveTo":t[0]+"To"]).apply(this.ctx,t.slice(1))},this),this.ctx.closePath(),this.ctx},n.prototype.font=function(t,e,n,r,i,o){this.setFillStyle(t).font=[e,n,r,i,o].join(" ").split(",")[0]},n.prototype.fontShadow=function(t,e,n,r){this.setVariable("shadowColor",t.toString()).setVariable("shadowOffsetY",e).setVariable("shadowOffsetX",n).setVariable("shadowBlur",r)},n.prototype.clearShadow=function(){this.setVariable("shadowColor","rgba(0,0,0,0)")},n.prototype.setOpacity=function(t){this.ctx.globalAlpha=t},n.prototype.setTransform=function(t){this.ctx.translate(t.origin[0],t.origin[1]),this.ctx.transform.apply(this.ctx,t.matrix),this.ctx.translate(-t.origin[0],-t.origin[1])},n.prototype.setVariable=function(t,e){return this.variables[t]!==e&&(this.variables[t]=this.ctx[t]=e),this},n.prototype.text=function(t,e,n){this.ctx.fillText(t,e,n)},n.prototype.backgroundRepeatShape=function(t,e,n,r,i,o,s,a,u){s=[["line",Math.round(i),Math.round(o)],["line",Math.round(i+s),Math.round(o)],["line",Math.round(i+s),Math.round(a+o)],["line",Math.round(i),Math.round(a+o)]];this.clip([s],function(){this.renderBackgroundRepeat(t,e,n,r,u[3],u[0])},this)},n.prototype.renderBackgroundRepeat=function(t,e,n,r,i,o){i=Math.round(r.left+e.left+i),r=Math.round(r.top+e.top+o);this.setFillStyle(this.ctx.createPattern(this.resizeImage(t,n),"repeat")),this.ctx.translate(i,r),this.ctx.fill(),this.ctx.translate(-i,-r)},n.prototype.renderBackgroundGradient=function(t,e){var n;t instanceof o&&(n=this.ctx.createLinearGradient(e.left+e.width*t.x0,e.top+e.height*t.y0,e.left+e.width*t.x1,e.top+e.height*t.y1),t.colorStops.forEach(function(t){n.addColorStop(t.stop,t.color.toString())}),this.rectangle(e.left,e.top,e.width,e.height,n))},n.prototype.resizeImage=function(t,e){var n,t=t.image;return t.width===e.width&&t.height===e.height?t:((n=document.createElement("canvas")).width=e.width,n.height=e.height,n.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,e.width,e.height),n)},e.exports=n},{"../lineargradientcontainer":12,"../log":13,"../renderer":19}],21:[function(t,e){function n(t,e,n,r){i.call(this,n,r),this.ownStacking=t,this.contexts=[],this.children=[],this.opacity=(this.parent?this.parent.stack.opacity:1)*e}var i=t("./nodecontainer");(n.prototype=Object.create(i.prototype)).getParentStack=function(t){var e=this.parent?this.parent.stack:null;return e?e.ownStacking?e:e.getParentStack(t):t.stack},e.exports=n},{"./nodecontainer":14}],22:[function(t,e){function n(t){this.rangeBounds=this.testRangeBounds(t),this.cors=this.testCORS(),this.svg=this.testSVG()}n.prototype.testRangeBounds=function(t){var e,n,r=!1;return t.createRange&&((e=t.createRange()).getBoundingClientRect&&((n=t.createElement("boundtest")).style.height="123px",n.style.display="block",t.body.appendChild(n),e.selectNode(n),123===e.getBoundingClientRect().height&&(r=!0),t.body.removeChild(n))),r},n.prototype.testCORS=function(){return void 0!==(new Image).crossOrigin},n.prototype.testSVG=function(){var t=new Image,e=document.createElement("canvas"),n=e.getContext("2d");t.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{n.drawImage(t,0,0),e.toDataURL()}catch(t){return!1}return!0},e.exports=n},{}],23:[function(t,e){function n(t){this.src=t,this.image=null;var n=this;this.promise=this.hasFabric().then(function(){return n.isInline(t)?Promise.resolve(n.inlineFormatting(t)):r(t)}).then(function(e){return new Promise(function(t){window.html2canvas.svg.fabric.loadSVGFromString(e,n.createCanvas.call(n,t))})})}var r=t("./xhr"),i=t("./utils").decode64;n.prototype.hasFabric=function(){return window.html2canvas.svg&&window.html2canvas.svg.fabric?Promise.resolve():Promise.reject(new Error("html2canvas.svg.js is not loaded, cannot render svg"))},n.prototype.inlineFormatting=function(t){return/^data:image\/svg\+xml;base64,/.test(t)?this.decode64(this.removeContentType(t)):this.removeContentType(t)},n.prototype.removeContentType=function(t){return t.replace(/^data:image\/svg\+xml(;base64)?,/,"")},n.prototype.isInline=function(t){return/^data:image\/svg\+xml/i.test(t)},n.prototype.createCanvas=function(r){var i=this;return function(t,e){var n=new window.html2canvas.svg.fabric.StaticCanvas("c");i.image=n.lowerCanvasEl,n.setWidth(e.width).setHeight(e.height).add(window.html2canvas.svg.fabric.util.groupSVGElements(t,e)).renderAll(),r(n.lowerCanvasEl)}},n.prototype.decode64=function(t){return"function"==typeof window.atob?window.atob(t):i(t)},e.exports=n},{"./utils":26,"./xhr":28}],24:[function(t,e){function n(n,t){this.src=n,this.image=null;var r=this;this.promise=t?new Promise(function(t,e){r.image=new Image,r.image.onload=t,r.image.onerror=e,r.image.src="data:image/svg+xml,"+(new XMLSerializer).serializeToString(n),!0===r.image.complete&&t(r.image)}):this.hasFabric().then(function(){return new Promise(function(t){window.html2canvas.svg.fabric.parseSVGDocument(n,r.createCanvas.call(r,t))})})}t=t("./svgcontainer");n.prototype=Object.create(t.prototype),e.exports=n},{"./svgcontainer":23}],25:[function(t,e){function n(t,e){i.call(this,t,e)}function r(t,e,n){return 0<t.length?e+n.toUpperCase():void 0}var i=t("./nodecontainer");(n.prototype=Object.create(i.prototype)).applyTextTransform=function(){this.node.data=this.transform(this.parent.css("textTransform"))},n.prototype.transform=function(t){var e=this.node.data;switch(t){case"lowercase":return e.toLowerCase();case"capitalize":return e.replace(/(^|\s|:|-|\(|\))([a-z])/g,r);case"uppercase":return e.toUpperCase();default:return e}},e.exports=n},{"./nodecontainer":14}],26:[function(t,e,n){n.smallImage=function(){return"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"},n.bind=function(t,e){return function(){return t.apply(e,arguments)}},n.decode64=function(t){for(var e,n,r,i,o,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=t.length,u="",c=0;c<a;c+=4)r=s.indexOf(t[c])<<2|(e=s.indexOf(t[c+1]))>>4,i=(15&e)<<4|(e=s.indexOf(t[c+2]))>>2,o=(3&e)<<6|(n=s.indexOf(t[c+3])),u+=64===e?String.fromCharCode(r):64===n||-1===n?String.fromCharCode(r,i):String.fromCharCode(r,i,o);return u},n.getBounds=function(t){var e,n;return t.getBoundingClientRect?(e=t.getBoundingClientRect(),n=null==t.offsetWidth?e.width:t.offsetWidth,{top:e.top,bottom:e.bottom||e.top+e.height,right:e.left+n,left:e.left,width:n,height:null==t.offsetHeight?e.height:t.offsetHeight}):{}},n.offsetBounds=function(t){var e=t.offsetParent?n.offsetBounds(t.offsetParent):{top:0,left:0};return{top:t.offsetTop+e.top,bottom:t.offsetTop+t.offsetHeight+e.top,right:t.offsetLeft+e.left+t.offsetWidth,left:t.offsetLeft+e.left,width:t.offsetWidth,height:t.offsetHeight}},n.parseBackgrounds=function(t){function e(){h&&((n='"'===n.substr(0,1)?n.substr(1,n.length-2):n)&&l.push(n),"-"===h.substr(0,1)&&0<(i=h.indexOf("-",1)+1)&&(r=h.substr(0,i),h=h.substr(i)),a.push({prefix:r,method:h.toLowerCase(),value:o,args:l,image:null})),l=[],h=r=n=o=""}var n,r,i,o,s,a=[],u=0,c=0,l=[],h=r=n=o="";return t.split("").forEach(function(t){if(!(0===u&&-1<" \r\n\t".indexOf(t))){switch(t){case'"':s?s===t&&(s=null):s=t;break;case"(":if(!s){if(0===u)return u=1,void(o+=t);c++}break;case")":if(!s&&1===u){if(0===c)return u=0,o+=t,void e();c--}break;case",":if(!s){if(0===u)return void e();if(1===u&&0===c&&!h.match(/^url$/i))return l.push(n),n="",void(o+=t)}}o+=t,0===u?h+=t:n+=t}}),e(),a}},{}],27:[function(t,e){function n(t){r.apply(this,arguments),this.type="linear"===t.args[0]?r.TYPES.LINEAR:r.TYPES.RADIAL}var r=t("./gradientcontainer");n.prototype=Object.create(r.prototype),e.exports=n},{"./gradientcontainer":9}],28:[function(t,e){e.exports=function(r){return new Promise(function(t,e){var n=new XMLHttpRequest;n.open("GET",r),n.onload=function(){200===n.status?t(n.responseText):e(new Error(n.statusText))},n.onerror=function(){e(new Error("Network Error"))},n.send()})}},{}]},{},[4])(4)}),!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).localforage=t()}(function(){return function r(i,o,s){function a(e,t){if(!o[e]){if(!i[e]){var n="function"==typeof require&&require;if(!t&&n)return n(e,!0);if(u)return u(e,!0);t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}n=o[e]={exports:{}};i[e][0].call(n.exports,function(t){return a(i[e][1][t]||t)},n,n.exports,r,i,o,s)}return o[e].exports}for(var u="function"==typeof require&&require,t=0;t<s.length;t++)a(s[t]);return a}({1:[function(t,c,e){!function(e){"use strict";function n(){o=!0;for(var t,e,n=u.length;n;){for(e=u,u=[],t=-1;++t<n;)e[t]();n=u.length}o=!1}var t,r,i,o,s=e.MutationObserver||e.WebKitMutationObserver,a=s?(t=0,s=new s(n),r=e.document.createTextNode(""),s.observe(r,{characterData:!0}),function(){r.data=t=++t%2}):e.setImmediate||void 0===e.MessageChannel?"document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){n(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(n,0)}:((i=new e.MessageChannel).port1.onmessage=n,function(){i.port2.postMessage(0)}),u=[];c.exports=function(t){1!==u.push(t)||o||a()}}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e,n){"use strict";function c(){}function r(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=d,this.queue=[],this.outcome=void 0,t!==c&&a(this,t)}function i(t,e,n){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof n&&(this.onRejected=n,this.callRejected=this.otherCallRejected)}function o(e,n,r){l(function(){var t;try{t=n(r)}catch(t){return h.reject(e,t)}t===e?h.reject(e,new TypeError("Cannot resolve promise with itself")):h.resolve(e,t)})}function s(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function a(e,t){function n(t){i||(i=!0,h.reject(e,t))}function r(t){i||(i=!0,h.resolve(e,t))}var i=!1,o=u(function(){t(r,n)});"error"===o.status&&n(o.value)}function u(t,e){var n={};try{n.value=t(e),n.status="success"}catch(t){n.status="error",n.value=t}return n}var l=t(1),h={},f=["REJECTED"],p=["FULFILLED"],d=["PENDING"];(e.exports=r).prototype.catch=function(t){return this.then(null,t)},r.prototype.then=function(t,e){var n;return"function"!=typeof t&&this.state===p||"function"!=typeof e&&this.state===f?this:(n=new this.constructor(c),this.state!==d?o(n,this.state===p?t:e,this.outcome):this.queue.push(new i(n,t,e)),n)},i.prototype.callFulfilled=function(t){h.resolve(this.promise,t)},i.prototype.otherCallFulfilled=function(t){o(this.promise,this.onFulfilled,t)},i.prototype.callRejected=function(t){h.reject(this.promise,t)},i.prototype.otherCallRejected=function(t){o(this.promise,this.onRejected,t)},h.resolve=function(t,e){var n=u(s,e);if("error"===n.status)return h.reject(t,n.value);n=n.value;if(n)a(t,n);else{t.state=p,t.outcome=e;for(var r=-1,i=t.queue.length;++r<i;)t.queue[r].callFulfilled(e)}return t},h.reject=function(t,e){t.state=f,t.outcome=e;for(var n=-1,r=t.queue.length;++n<r;)t.queue[n].callRejected(e);return t},r.resolve=function(t){return t instanceof this?t:h.resolve(new this(c),t)},r.reject=function(t){var e=new this(c);return h.reject(e,t)},r.all=function(t){function e(t,e){n.resolve(t).then(function(t){o[e]=t,++s!==r||i||(i=!0,h.resolve(u,o))},function(t){i||(i=!0,h.reject(u,t))})}var n=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var r=t.length,i=!1;if(!r)return this.resolve([]);for(var o=new Array(r),s=0,a=-1,u=new this(c);++a<r;)e(t[a],a);return u},r.race=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var n=t.length,r=!1;if(!n)return this.resolve([]);for(var i,o=-1,s=new this(c);++o<n;)i=t[o],e.resolve(i).then(function(t){r||(r=!0,h.resolve(s,t))},function(t){r||(r=!0,h.reject(s,t))});return s}},{1:1}],3:[function(e,t,n){!function(t){"use strict";"function"!=typeof t.Promise&&(t.Promise=e(2))}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{2:2}],4:[function(t,M,W){"use strict";function o(e,n){e=e||[],n=n||{};try{return new Blob(e,n)}catch(t){if("TypeError"!==t.name)throw t;for(var r=new("undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder),i=0;i<e.length;i+=1)r.append(e[i]);return r.getBlob(n.type)}}function p(t,e){e&&t.then(function(t){e(null,t)},function(t){e(t)})}function u(t,e,n){"function"==typeof e&&t.then(e),"function"==typeof n&&t.catch(n)}function l(t){return t="string"!=typeof t?String(t):t}function a(){if(arguments.length&&"function"==typeof arguments[arguments.length-1])return arguments[arguments.length-1]}function L(t){return"boolean"==typeof n?x.resolve(n):(r=t,new x(function(n){var t=r.transaction(C,O),e=o([""]);t.objectStore(C).put(e,"key"),t.onabort=function(t){t.preventDefault(),t.stopPropagation(),n(!1)},t.oncomplete=function(){var t=navigator.userAgent.match(/Chrome\/(\d+)/),e=navigator.userAgent.match(/Edge\//);n(e||!t||43<=parseInt(t[1],10))}}).catch(function(){return!1}).then(function(t){return n=t}));var r}function c(t){var t=k[t.name],n={};n.promise=new x(function(t,e){n.resolve=t,n.reject=e}),t.deferredOperations.push(n),t.dbReady?t.dbReady=t.dbReady.then(function(){return n.promise}):t.dbReady=n.promise}function h(t){t=k[t.name].deferredOperations.pop();t&&(t.resolve(),t.promise)}function f(t,e){t=k[t.name].deferredOperations.pop();if(t)return t.reject(e),t.promise}function e(i,o){return new x(function(t,e){if(k[i.name]=k[i.name]||H(),i.db){if(!o)return t(i.db);c(i),i.db.close()}var n=[i.name],r=(o&&n.push(i.version),_.open.apply(_,n));o&&(r.onupgradeneeded=function(t){var e=r.result;try{e.createObjectStore(i.storeName),t.oldVersion<=1&&e.createObjectStore(C)}catch(e){if("ConstraintError"!==e.name)throw e}}),r.onerror=function(t){t.preventDefault(),e(r.error)},r.onsuccess=function(){t(r.result),h(i)}})}function d(t){return e(t,!1)}function g(t){return e(t,!0)}function m(t){var e,n,r;return!t.db||(e=!t.db.objectStoreNames.contains(t.storeName),r=t.version<t.db.version,n=t.version>t.db.version,r&&(t.version,t.version=t.db.version),(n||e)&&(e&&(r=t.db.version+1)>t.version&&(t.version=r),1))}function v(t){return o([function(t){for(var e=t.length,n=new ArrayBuffer(e),r=new Uint8Array(n),i=0;i<e;i++)r[i]=t.charCodeAt(i);return n}(atob(t.data))],{type:t.type})}function y(t){return t&&t.__local_forage_encoded_blob}function F(t){var e=this,n=e._initReady().then(function(){var t=k[e._dbInfo.name];if(t&&t.dbReady)return t.dbReady});return u(n,t,t),n}function b(t,e,n,r){void 0===r&&(r=1);try{var i=t.db.transaction(t.storeName,e);n(null,i)}catch(i){if(0<r&&(!t.db||"InvalidStateError"===i.name||"NotFoundError"===i.name))return x.resolve().then(function(){if(!t.db||"NotFoundError"===i.name&&!t.db.objectStoreNames.contains(t.storeName)&&t.version<=t.db.version)return t.db&&(t.version=t.db.version+1),g(t)}).then(function(){return function(n){c(n);for(var r=k[n.name],i=r.forages,t=0;t<i.length;t++){var e=i[t];e._dbInfo.db&&(e._dbInfo.db.close(),e._dbInfo.db=null)}return n.db=null,d(n).then(function(t){return n.db=t,m(n)?g(n):t}).then(function(t){n.db=r.db=t;for(var e=0;e<i.length;e++)i[e]._dbInfo.db=t}).catch(function(t){throw f(n,t),t})}(t).then(function(){b(t,e,n,r-1)})}).catch(n);n(i)}}function H(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function B(t){for(var e,n,r,i,o=.75*t.length,s=t.length,a=0,o=("="===t[t.length-1]&&(o--,"="===t[t.length-2]&&o--),new ArrayBuffer(o)),u=new Uint8Array(o),c=0;c<s;c+=4)e=E.indexOf(t[c]),n=E.indexOf(t[c+1]),r=E.indexOf(t[c+2]),i=E.indexOf(t[c+3]),u[a++]=e<<2|n>>4,u[a++]=(15&n)<<4|r>>2,u[a++]=(3&r)<<6|63&i;return o}function s(t){for(var e=new Uint8Array(t),n="",r=0;r<e.length;r+=3)n=(n=(n=(n+=E[e[r]>>2])+E[(3&e[r])<<4|e[r+1]>>4])+E[(15&e[r+1])<<2|e[r+2]>>6])+E[63&e[r+2]];return e.length%3==2?n=n.substring(0,n.length-1)+"=":e.length%3==1&&(n=n.substring(0,n.length-2)+"=="),n}function q(t,e,n,r){t.executeSql("CREATE TABLE IF NOT EXISTS "+e.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],n,r)}function w(t,r,i,o,s,a){t.executeSql(i,o,s,function(t,n){n.code===n.SYNTAX_ERR?t.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[name],function(t,e){e.rows.length?a(t,n):q(t,r,function(){t.executeSql(i,o,s,a)},a)},a):a(t,n)},a)}function z(s,t,a,u){var c=this,e=(s=l(s),new x(function(i,o){c.ready().then(function(){var n=t=void 0===t?null:t,r=c._dbInfo;r.serializer.serialize(t,function(e,t){t?o(t):r.db.transaction(function(t){w(t,r,"INSERT OR REPLACE INTO "+r.storeName+" (key, value) VALUES (?, ?)",[s,e],function(){i(n)},function(t,e){o(e)})},function(t){t.code===t.QUOTA_ERR&&(0<u?i(z.apply(c,[s,n,a,u-1])):o(t))})})}).catch(o)}));return p(e,a),e}function U(t,e){var n=t.name+"/";return t.storeName!==e.storeName&&(n+=t.storeName+"/"),n}function G(){return!function(){var t="_localforage_support_test";try{return localStorage.setItem(t,!0),localStorage.removeItem(t),0}catch(t){return 1}}()||0<localStorage.length}function i(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];if(n)for(var r in n)n.hasOwnProperty(r)&&(Z(n[r])?t[r]=n[r].slice():t[r]=n[r])}return t}function V(t,e){for(var n,r,i=t.length,o=0;o<i;){if((n=t[o])===(r=e)||"number"==typeof n&&"number"==typeof r&&isNaN(n)&&isNaN(r))return 1;o++}}var $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_=function(){try{return"undefined"!=typeof indexedDB?indexedDB:"undefined"!=typeof webkitIndexedDB?webkitIndexedDB:"undefined"!=typeof mozIndexedDB?mozIndexedDB:"undefined"!=typeof OIndexedDB?OIndexedDB:"undefined"!=typeof msIndexedDB?msIndexedDB:void 0}catch(t){}}(),x=("undefined"==typeof Promise&&t(3),Promise),C="local-forage-detect-blob-support",n=void 0,k={},X=Object.prototype.toString,S="readonly",O="readwrite",t={_driver:"asyncStorage",_initStorage:function(t){function e(){return x.resolve()}var r=this,i={db:null};if(t)for(var n in t)i[n]=t[n];var o=k[i.name];o||(o=H(),k[i.name]=o),o.forages.push(r),r._initReady||(r._initReady=r.ready,r.ready=F);for(var s=[],a=0;a<o.forages.length;a++){var u=o.forages[a];u!==r&&s.push(u._initReady().catch(e))}var c=o.forages.slice(0);return x.all(s).then(function(){return i.db=o.db,d(i)}).then(function(t){return i.db=t,m(i,r._defaultConfig.version)?g(i):t}).then(function(t){i.db=o.db=t,r._dbInfo=i;for(var e=0;e<c.length;e++){var n=c[e];n!==r&&(n._dbInfo.db=i.db,n._dbInfo.version=i.version)}})},_support:function(){try{var t,e;return _?(t="undefined"!=typeof openDatabase&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),e="function"==typeof fetch&&-1!==fetch.toString().indexOf("[native code"),(!t||e)&&"undefined"!=typeof indexedDB&&"undefined"!=typeof IDBKeyRange):!1}catch(t){return!1}}(),iterate:function(s,t){var a=this,e=new x(function(i,o){a.ready().then(function(){b(a._dbInfo,S,function(t,e){if(t)return o(t);try{var n=e.objectStore(a._dbInfo.storeName).openCursor(),r=1;n.onsuccess=function(){var t,e=n.result;e?(y(t=e.value)&&(t=v(t)),void 0!==(t=s(t,e.key,r++))?i(t):e.continue()):i()},n.onerror=function(){o(n.error)}}catch(t){o(t)}})}).catch(o)});return p(e,t),e},getItem:function(o,t){var s=this,e=(o=l(o),new x(function(r,i){s.ready().then(function(){b(s._dbInfo,S,function(t,e){if(t)return i(t);try{var n=e.objectStore(s._dbInfo.storeName).get(o);n.onsuccess=function(){var t=n.result;y(t=void 0===t?null:t)&&(t=v(t)),r(t)},n.onerror=function(){i(n.error)}}catch(t){i(t)}})}).catch(i)}));return p(e,t),e},setItem:function(a,e,t){var u=this,n=(a=l(a),new x(function(o,s){var t;u.ready().then(function(){return t=u._dbInfo,"[object Blob]"===X.call(e)?L(t.db).then(function(t){return t?e:(r=e,new x(function(e,t){var n=new FileReader;n.onerror=t,n.onloadend=function(t){t=btoa(t.target.result||"");e({__local_forage_encoded_blob:!0,data:t,type:r.type})},n.readAsBinaryString(r)}));var r}):e}).then(function(i){b(u._dbInfo,O,function(t,e){if(t)return s(t);try{var n=e.objectStore(u._dbInfo.storeName),r=(null===i&&(i=void 0),n.put(i,a));e.oncomplete=function(){o(i=void 0===i?null:i)},e.onabort=e.onerror=function(){var t=r.error||r.transaction.error;s(t)}}catch(t){s(t)}})}).catch(s)}));return p(n,t),n},removeItem:function(o,t){var s=this,e=(o=l(o),new x(function(r,i){s.ready().then(function(){b(s._dbInfo,O,function(t,e){if(t)return i(t);try{var n=e.objectStore(s._dbInfo.storeName).delete(o);e.oncomplete=function(){r()},e.onerror=function(){i(n.error)},e.onabort=function(){var t=n.error||n.transaction.error;i(t)}}catch(t){i(t)}})}).catch(i)}));return p(e,t),e},clear:function(t){var o=this,e=new x(function(r,i){o.ready().then(function(){b(o._dbInfo,O,function(t,e){if(t)return i(t);try{var n=e.objectStore(o._dbInfo.storeName).clear();e.oncomplete=function(){r()},e.onabort=e.onerror=function(){var t=n.error||n.transaction.error;i(t)}}catch(t){i(t)}})}).catch(i)});return p(e,t),e},length:function(t){var o=this,e=new x(function(r,i){o.ready().then(function(){b(o._dbInfo,S,function(t,e){if(t)return i(t);try{var n=e.objectStore(o._dbInfo.storeName).count();n.onsuccess=function(){r(n.result)},n.onerror=function(){i(n.error)}}catch(t){i(t)}})}).catch(i)});return p(e,t),e},key:function(a,t){var u=this,e=new x(function(o,s){a<0?o(null):u.ready().then(function(){b(u._dbInfo,S,function(t,e){if(t)return s(t);try{var n=e.objectStore(u._dbInfo.storeName),r=!1,i=n.openCursor();i.onsuccess=function(){var t=i.result;t?0===a||r?o(t.key):(r=!0,t.advance(a)):o(null)},i.onerror=function(){s(i.error)}}catch(t){s(t)}})}).catch(s)});return p(e,t),e},keys:function(t){var s=this,e=new x(function(i,o){s.ready().then(function(){b(s._dbInfo,S,function(t,e){if(t)return o(t);try{var n=e.objectStore(s._dbInfo.storeName).openCursor(),r=[];n.onsuccess=function(){var t=n.result;t?(r.push(t.key),t.continue()):i(r)},n.onerror=function(){o(n.error)}}catch(t){o(t)}})}).catch(o)});return p(e,t),e},dropInstance:function(s,t){t=a.apply(this,arguments);var e=this.config();return(s="function"!=typeof s&&s||{}).name||(s.name=s.name||e.name,s.storeName=s.storeName||e.storeName),p(e=s.name?(e=s.name===e.name&&this._dbInfo.db?x.resolve(this._dbInfo.db):d(s).then(function(t){var e=k[s.name],n=e.forages;e.db=t;for(var r=0;r<n.length;r++)n[r]._dbInfo.db=t;return t}),s.storeName?e.then(function(t){if(t.objectStoreNames.contains(s.storeName)){var i=t.version+1,r=(c(s),k[s.name]),o=r.forages;t.close();for(var e=0;e<o.length;e++){var n=o[e];n._dbInfo.db=null,n._dbInfo.version=i}return new x(function(e,n){var r=_.open(s.name,i);r.onerror=function(t){r.result.close(),n(t)},r.onupgradeneeded=function(){r.result.deleteObjectStore(s.storeName)},r.onsuccess=function(){var t=r.result;t.close(),e(t)}}).then(function(t){r.db=t;for(var e=0;e<o.length;e++){var n=o[e];n._dbInfo.db=t,h(n._dbInfo)}}).catch(function(t){throw(f(s,t)||x.resolve()).catch(function(){}),t})}}):e.then(function(t){c(s);var n=k[s.name],r=n.forages;t.close();for(var e=0;e<r.length;e++)r[e]._dbInfo.db=null;return new x(function(e,n){var r=_.deleteDatabase(s.name);r.onerror=r.onblocked=function(t){var e=r.result;e&&e.close(),n(t)},r.onsuccess=function(){var t=r.result;t&&t.close(),e(t)}}).then(function(t){n.db=t;for(var e=0;e<r.length;e++)h(r[e]._dbInfo)}).catch(function(t){throw(f(s,t)||x.resolve()).catch(function(){}),t})})):x.reject("Invalid arguments"),t),e}},E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Y=/^~~local_forage_type~([^~]+)~/,T="__lfsc__:",A=T.length,K=A+"arbf".length,Q=Object.prototype.toString,I={serialize:function(e,n){var t="";if(e&&(t=Q.call(e)),e&&("[object ArrayBuffer]"===t||e.buffer&&"[object ArrayBuffer]"===Q.call(e.buffer))){var r,i=T;e instanceof ArrayBuffer?(r=e,i+="arbf"):(r=e.buffer,"[object Int8Array]"===t?i+="si08":"[object Uint8Array]"===t?i+="ui08":"[object Uint8ClampedArray]"===t?i+="uic8":"[object Int16Array]"===t?i+="si16":"[object Uint16Array]"===t?i+="ur16":"[object Int32Array]"===t?i+="si32":"[object Uint32Array]"===t?i+="ui32":"[object Float32Array]"===t?i+="fl32":"[object Float64Array]"===t?i+="fl64":n(new Error("Failed to get type for BinaryArray"))),n(i+s(r))}else if("[object Blob]"===t){i=new FileReader;i.onload=function(){var t="~~local_forage_type~"+e.type+"~"+s(this.result);n(T+"blob"+t)},i.readAsArrayBuffer(e)}else try{n(JSON.stringify(e))}catch(t){n(null,t)}},deserialize:function(t){if(t.substring(0,A)!==T)return JSON.parse(t);var e,n=t.substring(K),r=t.substring(A,K),i=("blob"===r&&Y.test(n)&&(e=(t=n.match(Y))[1],n=n.substring(t[0].length)),B(n));switch(r){case"arbf":return i;case"blob":return o([i],{type:e});case"si08":return new Int8Array(i);case"ui08":return new Uint8Array(i);case"uic8":return new Uint8ClampedArray(i);case"si16":return new Int16Array(i);case"ur16":return new Uint16Array(i);case"si32":return new Int32Array(i);case"ui32":return new Uint32Array(i);case"fl32":return new Float32Array(i);case"fl64":return new Float64Array(i);default:throw new Error("Unkown type: "+r)}},stringToBuffer:B,bufferToString:s},r={_driver:"webSQLStorage",_initStorage:function(t){var r=this,i={db:null};if(t)for(var e in t)i[e]="string"!=typeof t[e]?t[e].toString():t[e];var n=new x(function(e,n){try{i.db=openDatabase(i.name,String(i.version),i.description,i.size)}catch(e){return n(e)}i.db.transaction(function(t){q(t,i,function(){r._dbInfo=i,e()},function(t,e){n(e)})},n)});return i.serializer=I,n},_support:"function"==typeof openDatabase,iterate:function(c,t){var e=this,n=new x(function(u,n){e.ready().then(function(){var a=e._dbInfo;a.db.transaction(function(t){w(t,a,"SELECT * FROM "+a.storeName,[],function(t,e){for(var n=e.rows,r=n.length,i=0;i<r;i++){var o=n.item(i),s=(s=o.value)&&a.serializer.deserialize(s);if(void 0!==(s=c(s,o.key,i+1)))return void u(s)}u()},function(t,e){n(e)})})}).catch(n)});return p(n,t),n},getItem:function(e,t){var o=this,n=(e=l(e),new x(function(r,i){o.ready().then(function(){var n=o._dbInfo;n.db.transaction(function(t){w(t,n,"SELECT * FROM "+n.storeName+" WHERE key = ? LIMIT 1",[e],function(t,e){e=(e=e.rows.length?e.rows.item(0).value:null)&&n.serializer.deserialize(e);r(e)},function(t,e){i(e)})})}).catch(i)}));return p(n,t),n},setItem:function(t,e,n){return z.apply(this,[t,e,n,1])},removeItem:function(i,t){var o=this,e=(i=l(i),new x(function(n,r){o.ready().then(function(){var e=o._dbInfo;e.db.transaction(function(t){w(t,e,"DELETE FROM "+e.storeName+" WHERE key = ?",[i],function(){n()},function(t,e){r(e)})})}).catch(r)}));return p(e,t),e},clear:function(t){var i=this,e=new x(function(n,r){i.ready().then(function(){var e=i._dbInfo;e.db.transaction(function(t){w(t,e,"DELETE FROM "+e.storeName,[],function(){n()},function(t,e){r(e)})})}).catch(r)});return p(e,t),e},length:function(t){var i=this,e=new x(function(n,r){i.ready().then(function(){var e=i._dbInfo;e.db.transaction(function(t){w(t,e,"SELECT COUNT(key) as c FROM "+e.storeName,[],function(t,e){e=e.rows.item(0).c;n(e)},function(t,e){r(e)})})}).catch(r)});return p(e,t),e},key:function(i,t){var o=this,e=new x(function(n,r){o.ready().then(function(){var e=o._dbInfo;e.db.transaction(function(t){w(t,e,"SELECT key FROM "+e.storeName+" WHERE id = ? LIMIT 1",[i+1],function(t,e){e=e.rows.length?e.rows.item(0).key:null;n(e)},function(t,e){r(e)})})}).catch(r)});return p(e,t),e},keys:function(t){var r=this,e=new x(function(i,n){r.ready().then(function(){var e=r._dbInfo;e.db.transaction(function(t){w(t,e,"SELECT key FROM "+e.storeName,[],function(t,e){for(var n=[],r=0;r<e.rows.length;r++)n.push(e.rows.item(r).key);i(n)},function(t,e){n(e)})})}).catch(n)});return p(e,t),e},dropInstance:function(n,t){t=a.apply(this,arguments);var r=this.config(),i=((n="function"!=typeof n&&n||{}).name||(n.name=n.name||r.name,n.storeName=n.storeName||r.storeName),this),e=n.name?new x(function(t){var o,e=n.name===r.name?i._dbInfo.db:openDatabase(n.name,"","",0);t(n.storeName?{db:e,storeNames:[n.storeName]}:(o=e,new x(function(i,n){o.transaction(function(t){t.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(t,e){for(var n=[],r=0;r<e.rows.length;r++)n.push(e.rows.item(r).name);i({db:o,storeNames:n})},function(t,e){n(e)})},function(t){n(t)})})))}).then(function(s){return new x(function(i,o){s.db.transaction(function(r){for(var t=[],e=0,n=s.storeNames.length;e<n;e++)t.push(function(e){return new x(function(t,n){r.executeSql("DROP TABLE IF EXISTS "+e,[],function(){t()},function(t,e){n(e)})})}(s.storeNames[e]));x.all(t).then(function(){i()}).catch(function(t){o(t)})},function(t){o(t)})})}):x.reject("Invalid arguments");return p(e,t),e}},J={_driver:"localStorageWrapper",_initStorage:function(t){var e={};if(t)for(var n in t)e[n]=t[n];return e.keyPrefix=U(t,this._defaultConfig),G()?((this._dbInfo=e).serializer=I,x.resolve()):x.reject()},_support:function(){try{return"undefined"!=typeof localStorage&&"setItem"in localStorage&&!!localStorage.setItem}catch(t){return!1}}(),iterate:function(u,t){var c=this,e=c.ready().then(function(){for(var t=c._dbInfo,e=t.keyPrefix,n=e.length,r=localStorage.length,i=1,o=0;o<r;o++){var s=localStorage.key(o);if(0===s.indexOf(e)){var a=(a=localStorage.getItem(s))&&t.serializer.deserialize(a);if(void 0!==(a=u(a,s.substring(n),i++)))return a}}});return p(e,t),e},getItem:function(n,t){var r=this,e=(n=l(n),r.ready().then(function(){var t=r._dbInfo,e=localStorage.getItem(t.keyPrefix+n);return e=e&&t.serializer.deserialize(e)}));return p(e,t),e},setItem:function(s,t,e){var a=this,n=(s=l(s),a.ready().then(function(){var o=t=void 0===t?null:t;return new x(function(n,r){var i=a._dbInfo;i.serializer.serialize(t,function(t,e){if(e)r(e);else try{localStorage.setItem(i.keyPrefix+s,t),n(o)}catch(t){"QuotaExceededError"!==t.name&&"NS_ERROR_DOM_QUOTA_REACHED"!==t.name||r(t),r(t)}})})}));return p(n,e),n},removeItem:function(e,t){var n=this,r=(e=l(e),n.ready().then(function(){var t=n._dbInfo;localStorage.removeItem(t.keyPrefix+e)}));return p(r,t),r},clear:function(t){var r=this,e=r.ready().then(function(){for(var t=r._dbInfo.keyPrefix,e=localStorage.length-1;0<=e;e--){var n=localStorage.key(e);0===n.indexOf(t)&&localStorage.removeItem(n)}});return p(e,t),e},length:function(t){var e=this.keys().then(function(t){return t.length});return p(e,t),e},key:function(n,t){var r=this,e=r.ready().then(function(){var e,t=r._dbInfo;try{e=localStorage.key(n)}catch(t){e=null}return e=e&&e.substring(t.keyPrefix.length)});return p(e,t),e},keys:function(t){var o=this,e=o.ready().then(function(){for(var t=o._dbInfo,e=localStorage.length,n=[],r=0;r<e;r++){var i=localStorage.key(r);0===i.indexOf(t.keyPrefix)&&n.push(i.substring(t.keyPrefix.length))}return n});return p(e,t),e},dropInstance:function(e,t){t=a.apply(this,arguments),(e="function"!=typeof e&&e||{}).name||(r=this.config(),e.name=e.name||r.name,e.storeName=e.storeName||r.storeName);var n=this,r=e.name?new x(function(t){t(e.storeName?U(e,n._defaultConfig):e.name+"/")}).then(function(t){for(var e=localStorage.length-1;0<=e;e--){var n=localStorage.key(e);0===n.indexOf(t)&&localStorage.removeItem(n)}}):x.reject("Invalid arguments");return p(r,t),r}},Z=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},j={},tt={},P={INDEXEDDB:t,WEBSQL:r,LOCALSTORAGE:J},t=[P.INDEXEDDB._driver,P.WEBSQL._driver,P.LOCALSTORAGE._driver],N=["dropInstance"],D=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(N),et={description:"",driver:t.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};R.prototype.config=function(t){if("object"!==(void 0===t?"undefined":$(t)))return"string"==typeof t?this._config[t]:this._config;if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var e in t){if("storeName"===e&&(t[e]=t[e].replace(/\W/g,"_")),"version"===e&&"number"!=typeof t[e])return new Error("Database version must be a number.");this._config[e]=t[e]}return!("driver"in t&&t.driver)||this.setDriver(this._config.driver)},R.prototype.defineDriver=function(f,t,e){var n=new x(function(e,n){try{var r=f._driver,t=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(f._driver){for(var i=D.concat("_initStorage"),o=0,s=i.length;o<s;o++){var a=i[o];if((!V(N,a)||f[a])&&"function"!=typeof f[a])return void n(t)}for(var u=0,c=N.length;u<c;u++){var l=N[u];f[l]||(f[l]=function(e){return function(){var t=new Error("Method "+e+" is not implemented by the current driver"),t=x.reject(t);return p(t,arguments[arguments.length-1]),t}}(l))}var h=function(t){j[r],j[r]=f,tt[r]=t,e()};"_support"in f?f._support&&"function"==typeof f._support?f._support().then(h,n):h(!!f._support):h(!0)}else n(t)}catch(t){n(t)}});return u(n,t,e),n},R.prototype.driver=function(){return this._driver||null},R.prototype.getDriver=function(t,e,n){t=j[t]?x.resolve(j[t]):x.reject(new Error("Driver not found."));return u(t,e,n),t},R.prototype.getSerializer=function(t){var e=x.resolve(I);return u(e,t),e},R.prototype.ready=function(t){var e=this,n=e._driverSet.then(function(){return null===e._ready&&(e._ready=e._initDriver()),e._ready});return u(n,t,t),n},R.prototype.setDriver=function(t,e,n){function o(){a._config.driver=a.driver()}function s(t){return a._extend(t),o(),a._ready=a._initStorage(a._config),a._ready}function r(i){return function(){var r=0;return function t(){for(;r<i.length;){var e=i[r];return r++,a._dbInfo=null,a._ready=null,a.getDriver(e).then(s).catch(t)}o();var n=new Error("No available storage method found.");return a._driverSet=x.reject(n),a._driverSet}()}}var a=this,i=(Z(t)||(t=[t]),this._getSupportedDrivers(t)),t=null!==this._driverSet?this._driverSet.catch(function(){return x.resolve()}):x.resolve();return this._driverSet=t.then(function(){var t=i[0];return a._dbInfo=null,a._ready=null,a.getDriver(t).then(function(t){a._driver=t._driver,o(),a._wrapLibraryMethodsWithReady(),a._initDriver=r(i)})}).catch(function(){o();var t=new Error("No available storage method found.");return a._driverSet=x.reject(t),a._driverSet}),u(this._driverSet,e,n),this._driverSet},R.prototype.supports=function(t){return!!tt[t]},R.prototype._extend=function(t){i(this,t)},R.prototype._getSupportedDrivers=function(t){for(var e=[],n=0,r=t.length;n<r;n++){var i=t[n];this.supports(i)&&e.push(i)}return e},R.prototype._wrapLibraryMethodsWithReady=function(){for(var t=0,e=D.length;t<e;t++)!function(e,n){e[n]=function(){var t=arguments;return e.ready().then(function(){return e[n].apply(e,t)})}}(this,D[t])},R.prototype.createInstance=function(t){return new R(t)};r=new R;function R(t){var e,n,r;if(!(this instanceof R))throw new TypeError("Cannot call a class as a function");for(e in P)P.hasOwnProperty(e)&&(r=(n=P[e])._driver,this[e]=r,j[r]||this.defineDriver(n));this._defaultConfig=i({},et),this._config=i({},this._defaultConfig,t),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch(function(){})}M.exports=r},{3:3}]},{},[4])(4)}),!function(){function ys(t,e){return t.set(e[0],e[1]),t}function bs(t,e){return t.add(e),t}function Ho(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}function ws(t,e,n,r){for(var i=-1,o=null==t?0:t.length;++i<o;){var s=t[i];e(r,s,n(s),t)}return r}function Bo(t,e){for(var n=-1,r=null==t?0:t.length;++n<r&&!1!==e(t[n],n,t););return t}function _s(t,e){for(var n=null==t?0:t.length;n--&&!1!==e(t[n],n,t););return t}function xs(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(!e(t[n],n,t))return!1;return!0}function qo(t,e){for(var n=-1,r=null==t?0:t.length,i=0,o=[];++n<r;){var s=t[n];e(s,n,t)&&(o[i++]=s)}return o}function Cs(t,e){return!!(null==t?0:t.length)&&-1<Vo(t,e,0)}function ks(t,e,n){for(var r=-1,i=null==t?0:t.length;++r<i;)if(n(e,t[r]))return!0;return!1}function zo(t,e){for(var n=-1,r=null==t?0:t.length,i=Array(r);++n<r;)i[n]=e(t[n],n,t);return i}function Uo(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}function Go(t,e,n,r){var i=-1,o=null==t?0:t.length;for(r&&o&&(n=t[++i]);++i<o;)n=e(n,t[i],i,t);return n}function Ss(t,e,n,r){var i=null==t?0:t.length;for(r&&i&&(n=t[--i]);i--;)n=e(n,t[i],i,t);return n}function Os(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}function Es(t,r,e){var i;return e(t,function(t,e,n){if(r(t,e,n))return i=e,!1}),i}function Ts(t,e,n,r){for(var i=t.length,o=n+(r?1:-1);r?o--:++o<i;)if(e(t[o],o,t))return o;return-1}function Vo(t,e,n){if(e!=e)return Ts(t,Is,n);for(var r=t,i=e,o=n-1,s=r.length;++o<s;)if(r[o]===i)return o;return-1}function As(t,e,n,r){for(var i=n-1,o=t.length;++i<o;)if(r(t[i],e))return i;return-1}function Is(t){return t!=t}function js(t,e){var n=null==t?0:t.length;return n?Ds(t,e)/n:zs}function Ps(e){return function(t){return null==t?es:t[e]}}function i(e){return function(t){return null==e?es:e[t]}}function Ns(t,r,i,o,e){return e(t,function(t,e,n){i=o?(o=!1,t):r(i,t,e,n)}),i}function Ds(t,e){for(var n,r=-1,i=t.length;++r<i;){var o=e(t[r]);o!==es&&(n=n===es?o:n+o)}return n}function Rs(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}function $o(e){return function(t){return e(t)}}function Ms(e,t){return zo(t,function(t){return e[t]})}function Xo(t,e){return t.has(e)}function Ws(t,e){for(var n=-1,r=t.length;++n<r&&-1<Vo(e,t[n],0););return n}function Ls(t,e){for(var n=t.length;n--&&-1<Vo(e,t[n],0););return n}function Fs(t){return"\\"+x[t]}function Yo(t){return _.test(t)}function Ko(t){var n=-1,r=Array(t.size);return t.forEach(function(t,e){r[++n]=[e,t]}),r}function Hs(e,n){return function(t){return e(n(t))}}function Qo(t,e){for(var n=-1,r=t.length,i=0,o=[];++n<r;){var s=t[n];s!==e&&s!==qs||(t[n]=qs,o[i++]=n)}return o}function Jo(t){var e=-1,n=Array(t.size);return t.forEach(function(t){n[++e]=t}),n}function Zo(t){return(Yo(t)?function(t){for(var e=w.lastIndex=0;w.test(t);)++e;return e}:k)(t)}function ts(t){return Yo(t)?t.match(w)||[]:t.split("")}var es,ns="Expected a function",Bs="__lodash_hash_undefined__",qs="__lodash_placeholder__",rs=9007199254740991,zs=NaN,is=4294967295,Us=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]],os="[object Arguments]",Gs="[object Array]",ss="[object Boolean]",as="[object Date]",Vs="[object Error]",$s="[object Function]",Xs="[object GeneratorFunction]",us="[object Map]",cs="[object Number]",ls="[object Object]",Ys="[object Promise]",hs="[object RegExp]",fs="[object Set]",Ks="[object String]",Qs="[object Symbol]",Js="[object WeakMap]",Zs="[object ArrayBuffer]",ps="[object DataView]",ta="[object Float32Array]",ea="[object Float64Array]",na="[object Int8Array]",ra="[object Int16Array]",ia="[object Int32Array]",oa="[object Uint8Array]",sa="[object Uint8ClampedArray]",aa="[object Uint16Array]",ua="[object Uint32Array]",ca=/\b__p \+= '';/g,la=/\b(__p \+=) '' \+/g,ha=/(__e\(.*?\)|\b__t\)) \+\n'';/g,fa=/&(?:amp|lt|gt|quot|#39);/g,pa=/[&<>"']/g,da=RegExp(fa.source),ga=RegExp(pa.source),ma=/<%-([\s\S]+?)%>/g,va=/<%([\s\S]+?)%>/g,ya=/<%=([\s\S]+?)%>/g,ba=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,wa=/^\w*$/,_a=/^\./,xa=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ca=/[\\^$.*+?()[\]{}|]/g,ka=RegExp(Ca.source),Sa=/^\s+|\s+$/g,Oa=/^\s+/,Ea=/\s+$/,Ta=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Aa=/\{\n\/\* \[wrapped with (.+)\] \*/,Ia=/,? & /,ja=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Pa=/\\(\\)?/g,Na=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Da=/\w*$/,Ra=/^[-+]0x[0-9a-f]+$/i,Ma=/^0b[01]+$/i,Wa=/^\[object .+?Constructor\]$/,La=/^0o[0-7]+$/i,Fa=/^(?:0|[1-9]\d*)$/,Ha=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Ba=/($^)/,qa=/['\n\r\u2028\u2029\\]/g,o="\\ud800-\\udfff",s="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",a="\\u2700-\\u27bf",t="a-z\\xdf-\\xf6\\xf8-\\xff",e="A-Z\\xc0-\\xd6\\xd8-\\xde",u="\\ufe0e\\ufe0f",c="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",l="["+o+"]",h="["+c+"]",n="["+s+"]",f="["+a+"]",p="["+t+"]",c="[^"+o+c+"\\d+"+a+t+e+"]",a="\\ud83c[\\udffb-\\udfff]",t="[^"+o+"]",d="(?:\\ud83c[\\udde6-\\uddff]){2}",r="[\\ud800-\\udbff][\\udc00-\\udfff]",e="["+e+"]",g="(?:"+p+"|"+c+")",c="(?:"+e+"|"+c+")",m="(?:['’](?:d|ll|m|re|s|t|ve))?",v="(?:['’](?:D|LL|M|RE|S|T|VE))?",y="(?:"+n+"|"+a+")"+"?",b="["+u+"]?",b=b+y+("(?:\\u200d(?:"+[t,d,r].join("|")+")"+b+y+")*"),y="(?:"+[f,d,r].join("|")+")"+b,f="(?:"+[t+n+"?",n,d,r,l].join("|")+")",za=RegExp("['’]","g"),Ua=RegExp(n,"g"),w=RegExp(a+"(?="+a+")|"+f+b,"g"),Ga=RegExp([e+"?"+p+"+"+m+"(?="+[h,e,"$"].join("|")+")",c+"+"+v+"(?="+[h,e+g,"$"].join("|")+")",e+"?"+g+"+"+m,e+"+"+v,"\\d*(?:(?:1ST|2ND|3RD|(?![123])\\dTH)\\b)","\\d*(?:(?:1st|2nd|3rd|(?![123])\\dth)\\b)","\\d+",y].join("|"),"g"),_=RegExp("[\\u200d"+o+s+u+"]"),Va=/[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,$a=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Xa=-1,ds={},gs=(ds[ta]=ds[ea]=ds[na]=ds[ra]=ds[ia]=ds[oa]=ds[sa]=ds[aa]=ds[ua]=!0,ds[os]=ds[Gs]=ds[Zs]=ds[ss]=ds[ps]=ds[as]=ds[Vs]=ds[$s]=ds[us]=ds[cs]=ds[ls]=ds[hs]=ds[fs]=ds[Ks]=ds[Js]=!1,{}),x=(gs[os]=gs[Gs]=gs[Zs]=gs[ps]=gs[ss]=gs[as]=gs[ta]=gs[ea]=gs[na]=gs[ra]=gs[ia]=gs[us]=gs[cs]=gs[ls]=gs[hs]=gs[fs]=gs[Ks]=gs[Qs]=gs[oa]=gs[sa]=gs[aa]=gs[ua]=!0,gs[Vs]=gs[$s]=gs[Js]=!1,{"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"}),Ya=parseFloat,Ka=parseInt,t="object"==typeof global&&global&&global.Object===Object&&global,d="object"==typeof self&&self&&self.Object===Object&&self,ms=t||d||Function("return this")(),r="object"==typeof exports&&exports&&!exports.nodeType&&exports,l=r&&"object"==typeof module&&module&&!module.nodeType&&module,Qa=l&&l.exports===r,C=Qa&&t.process,n=function(){try{return C&&C.binding&&C.binding("util")}catch(t){}}(),Ja=n&&n.isArrayBuffer,Za=n&&n.isDate,tu=n&&n.isMap,eu=n&&n.isRegExp,nu=n&&n.isSet,ru=n&&n.isTypedArray,k=Ps("length"),iu=i({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),ou=i({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),su=i({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),vs=function i(t){function d(t){if(o(t)&&!M(t)&&!(t instanceof m)){if(t instanceof g)return t;if(P.call(t,"__wrapped__"))return rn(t)}return new g(t)}function W(){}function g(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=es}function m(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=is,this.__views__=[]}function L(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function F(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function H(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function B(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new H;++e<n;)this.add(t[e])}function w(t){t=this.__data__=new F(t);this.size=t.size}function q(t,e){var n,r=M(t),i=!r&&qi(t),o=!r&&!i&&Ui(t),s=!r&&!i&&!o&&Xi(t),a=r||i||o||s,u=a?Rs(t.length,tr):[],c=u.length;for(n in t)!e&&!P.call(t,n)||a&&("length"==n||o&&("offset"==n||"parent"==n)||s&&("buffer"==n||"byteLength"==n||"byteOffset"==n)||Ue(n,c))||u.push(n);return u}function z(t){var e=t.length;return e?t[Pt(0,e-1)]:es}function U(t,e){return tn(x(t),Z(e,0,t.length))}function G(t){return tn(x(t))}function V(t,e,n){(n===es||Cn(t[e],n))&&(n!==es||e in t)||Q(t,e,n)}function $(t,e,n){var r=t[e];P.call(t,e)&&Cn(r,n)&&(n!==es||e in t)||Q(t,e,n)}function X(t,e){for(var n=t.length;n--;)if(Cn(t[n][0],e))return n;return-1}function Y(t,r,i,o){return Jr(t,function(t,e,n){r(o,t,i(t),n)}),o}function K(t,e){return t&&se(e,O(e),t)}function Q(t,e,n){"__proto__"==e&&_r?_r(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function J(t,e){for(var n=-1,r=e.length,i=A(r),o=null==t;++n<r;)i[n]=o?es:Rn(t,e[n]);return i}function Z(t,e,n){return t==t&&(n!==es&&(t=t<=n?t:n),e!==es&&(t=e<=t?t:e)),t}function v(n,r,i,t,e,o){var s,a=1&r,u=2&r,c=4&r;if((s=i?e?i(n,t,e,o):i(n):s)===es){if(!C(n))return n;var l,h,f,t=M(n);if(t){if(p=(d=n).length,f=d.constructor(p),p&&"string"==typeof d[0]&&P.call(d,"index")&&(f.index=d.index,f.input=d.input),s=f,!a)return x(n,s)}else{var p=R(n),d=p==$s||p==Xs;if(Ui(n))return te(n,a);if(p==ls||p==os||d&&!e){if(s=u||d?{}:qe(n),!a)return u?(h=f=n,h=(l=s)&&se(h,E(h),l),se(f,ui(f),h)):(h=K(s,l=n),se(l,ai(l),h))}else{if(!gs[p])return e?n:{};s=function(t,e,n,r){var i=t.constructor;switch(e){case Zs:return ee(t);case ss:case as:return new i(+t);case ps:return function(t,e){e=e?ee(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.byteLength)}(t,r);case ta:case ea:case na:case ra:case ia:case oa:case sa:case aa:case ua:return ne(t,r);case us:return function(t,e,n){return Go(e?n(Ko(t),1):Ko(t),ys,new t.constructor)}(t,r,n);case cs:case Ks:return new i(t);case hs:return function(t){var e=new t.constructor(t.source,Da.exec(t));return e.lastIndex=t.lastIndex,e}(t);case fs:return function(t,e,n){return Go(e?n(Jo(t),1):Jo(t),bs,new t.constructor)}(t,r,n);case Qs:return function(t){return Vr?I(Vr.call(t)):{}}(t)}}(n,p,v,a)}}e=(o=o||new w).get(n);if(e)return e;o.set(n,s);var g=t?es:(c?u?Re:De:u?E:O)(n);Bo(g||n,function(t,e){g&&(t=n[e=t]),$(s,e,v(t,r,i,e,n,o))})}return s}function tt(t,e,n){var r=n.length;if(null==t)return!r;for(t=I(t);r--;){var i=n[r],o=e[i],s=t[i];if(s===es&&!(i in t)||!o(s))return!1}return!0}function et(t,e,n){if("function"!=typeof t)throw new j(ns);return hi(function(){t.apply(es,n)},e)}function nt(t,e,n,r){var i=-1,o=Cs,s=!0,a=t.length,u=[],c=e.length;if(a){n&&(e=zo(e,$o(n))),r?(o=ks,s=!1):200<=e.length&&(o=Xo,s=!1,e=new B(e));t:for(;++i<a;){var l=t[i],h=null==n?l:n(l),l=r||0!==l?l:0;if(s&&h==h){for(var f=c;f--;)if(e[f]===h)continue t;u.push(l)}else o(e,h,r)||u.push(l)}}return u}function rt(t,r){var i=!0;return Jr(t,function(t,e,n){return i=!!r(t,e,n)}),i}function it(t,e,n){for(var r=-1,i=t.length;++r<i;){var o,s,a=t[r],u=e(a);null!=u&&(o===es?u==u&&!y(u):n(u,o))&&(o=u,s=a)}return s}function ot(t,r){var i=[];return Jr(t,function(t,e,n){r(t,e,n)&&i.push(t)}),i}function u(t,e,n,r,i){var o=-1,s=t.length;for(n=n||ze,i=i||[];++o<s;){var a=t[o];0<e&&n(a)?1<e?u(a,e-1,n,r,i):Uo(i,a):r||(i[i.length]=a)}return i}function a(t,e){return t&&ti(t,e,O)}function st(t,e){return t&&ei(t,e,O)}function at(e,t){return qo(t,function(t){return Sn(e[t])})}function ut(t,e){for(var n=0,r=(e=Jt(e,t)).length;null!=t&&n<r;)t=t[en(e[n++])];return n&&n==r?t:es}function ct(t,e,n){e=e(t);return M(t)?e:Uo(e,n(t))}function n(t){{if(null==t)return t===es?"[object Undefined]":"[object Null]";if(wr&&wr in I(t)){var e=t,n=P.call(e,wr),r=e[wr];try{e[wr]=es;var i=!0}catch(e){}var o=ur.call(e);return i&&(n?e[wr]=r:delete e[wr]),o}return ur.call(t)}}function lt(t,e){return e<t}function ht(t,e){return null!=t&&P.call(t,e)}function ft(t,e){return null!=t&&e in I(t)}function pt(t,e,n){for(var r=n?ks:Cs,i=t[0].length,o=t.length,s=o,a=A(o),u=1/0,c=[];s--;){var l=t[s];s&&e&&(l=zo(l,$o(e))),u=D(l.length,u),a[s]=!n&&(e||120<=i&&120<=l.length)?new B(s&&l):es}var l=t[0],h=-1,f=a[0];t:for(;++h<i&&c.length<u;){var p=l[h],d=e?e(p):p,p=n||0!==p?p:0;if(!(f?Xo(f,d):r(c,d,n))){for(s=o;--s;){var g=a[s];if(!(g?Xo(g,d):r(t[s],d,n)))continue t}f&&f.push(d),c.push(p)}}return c}function dt(t,e,n){e=null==(t=Qe(t,e=Jt(e,t)))?t:t[en(r(e))];return null==e?es:Ho(e,t,n)}function gt(t){return o(t)&&n(t)==os}function mt(t,e,n,r,i){return t===e||(null==t||null==e||!o(t)&&!o(e)?t!=t&&e!=e:function(t,e,n,r,i,o){var s=M(t),a=M(e),u=s?Gs:R(t),a=a?Gs:R(e),c=(u=u==os?ls:u)==ls,l=(a=a==os?ls:a)==ls,a=u==a;if(a&&Ui(t)){if(!Ui(e))return!1;c=!(s=!0)}if(a&&!c)return o=o||new w,s||Xi(t)?Pe(t,e,n,r,i,o):function(t,e,n,r,i,o,s){switch(n){case ps:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case Zs:return!(t.byteLength!=e.byteLength||!o(new fr(t),new fr(e)));case ss:case as:case cs:return Cn(+t,+e);case Vs:return t.name==e.name&&t.message==e.message;case hs:case Ks:return t==e+"";case us:var a=Ko;case fs:var u=1&r;if(a=a||Jo,t.size!=e.size&&!u)return!1;u=s.get(t);if(u)return u==e;r|=2,s.set(t,e);u=Pe(a(t),a(e),r,i,o,s);return s.delete(t),u;case Qs:if(Vr)return Vr.call(t)==Vr.call(e)}return!1}(t,e,u,n,r,i,o);if(!(1&n)){s=c&&P.call(t,"__wrapped__"),u=l&&P.call(e,"__wrapped__");if(s||u)return c=s?t.value():t,l=u?e.value():e,o=o||new w,i(c,l,n,r,o)}return!!a&&(o=o||new w,function(t,e,n,r,i,o){var s=1&n,a=De(t),u=a.length,c=De(e).length;if(u!=c&&!s)return!1;for(var l=u;l--;){var h=a[l];if(!(s?h in e:P.call(e,h)))return!1}c=o.get(t);if(c&&o.get(e))return c==e;var f=!0;o.set(t,e),o.set(e,t);for(var p=s;++l<u;){h=a[l];var d,g=t[h],m=e[h];if(!((d=r?s?r(m,g,h,e,t,o):r(g,m,h,t,e,o):d)===es?g===m||i(g,m,n,r,o):d)){f=!1;break}p=p||"constructor"==h}{var v;f&&!p&&(c=t.constructor,v=e.constructor,c!=v&&"constructor"in t&&"constructor"in e&&!("function"==typeof c&&c instanceof c&&"function"==typeof v&&v instanceof v)&&(f=!1))}return o.delete(t),o.delete(e),f}(t,e,n,r,i,o))}(t,e,n,r,mt,i))}function vt(t,e,n,r){var i=n.length,o=i,s=!r;if(null==t)return!o;for(t=I(t);i--;){var a=n[i];if(s&&a[2]?a[1]!==t[a[0]]:!(a[0]in t))return!1}for(;++i<o;){var u=(a=n[i])[0],c=t[u],l=a[1];if(s&&a[2]){if(c===es&&!(u in t))return!1}else{var h,f=new w;if(!((h=r?r(c,l,u,t,e,f):h)===es?mt(l,c,3,r,f):h))return!1}}return!0}function yt(t){var e;return!(!C(t)||(e=t,ar&&ar in e))&&(Sn(t)?hr:Wa).test(nn(t))}function bt(t){return"function"==typeof t?t:null==t?T:"object"==typeof t?M(t)?St(t[0],t[1]):kt(t):Vn(t)}function wt(t){if(!$e(t))return Ir(t);var e,n=[];for(e in I(t))P.call(t,e)&&"constructor"!=e&&n.push(e);return n}function _t(t){if(C(t)){var e,n=$e(t),r=[];for(e in t)("constructor"!=e||!n&&P.call(t,e))&&r.push(e);return r}var i=t,o=[];if(null!=i)for(var s in I(i))o.push(s);return o}function xt(t,e){return t<e}function Ct(t,r){var i=-1,o=p(t)?A(t.length):[];return Jr(t,function(t,e,n){o[++i]=r(t,e,n)}),o}function kt(e){var n=Fe(e);return 1==n.length&&n[0][2]?Ye(n[0][0],n[0][1]):function(t){return t===e||vt(t,e,n)}}function St(n,r){return Ge(n)&&Xe(r)?Ye(en(n),r):function(t){var e=Rn(t,n);return e===es&&e===r?Mn(t,n):mt(r,e,3)}}function Ot(g,m,v,y,b){g!==m&&ti(m,function(t,e){var n,r,i,o,s,a,u,c,l,h,f,p,d;C(t)?(b=b||new w,r=m,o=v,s=Ot,a=y,u=b,f=(n=g)[i=e],p=r[i],(d=u.get(p))?V(n,i,d):(d=a?a(f,p,i+"",n,r,u):es,(r=d===es)&&(c=M(p),l=!c&&Ui(p),h=!c&&!l&&Xi(p),d=p,c||l||h?d=M(f)?f:_(f)?x(f):l?te(p,!(r=!1)):h?ne(p,!(r=!1)):[]:An(p)||qi(p)?qi(d=f)?d=Dn(f):(!C(f)||o&&Sn(f))&&(d=qe(p)):r=!1),r&&(u.set(p,d),s(d,p,o,a,u),u.delete(p)),V(n,i,d))):(c=y?y(g[e],t,e+"",g,m,b):es,V(g,e,c=c===es?t:c))},E)}function Et(t,e){var n=t.length;if(n)return Ue(e+=e<0?n:0,n)?t[e]:es}function Tt(t,r,l){var i=-1;r=zo(r.length?r:[T],$o(h()));var e=Ct(t,function(e,t,n){return{criteria:zo(r,function(t){return t(e)}),index:++i,value:e}}),t=function(t,e){for(var n=l,r=-1,i=t.criteria,o=e.criteria,s=i.length,a=n.length;++r<s;){var u,c=re(i[r],o[r]);if(c)return a<=r?c:(u=n[r],c*("desc"==u?-1:1))}return t.index-e.index},n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}function At(t,e,n){for(var r=-1,i=e.length,o={};++r<i;){var s=e[r],a=ut(t,s);n(a,s)&&Mt(o,Jt(s,t),a)}return o}function It(t,e,n,r){var i=r?As:Vo,o=-1,s=e.length,a=t;for(t===e&&(e=x(e)),n&&(a=zo(t,$o(n)));++o<s;)for(var u=0,c=e[o],l=n?n(c):c;-1<(u=i(a,l,u,r));)a!==t&&vr.call(a,u,1),vr.call(t,u,1);return t}function jt(t,e){for(var n=t?e.length:0,r=n-1;n--;){var i,o=e[n];n!=r&&o===i||(Ue(i=o)?vr.call(t,o,1):Ut(t,o))}}function Pt(t,e){return t+Or(Nr()*(e-t+1))}function Nt(t,e){var n="";if(!(!t||e<1||rs<e))for(;e%2&&(n+=t),(e=Or(e/2))&&(t+=t),e;);return n}function s(t,e){return fi(Ke(t,e,T),t+"")}function Dt(t){return z(Ln(t))}function Rt(t,e){t=Ln(t);return tn(t,Z(e,0,t.length))}function Mt(t,e,n,r){if(C(t))for(var i=-1,o=(e=Jt(e,t)).length,s=o-1,a=t;null!=a&&++i<o;){var u,c=en(e[i]),l=n;i!=s&&(u=a[c],(l=r?r(u,c,a):es)===es&&(l=C(u)?u:Ue(e[i+1])?[]:{})),$(a,c,l),a=a[c]}return t}function Wt(t){return tn(Ln(t))}function c(t,e,n){var r=-1,i=t.length;(n=i<n?i:n)<0&&(n+=i),i=n<(e=e<0?i<-e?0:i+e:e)?0:n-e>>>0,e>>>=0;for(var o=A(i);++r<i;)o[r]=t[r+e];return o}function Lt(t,r){var i;return Jr(t,function(t,e,n){return!(i=r(t,e,n))}),!!i}function Ft(t,e,n){var r=0,i=null==t?r:t.length;if("number"==typeof e&&e==e&&i<=2147483647){for(;r<i;){var o=r+i>>>1,s=t[o];null!==s&&!y(s)&&(n?s<=e:s<e)?r=1+o:i=o}return i}return Ht(t,e,T,n)}function Ht(t,e,n,r){e=n(e);for(var i=0,o=null==t?0:t.length,s=e!=e,a=null===e,u=y(e),c=e===es;i<o;){var l=Or((i+o)/2),h=n(t[l]),f=h!==es,p=null===h,d=h==h,g=y(h),d=s?r||d:c?d&&(r||f):a?d&&f&&(r||!p):u?d&&f&&!p&&(r||!g):!p&&!g&&(r?h<=e:h<e);d?i=l+1:o=l}return D(o,4294967294)}function Bt(t,e){for(var n=-1,r=t.length,i=0,o=[];++n<r;){var s,a=t[n],u=e?e(a):a;n&&Cn(u,s)||(s=u,o[i++]=0===a?0:a)}return o}function qt(t){return"number"==typeof t?t:y(t)?zs:+t}function l(t){var e;return"string"==typeof t?t:M(t)?zo(t,l)+"":y(t)?$r?$r.call(t):"":"0"==(e=t+"")&&1/t==-1/0?"-0":e}function zt(t,e,n){var r=-1,i=Cs,o=t.length,s=!0,a=[],u=a;if(n)s=!1,i=ks;else if(200<=o){var c=e?null:oi(t);if(c)return Jo(c);s=!1,i=Xo,u=new B}else u=e?[]:a;t:for(;++r<o;){var l=t[r],h=e?e(l):l,l=n||0!==l?l:0;if(s&&h==h){for(var f=u.length;f--;)if(u[f]===h)continue t;e&&u.push(h),a.push(l)}else i(u,h,n)||(u!==a&&u.push(h),a.push(l))}return a}function Ut(t,e){return null==(t=Qe(t,e=Jt(e,t)))||delete t[en(r(e))]}function Gt(t,e,n,r){return Mt(t,e,n(ut(t,e)),r)}function Vt(t,e,n,r){for(var i=t.length,o=r?i:-1;(r?o--:++o<i)&&e(t[o],o,t););return n?c(t,r?0:o,r?o+1:i):c(t,r?o+1:0,r?i:o)}function $t(t,e){var n=t;return Go(e,function(t,e){return e.func.apply(e.thisArg,Uo([t],e.args))},n=t instanceof m?t.value():n)}function Xt(t,e,n){var r=t.length;if(r<2)return r?zt(t[0]):[];for(var i=-1,o=A(r);++i<r;)for(var s=t[i],a=-1;++a<r;)a!=i&&(o[i]=nt(o[i]||s,t[a],e,n));return zt(u(o,1),e,n)}function Yt(t,e,n){for(var r=-1,i=t.length,o=e.length,s={};++r<i;){var a=r<o?e[r]:es;n(s,t[r],a)}return s}function Kt(t){return _(t)?t:[]}function Qt(t){return"function"==typeof t?t:T}function Jt(t,e){return M(t)?t:Ge(t,e)?[t]:pi(S(t))}function Zt(t,e,n){var r=t.length;return n=n===es?r:n,!e&&r<=n?t:c(t,e,n)}function te(t,e){return e?t.slice():(e=t.length,e=pr?pr(e):new t.constructor(e),t.copy(e),e)}function ee(t){var e=new t.constructor(t.byteLength);return new fr(e).set(new fr(t)),e}function ne(t,e){e=e?ee(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.length)}function re(t,e){if(t!==e){var n=t!==es,r=null===t,i=t==t,o=y(t),s=e!==es,a=null===e,u=e==e,c=y(e);if(!a&&!c&&!o&&e<t||o&&s&&u&&!a&&!c||r&&s&&u||!n&&u||!i)return 1;if(!r&&!o&&!c&&t<e||c&&n&&i&&!r&&!o||a&&n&&i||!s&&i||!u)return-1}return 0}function ie(t,e,n,r){for(var i=-1,o=t.length,s=n.length,a=-1,u=e.length,c=N(o-s,0),l=A(u+c),h=!r;++a<u;)l[a]=e[a];for(;++i<s;)(h||i<o)&&(l[n[i]]=t[i]);for(;c--;)l[a++]=t[i++];return l}function oe(t,e,n,r){for(var i=-1,o=t.length,s=-1,a=n.length,u=-1,c=e.length,l=N(o-a,0),h=A(l+c),f=!r;++i<l;)h[i]=t[i];for(var p=i;++u<c;)h[p+u]=e[u];for(;++s<a;)(f||i<o)&&(h[p+n[s]]=t[i++]);return h}function x(t,e){var n=-1,r=t.length;for(e=e||A(r);++n<r;)e[n]=t[n];return e}function se(t,e,n,r){var i=!n;n=n||{};for(var o=-1,s=e.length;++o<s;){var a=e[o],u=r?r(n[a],t[a],a,n,t):es;(i?Q:$)(n,a,u=u===es?t[a]:u)}return n}function ae(i,o){return function(t,e){var n=M(t)?ws:Y,r=o?o():{};return n(t,i,h(e,2),r)}}function ue(a){return s(function(t,e){var n=-1,r=e.length,i=1<r?e[r-1]:es,o=2<r?e[2]:es,i=3<a.length&&"function"==typeof i?(r--,i):es;for(o&&f(e[0],e[1],o)&&(i=r<3?es:i,r=1),t=I(t);++n<r;){var s=e[n];s&&a(t,s,n,i)}return t})}function ce(o,s){return function(t,e){if(null!=t){if(!p(t))return o(t,e);for(var n=t.length,r=s?n:-1,i=I(t);(s?r--:++r<n)&&!1!==e(i[r],r,i););}return t}}function le(u){return function(t,e,n){for(var r=-1,i=I(t),o=n(t),s=o.length;s--;){var a=o[u?s:++r];if(!1===e(i[a],a,i))break}return t}}function he(r){return function(t){var e=Yo(t=S(t))?ts(t):es,n=e?e[0]:t.charAt(0),e=e?Zt(e,1).join(""):t.slice(1);return n[r]()+e}}function fe(e){return function(t){return Go(Bn(Hn(t).replace(za,"")),e,"")}}function pe(r){return function(){var t=arguments;switch(t.length){case 0:return new r;case 1:return new r(t[0]);case 2:return new r(t[0],t[1]);case 3:return new r(t[0],t[1],t[2]);case 4:return new r(t[0],t[1],t[2],t[3]);case 5:return new r(t[0],t[1],t[2],t[3],t[4]);case 6:return new r(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new r(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var e=Xr(r.prototype),n=r.apply(e,t);return C(n)?n:e}}function de(o,s,a){var u=pe(o);return function t(){for(var e=arguments.length,n=A(e),r=e,i=We(t);r--;)n[r]=arguments[r];i=e<3&&n[0]!==i&&n[e-1]!==i?[]:Qo(n,i);return(e-=i.length)<a?Se(o,s,ve,t.placeholder,es,n,i,es,es,a-e):Ho(this&&this!==ms&&this instanceof t?u:o,this,n)}}function ge(o){return function(t,e,n){var r,i=I(t),e=(p(t)||(r=h(e,3),t=O(t),e=function(t){return r(i[t],t,i)}),o(t,e,n));return-1<e?i[r?t[e]:e]:es}}function me(u){return Ne(function(i){var o=i.length,t=o,e=g.prototype.thru;for(u&&i.reverse();t--;){var n=i[t];if("function"!=typeof n)throw new j(ns);e&&!a&&"wrapper"==Me(n)&&(a=new g([],!0))}for(t=a?t:o;++t<o;)var r=Me(n=i[t]),s="wrapper"==r?si(n):es,a=s&&Ve(s[0])&&424==s[1]&&!s[4].length&&1==s[9]?a[Me(s[0])].apply(a,s[3]):1==n.length&&Ve(n)?a[r]():a.thru(n);return function(){var t=arguments,e=t[0];if(a&&1==t.length&&M(e))return a.plant(e).value();for(var n=0,r=o?i[n].apply(this,t):e;++n<o;)r=i[n].call(this,r);return r}})}function ve(s,a,u,c,l,h,f,p,d,g){var m=128&a,v=1&a,y=2&a,b=24&a,w=512&a,_=y?es:pe(s);return function t(){for(var e,n,r=arguments.length,i=A(r),o=r;o--;)i[o]=arguments[o];return b&&(n=function(t,e){for(var n=t.length,r=0;n--;)t[n]===e&&++r;return r}(i,e=We(t))),c&&(i=ie(i,c,l,b)),h&&(i=oe(i,h,f,b)),r-=n,b&&r<g?(n=Qo(i,e),Se(s,a,ve,t.placeholder,u,i,n,p,d,g-r)):(e=v?u:this,n=y?e[s]:s,r=i.length,p?i=function(t,e){for(var n=t.length,r=D(e.length,n),i=x(t);r--;){var o=e[r];t[r]=Ue(o,n)?i[o]:es}return t}(i,p):w&&1<r&&i.reverse(),m&&d<r&&(i.length=d),(n=this&&this!==ms&&this instanceof t?_||pe(n):n).apply(e,i))}}function ye(n,s){return function(t,e){return t=t,r=n,i=s(e),o={},a(t,function(t,e,n){r(o,i(t),e,n)}),o;var r,i,o}}function be(r,i){return function(t,e){var n;if(t===es&&e===es)return i;if(t!==es&&(n=t),e!==es){if(n===es)return e;e=("string"==typeof t||"string"==typeof e?(t=l(t),l):(t=qt(t),qt))(e),n=r(t,e)}return n}}function we(r){return Ne(function(t){return t=zo(t,$o(h())),s(function(e){var n=this;return r(t,function(t){return Ho(t,n,e)})})})}function _e(t,e){var n=(e=e===es?" ":l(e)).length;return n<2?n?Nt(e,t):e:(n=Nt(e,Sr(t/Zo(e))),Yo(e)?Zt(ts(n),0,t).join(""):n.slice(0,t))}function xe(a,t,u,c){var l=1&t,h=pe(a);return function t(){for(var e=-1,n=arguments.length,r=-1,i=c.length,o=A(i+n),s=this&&this!==ms&&this instanceof t?h:a;++r<i;)o[r]=c[r];for(;n--;)o[r++]=arguments[++e];return Ho(s,l?u:this,o)}}function Ce(c){return function(t,e,n){n&&"number"!=typeof n&&f(t,e,n)&&(e=n=es),t=Pn(t),e===es?(e=t,t=0):e=Pn(e),n=n===es?t<e?1:-1:Pn(n);for(var r=t,i=n,o=c,s=-1,a=N(Sr((e-r)/(i||1)),0),u=A(a);a--;)u[o?a:++s]=r,r+=i;return u}}function ke(n){return function(t,e){return"string"==typeof t&&"string"==typeof e||(t=b(t),e=b(e)),n(t,e)}}function Se(t,e,n,r,i,o,s,a,u,c){var l=8&e,i=(4&(e=(e|(l?32:64))&~(l?64:32))||(e&=-4),[t,e,i,l?o:es,l?s:es,l?es:o,l?es:s,a,u,c]),o=n.apply(es,i);return Ve(t)&&li(o,i),o.placeholder=r,Je(o,t,e)}function Oe(t){var r=Jn[t];return function(t,e){var n;return t=b(t),(e=null==e?0:D(k(e),292))?(n=(S(t)+"e").split("e"),+((n=(S(r(n[0]+"e"+(+n[1]+e)))+"e").split("e"))[0]+"e"+(+n[1]-e))):r(t)}}function Ee(o){return function(t){var e,n,r,i=R(t);return i==us?Ko(t):i==fs?(i=t,n=-1,r=Array(i.size),i.forEach(function(t){r[++n]=[t,t]}),r):zo(o(e=t),function(t){return[t,e[t]]})}}function Te(t,e,n,r,i,o,s,a){var u,c,l,h,f,p,d,g,m,v,y,b,w,_=2&e;if(_||"function"==typeof t)return(u=r?r.length:0)||(e&=-97,r=i=es),s=s===es?s:N(k(s),0),a=a===es?a:k(a),u-=i?i.length:0,64&e&&(l=r,g=i,r=i=es),c=_?es:si(t),l=[t,e,n,r,i,l,g,o,s,a],c&&(g=c,s=(o=l)[1],v=g[1],b=(y=s|v)<131,w=128==v&&8==s||128==v&&256==s&&o[7].length<=g[8]||384==v&&g[7].length<=g[8]&&8==s,(b||w)&&(1&v&&(o[2]=g[2],y|=1&s?0:4),(b=g[3])&&(m=o[3],o[3]=m?ie(m,b,g[4]):b,o[4]=m?Qo(o[3],qs):g[4]),(b=g[5])&&(m=o[5],o[5]=m?oe(m,b,g[6]):b,o[6]=m?Qo(o[5],qs):g[6]),(b=g[7])&&(o[7]=b),128&v&&(o[8]=null==o[8]?g[8]:D(o[8],g[8])),null==o[9]&&(o[9]=g[9]),o[0]=g[0],o[1]=y)),t=l[0],e=l[1],n=l[2],r=l[3],i=l[4],!(a=l[9]=l[9]===es?_?0:t.length:N(l[9]-u,0))&&24&e&&(e&=-25),w=e&&1!=e?8==e||16==e?de(t,e,a):32!=e&&33!=e||i.length?ve.apply(es,l):xe(t,e,n,r):(f=n,p=1&e,d=pe(h=t),function t(){return(this&&this!==ms&&this instanceof t?d:h).apply(p?f:this,arguments)}),Je((c?ni:li)(w,l),t,e);throw new j(ns)}function Ae(t,e,n,r){return t===es||Cn(t,rr[n])&&!P.call(r,n)?e:t}function Ie(t,e,n,r,i,o){return C(t)&&C(e)&&(o.set(e,t),Ot(t,e,es,Ie,o),o.delete(e)),t}function je(t){return An(t)?es:t}function Pe(t,e,n,r,i,o){var s=1&n,a=t.length,u=e.length;if(a!=u&&!(s&&a<u))return!1;u=o.get(t);if(u&&o.get(e))return u==e;var c=-1,l=!0,h=2&n?new B:es;for(o.set(t,e),o.set(e,t);++c<a;){var f,p=t[c],d=e[c];if((f=r?s?r(d,p,c,e,t,o):r(p,d,c,t,e,o):f)!==es){if(f)continue;l=!1;break}if(h){if(!Os(e,function(t,e){return!Xo(h,e)&&(p===t||i(p,t,n,r,o))&&h.push(e)})){l=!1;break}}else if(p!==d&&!i(p,d,n,r,o)){l=!1;break}}return o.delete(t),o.delete(e),l}function Ne(t){return fi(Ke(t,es,an),t+"")}function De(t){return ct(t,O,ai)}function Re(t){return ct(t,E,ui)}function Me(t){for(var e=t.name+"",n=Hr[e],r=P.call(Hr,e)?n.length:0;r--;){var i=n[r],o=i.func;if(null==o||o==t)return i.name}return e}function We(t){return(P.call(d,"placeholder")?d:t).placeholder}function h(){var t=(t=d.iteratee||zn)===zn?bt:t;return arguments.length?t(arguments[0],arguments[1]):t}function Le(t,e){var n,r,t=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?t["string"==typeof e?"string":"hash"]:t.map}function Fe(t){for(var e=O(t),n=e.length;n--;){var r=e[n],i=t[r];e[n]=[r,i,Xe(i)]}return e}function He(t,e){e=e;t=null==(t=t)?es:t[e];return yt(t)?t:es}function Be(t,e,n){for(var r=-1,i=(e=Jt(e,t)).length,o=!1;++r<i;){var s=en(e[r]);if(!(o=null!=t&&n(t,s)))break;t=t[s]}return o||++r!=i?o:!!(i=null==t?0:t.length)&&En(i)&&Ue(s,i)&&(M(t)||qi(t))}function qe(t){return"function"!=typeof t.constructor||$e(t)?{}:Xr(dr(t))}function ze(t){return M(t)||qi(t)||!!(yr&&t&&t[yr])}function Ue(t,e){return!!(e=null==e?rs:e)&&("number"==typeof t||Fa.test(t))&&-1<t&&t%1==0&&t<e}function f(t,e,n){var r;if(C(n))return("number"==(r=typeof e)?p(n)&&Ue(e,n.length):"string"==r&&e in n)&&Cn(n[e],t)}function Ge(t,e){var n;if(!M(t))return"number"==(n=typeof t)||"symbol"==n||"boolean"==n||null==t||y(t)||wa.test(t)||!ba.test(t)||null!=e&&t in I(e)}function Ve(t){var e=Me(t),n=d[e];return"function"==typeof n&&e in m.prototype&&(t===n||(e=si(n))&&t===e[0])}function $e(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||rr)}function Xe(t){return t==t&&!C(t)}function Ye(e,n){return function(t){return null!=t&&t[e]===n&&(n!==es||e in I(t))}}function Ke(o,s,a){return s=N(s===es?o.length-1:s,0),function(){for(var t=arguments,e=-1,n=N(t.length-s,0),r=A(n);++e<n;)r[e]=t[s+e];for(var e=-1,i=A(s+1);++e<s;)i[e]=t[e];return i[s]=a(r),Ho(o,this,i)}}function Qe(t,e){return e.length<2?t:ut(t,c(e,0,-1))}function Je(t,e,n){var r,i,o,e=e+"";return fi(t,(i=(e=(e=t=e).match(Aa))?e[1].split(Ia):[],o=n,Bo(Us,function(t){var e="_."+t[0];o&t[1]&&!Cs(i,e)&&i.push(e)}),e=i.sort(),(n=e.length)?(e[r=n-1]=(1<n?"& ":"")+e[r],e=e.join(2<n?", ":" "),t.replace(Ta,"{\n/* [wrapped with "+e+"] */\n")):t))}function Ze(n){var r=0,i=0;return function(){var t=jr(),e=16-(t-i);if(i=t,0<e){if(800<=++r)return arguments[0]}else r=0;return n.apply(es,arguments)}}function tn(t,e){var n=-1,r=t.length,i=r-1;for(e=e===es?r:e;++n<e;){var o=Pt(n,i),s=t[o];t[o]=t[n],t[n]=s}return t.length=e,t}function en(t){var e;return"string"==typeof t||y(t)?t:"0"==(e=t+"")&&1/t==-1/0?"-0":e}function nn(t){if(null!=t){try{return or.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function rn(t){var e;return t instanceof m?t.clone():((e=new g(t.__wrapped__,t.__chain__)).__actions__=x(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e)}function on(t,e,n){var r=null==t?0:t.length;return r?((n=null==n?0:k(n))<0&&(n=N(r+n,0)),Ts(t,h(e,3),n)):-1}function sn(t,e,n){var r,i=null==t?0:t.length;return i?(r=i-1,n!==es&&(r=k(n),r=n<0?N(i+r,0):D(r,i-1)),Ts(t,h(e,3),r,!0)):-1}function an(t){return(null==t?0:t.length)?u(t,1):[]}function un(t){return t&&t.length?t[0]:es}function r(t){var e=null==t?0:t.length;return e?t[e-1]:es}function cn(t,e){return t&&t.length&&e&&e.length?It(t,e):t}function ln(t){return null==t?t:Dr.call(t)}function hn(e){var n;return e&&e.length?(n=0,e=qo(e,function(t){return _(t)&&(n=N(t.length,n),1)}),Rs(n,function(t){return zo(e,Ps(t))})):[]}function fn(t,e){return t&&t.length?(t=hn(t),null==e?t:zo(t,function(t){return Ho(e,es,t)})):[]}function pn(t){t=d(t);return t.__chain__=!0,t}function dn(t,e){return e(t)}function gn(t,e){return(M(t)?Bo:Jr)(t,h(e,3))}function mn(t,e){return(M(t)?_s:Zr)(t,h(e,3))}function vn(t,e){return(M(t)?zo:Ct)(t,h(e,3))}function yn(t,e,n){return e=n?es:e,e=t&&null==e?t.length:e,Te(t,128,es,es,es,es,e)}function bn(t,e){var n;if("function"!=typeof e)throw new j(ns);return t=k(t),function(){return 0<--t&&(n=e.apply(this,arguments)),t<=1&&(e=es),n}}function wn(r,n,t){function i(t){var e=u,n=c;return u=c=es,d=t,h=r.apply(n,e)}function o(t){var e=t-p;return p===es||n<=e||e<0||m&&l<=t-d}function s(){var t,e=Ni();return o(e)?a(e):void(f=hi(s,(t=n-((e=e)-p),m?D(t,l-(e-d)):t)))}function a(t){return f=es,v&&u?i(t):(u=c=es,h)}function e(){var t=Ni(),e=o(t);if(u=arguments,c=this,p=t,e){if(f===es)return d=t=p,f=hi(s,n),g?i(t):h;if(m)return f=hi(s,n),i(p)}return f===es&&(f=hi(s,n)),h}var u,c,l,h,f,p,d=0,g=!1,m=!1,v=!0;if("function"!=typeof r)throw new j(ns);return n=b(n)||0,C(t)&&(g=!!t.leading,m="maxWait"in t,l=m?N(b(t.maxWait)||0,n):l,v="trailing"in t?!!t.trailing:v),e.cancel=function(){f!==es&&ii(f),d=0,u=p=c=f=es},e.flush=function(){return f===es?h:a(Ni())},e}function _n(r,i){if("function"!=typeof r||null!=i&&"function"!=typeof i)throw new j(ns);function o(){var t=arguments,e=i?i.apply(this,t):t[0],n=o.cache;return n.has(e)?n.get(e):(t=r.apply(this,t),o.cache=n.set(e,t)||n,t)}return o.cache=new(_n.Cache||H),o}function xn(e){if("function"!=typeof e)throw new j(ns);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Cn(t,e){return t===e||t!=t&&e!=e}function p(t){return null!=t&&En(t.length)&&!Sn(t)}function _(t){return o(t)&&p(t)}function kn(t){var e;return!!o(t)&&((e=n(t))==Vs||"[object DOMException]"==e||"string"==typeof t.message&&"string"==typeof t.name&&!An(t))}function Sn(t){return!!C(t)&&((t=n(t))==$s||t==Xs||"[object AsyncFunction]"==t||"[object Proxy]"==t)}function On(t){return"number"==typeof t&&t==k(t)}function En(t){return"number"==typeof t&&-1<t&&t%1==0&&t<=rs}function C(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function o(t){return null!=t&&"object"==typeof t}function Tn(t){return"number"==typeof t||o(t)&&n(t)==cs}function An(t){return!(!o(t)||n(t)!=ls)&&(null===(t=dr(t))||"function"==typeof(t=P.call(t,"constructor")&&t.constructor)&&t instanceof t&&or.call(t)==cr)}function In(t){return"string"==typeof t||!M(t)&&o(t)&&n(t)==Ks}function y(t){return"symbol"==typeof t||o(t)&&n(t)==Qs}function jn(t){if(!t)return[];if(p(t))return(In(t)?ts:x)(t);var e;if(br&&t[br]){for(var n,r=t[br](),i=[];!(n=r.next()).done;)i.push(n.value);return i}return((e=R(t))==us?Ko:e==fs?Jo:Ln)(t)}function Pn(t){return t?(t=b(t))===1/0||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}function k(t){var t=Pn(t),e=t%1;return t==t?e?t-e:t:0}function Nn(t){return t?Z(k(t),0,is):0}function b(t){if("number"==typeof t)return t;if(y(t))return zs;if("string"!=typeof(t=C(t)?C(e="function"==typeof t.valueOf?t.valueOf():t)?e+"":e:t))return 0===t?t:+t;t=t.replace(Sa,"");var e=Ma.test(t);return e||La.test(t)?Ka(t.slice(2),e?2:8):Ra.test(t)?zs:+t}function Dn(t){return se(t,E(t))}function S(t){return null==t?"":l(t)}function Rn(t,e,n){t=null==t?es:ut(t,e);return t===es?n:t}function Mn(t,e){return null!=t&&Be(t,e,ft)}function O(t){return(p(t)?q:wt)(t)}function E(t){return p(t)?q(t,!0):_t(t)}function Wn(t,n){var e;return null==t?{}:(e=zo(Re(t),function(t){return[t]}),n=h(n),At(t,e,function(t,e){return n(t,e[0])}))}function Ln(t){return null==t?[]:Ms(t,O(t))}function Fn(t){return _o(S(t).toLowerCase())}function Hn(t){return(t=S(t))&&t.replace(Ha,iu).replace(Ua,"")}function Bn(t,e,n){return t=S(t),(e=n?es:e)===es?(n=t,Va.test(n)?t.match(Ga)||[]:t.match(ja)||[]):t.match(e)||[]}function qn(t){return function(){return t}}function T(t){return t}function zn(t){return bt("function"==typeof t?t:v(t,1))}function Un(r,e,t){var n=O(e),i=at(e,n),o=(null!=t||C(e)&&(i.length||!n.length)||(t=e,e=r,r=this,i=at(e,O(e))),!(C(t)&&"chain"in t&&!t.chain)),s=Sn(r);return Bo(i,function(t){var n=e[t];r[t]=n,s&&(r.prototype[t]=function(){var t,e=this.__chain__;return o||e?(((t=r(this.__wrapped__)).__actions__=x(this.__actions__)).push({func:n,args:arguments,thisArg:r}),t.__chain__=e,t):n.apply(r,Uo([this.value()],arguments))})}),r}function Gn(){}function Vn(t){return Ge(t)?Ps(en(t)):(e=t,function(t){return ut(t,e)});var e}function $n(){return[]}function Xn(){return!1}var A=(t=null==t?ms:vs.defaults(ms.Object(),t,vs.pick(ms,$a))).Array,Yn=t.Date,Kn=t.Error,Qn=t.Function,Jn=t.Math,I=t.Object,Zn=t.RegExp,tr=t.String,j=t.TypeError,er=A.prototype,nr=Qn.prototype,rr=I.prototype,ir=t["__core-js_shared__"],or=nr.toString,P=rr.hasOwnProperty,sr=0,ar=(nr=/[^.]+$/.exec(ir&&ir.keys&&ir.keys.IE_PROTO||""))?"Symbol(src)_1."+nr:"",ur=rr.toString,cr=or.call(I),lr=ms._,hr=Zn("^"+or.call(P).replace(Ca,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),nr=Qa?t.Buffer:es,e=t.Symbol,fr=t.Uint8Array,pr=nr?nr.allocUnsafe:es,dr=Hs(I.getPrototypeOf,I),gr=I.create,mr=rr.propertyIsEnumerable,vr=er.splice,yr=e?e.isConcatSpreadable:es,br=e?e.iterator:es,wr=e?e.toStringTag:es,_r=function(){try{var t=He(I,"defineProperty");return t({},"",{}),t}catch(t){}}(),xr=t.clearTimeout!==ms.clearTimeout&&t.clearTimeout,Cr=Yn&&Yn.now!==ms.Date.now&&Yn.now,kr=t.setTimeout!==ms.setTimeout&&t.setTimeout,Sr=Jn.ceil,Or=Jn.floor,Er=I.getOwnPropertySymbols,nr=nr?nr.isBuffer:es,Tr=t.isFinite,Ar=er.join,Ir=Hs(I.keys,I),N=Jn.max,D=Jn.min,jr=Yn.now,Pr=t.parseInt,Nr=Jn.random,Dr=er.reverse,Yn=He(t,"DataView"),Rr=He(t,"Map"),Mr=He(t,"Promise"),Wr=He(t,"Set"),t=He(t,"WeakMap"),Lr=He(I,"create"),Fr=t&&new t,Hr={},Br=nn(Yn),qr=nn(Rr),zr=nn(Mr),Ur=nn(Wr),Gr=nn(t),e=e?e.prototype:es,Vr=e?e.valueOf:es,$r=e?e.toString:es,Xr=function(t){if(!C(t))return{};if(gr)return gr(t);Yr.prototype=t;t=new Yr;return Yr.prototype=es,t};function Yr(){}d.templateSettings={escape:ma,evaluate:va,interpolate:ya,variable:"",imports:{_:d}},(d.prototype=W.prototype).constructor=d,(g.prototype=Xr(W.prototype)).constructor=g,(m.prototype=Xr(W.prototype)).constructor=m,L.prototype.clear=function(){this.__data__=Lr?Lr(null):{},this.size=0},L.prototype.delete=function(t){return t=this.has(t)&&delete this.__data__[t],this.size-=t?1:0,t},L.prototype.get=function(t){var e,n=this.__data__;return Lr?(e=n[t])===Bs?es:e:P.call(n,t)?n[t]:es},L.prototype.has=function(t){var e=this.__data__;return Lr?e[t]!==es:P.call(e,t)},L.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=Lr&&e===es?Bs:e,this},F.prototype.clear=function(){this.__data__=[],this.size=0},F.prototype.delete=function(t){var e=this.__data__;return!((t=X(e,t))<0)&&(t==e.length-1?e.pop():vr.call(e,t,1),--this.size,!0)},F.prototype.get=function(t){var e=this.__data__;return(t=X(e,t))<0?es:e[t][1]},F.prototype.has=function(t){return-1<X(this.__data__,t)},F.prototype.set=function(t,e){var n=this.__data__,r=X(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},H.prototype.clear=function(){this.size=0,this.__data__={hash:new L,map:new(Rr||F),string:new L}},H.prototype.delete=function(t){return t=Le(this,t).delete(t),this.size-=t?1:0,t},H.prototype.get=function(t){return Le(this,t).get(t)},H.prototype.has=function(t){return Le(this,t).has(t)},H.prototype.set=function(t,e){var n=Le(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},B.prototype.add=B.prototype.push=function(t){return this.__data__.set(t,Bs),this},B.prototype.has=function(t){return this.__data__.has(t)},w.prototype.clear=function(){this.__data__=new F,this.size=0},w.prototype.delete=function(t){var e=this.__data__,t=e.delete(t);return this.size=e.size,t},w.prototype.get=function(t){return this.__data__.get(t)},w.prototype.has=function(t){return this.__data__.has(t)},w.prototype.set=function(t,e){var n=this.__data__;if(n instanceof F){var r=n.__data__;if(!Rr||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new H(r)}return n.set(t,e),this.size=n.size,this};var Kr,Qr,Jr=ce(a),Zr=ce(st,!0),ti=le(),ei=le(!0),ni=Fr?function(t,e){return Fr.set(t,e),t}:T,e=_r?function(t,e){return _r(t,"toString",{configurable:!0,enumerable:!1,value:qn(e),writable:!0})}:T,ri=s,ii=xr||function(t){return ms.clearTimeout(t)},oi=Wr&&1/Jo(new Wr([,-0]))[1]==1/0?function(t){return new Wr(t)}:Gn,si=Fr?function(t){return Fr.get(t)}:Gn,ai=Er?function(e){return null==e?[]:(e=I(e),qo(Er(e),function(t){return mr.call(e,t)}))}:$n,ui=Er?function(t){for(var e=[];t;)Uo(e,ai(t)),t=dr(t);return e}:$n,R=n,ci=((Yn&&R(new Yn(new ArrayBuffer(1)))!=ps||Rr&&R(new Rr)!=us||Mr&&R(Mr.resolve())!=Ys||Wr&&R(new Wr)!=fs||t&&R(new t)!=Js)&&(R=function(t){var e=n(t),t=e==ls?t.constructor:es,t=t?nn(t):"";if(t)switch(t){case Br:return ps;case qr:return us;case zr:return Ys;case Ur:return fs;case Gr:return Js}return e}),ir?Sn:Xn),li=Ze(ni),hi=kr||function(t,e){return ms.setTimeout(t,e)},fi=Ze(e),pi=(Kr=(xr=_n(xr=function(t){var i=[];return _a.test(t)&&i.push(""),t.replace(xa,function(t,e,n,r){i.push(n?r.replace(Pa,"$1"):e||t)}),i},function(t){return 500===Kr.size&&Kr.clear(),t})).cache,xr),Yn=s(function(t,e){return _(t)?nt(t,u(e,1,_,!0)):[]}),Mr=s(function(t,e){var n=r(e);return _(n)&&(n=es),_(t)?nt(t,u(e,1,_,!0),h(n,2)):[]}),t=s(function(t,e){var n=r(e);return _(n)&&(n=es),_(t)?nt(t,u(e,1,_,!0),es,n):[]}),ir=s(function(t){var e=zo(t,Kt);return e.length&&e[0]===t[0]?pt(e):[]}),kr=s(function(t){var e=r(t),n=zo(t,Kt);return e===r(n)?e=es:n.pop(),n.length&&n[0]===t[0]?pt(n,h(e,2)):[]}),e=s(function(t){var e=r(t),n=zo(t,Kt);return(e="function"==typeof e?e:es)&&n.pop(),n.length&&n[0]===t[0]?pt(n,es,e):[]}),xr=s(cn),di=Ne(function(t,e){var n=null==t?0:t.length,r=J(t,e);return jt(t,zo(e,function(t){return Ue(t,n)?+t:t}).sort(re)),r}),gi=s(function(t){return zt(u(t,1,_,!0))}),mi=s(function(t){var e=r(t);return _(e)&&(e=es),zt(u(t,1,_,!0),h(e,2))}),vi=s(function(t){var e="function"==typeof(e=r(t))?e:es;return zt(u(t,1,_,!0),es,e)}),yi=s(function(t,e){return _(t)?nt(t,e):[]}),bi=s(function(t){return Xt(qo(t,_))}),wi=s(function(t){var e=r(t);return _(e)&&(e=es),Xt(qo(t,_),h(e,2))}),_i=s(function(t){var e="function"==typeof(e=r(t))?e:es;return Xt(qo(t,_),es,e)}),xi=s(hn),Ci=s(function(t){var e=t.length,e="function"==typeof(e=1<e?t[e-1]:es)?(t.pop(),e):es;return fn(t,e)}),ki=Ne(function(e){function t(t){return J(t,e)}var n=e.length,r=n?e[0]:0,i=this.__wrapped__;return!(1<n||this.__actions__.length)&&i instanceof m&&Ue(r)?((i=i.slice(r,+r+(n?1:0))).__actions__.push({func:dn,args:[t],thisArg:es}),new g(i,this.__chain__).thru(function(t){return n&&!t.length&&t.push(es),t})):this.thru(t)}),Si=ae(function(t,e,n){P.call(t,n)?++t[n]:Q(t,n,1)}),Oi=ge(on),Ei=ge(sn),Ti=ae(function(t,e,n){P.call(t,n)?t[n].push(e):Q(t,n,[e])}),Ai=s(function(t,e,n){var r=-1,i="function"==typeof e,o=p(t)?A(t.length):[];return Jr(t,function(t){o[++r]=i?Ho(e,t,n):dt(t,e,n)}),o}),Ii=ae(function(t,e,n){Q(t,n,e)}),ji=ae(function(t,e,n){t[n?0:1].push(e)},function(){return[[],[]]}),Pi=s(function(t,e){var n;return null==t?[]:(1<(n=e.length)&&f(t,e[0],e[1])?e=[]:2<n&&f(e[0],e[1],e[2])&&(e=[e[0]]),Tt(t,u(e,1),[]))}),Ni=Cr||function(){return ms.Date.now()},Di=s(function(t,e,n){var r,i=1;return n.length&&(r=Qo(n,We(Di)),i|=32),Te(t,i,e,n,r)}),Ri=s(function(t,e,n){var r,i=3;return n.length&&(r=Qo(n,We(Ri)),i|=32),Te(e,i,t,n,r)}),Cr=s(function(t,e){return et(t,1,e)}),Mi=s(function(t,e,n){return et(t,b(e)||0,n)}),ri=(_n.Cache=H,ri(function(r,i){var o=(i=1==i.length&&M(i[0])?zo(i[0],$o(h())):zo(u(i,1),$o(h()))).length;return s(function(t){for(var e=-1,n=D(t.length,o);++e<n;)t[e]=i[e].call(this,t[e]);return Ho(r,this,t)})})),Wi=s(function(t,e){var n=Qo(e,We(Wi));return Te(t,32,es,e,n)}),Li=s(function(t,e){var n=Qo(e,We(Li));return Te(t,64,es,e,n)}),Fi=Ne(function(t,e){return Te(t,256,es,es,es,e)}),Hi=ke(lt),Bi=ke(function(t,e){return e<=t}),qi=gt(function(){return arguments}())?gt:function(t){return o(t)&&P.call(t,"callee")&&!mr.call(t,"callee")},M=A.isArray,zi=Ja?$o(Ja):function(t){return o(t)&&n(t)==Zs},Ui=nr||Xn,nr=Za?$o(Za):function(t){return o(t)&&n(t)==as},Gi=tu?$o(tu):function(t){return o(t)&&R(t)==us},Vi=eu?$o(eu):function(t){return o(t)&&n(t)==hs},$i=nu?$o(nu):function(t){return o(t)&&R(t)==fs},Xi=ru?$o(ru):function(t){return o(t)&&En(t.length)&&!!ds[n(t)]},Yi=ke(xt),Ki=ke(function(t,e){return t<=e}),Qi=ue(function(t,e){if($e(e)||p(e))se(e,O(e),t);else for(var n in e)P.call(e,n)&&$(t,n,e[n])}),Ji=ue(function(t,e){se(e,E(e),t)}),Zi=ue(function(t,e,n,r){se(e,E(e),t,r)}),to=ue(function(t,e,n,r){se(e,O(e),t,r)}),eo=Ne(J),no=s(function(t){return t.push(es,Ae),Ho(Zi,es,t)}),ro=s(function(t){return t.push(es,Ie),Ho(uo,es,t)}),io=ye(function(t,e,n){t[e]=n},qn(T)),oo=ye(function(t,e,n){P.call(t,e)?t[e].push(n):t[e]=[n]},h),so=s(dt),ao=ue(function(t,e,n){Ot(t,e,n)}),uo=ue(function(t,e,n,r){Ot(t,e,n,r)}),co=Ne(function(e,t){var n={};if(null!=e){var r=!1;t=zo(t,function(t){return t=Jt(t,e),r=r||1<t.length,t}),se(e,Re(e),n),r&&(n=v(n,7,je));for(var i=t.length;i--;)Ut(n,t[i])}return n}),lo=Ne(function(t,e){return null==t?{}:At(n=t,e,function(t,e){return Mn(n,e)});var n}),ho=Ee(O),fo=Ee(E),po=fe(function(t,e,n){return e=e.toLowerCase(),t+(n?Fn(e):e)}),go=fe(function(t,e,n){return t+(n?"-":"")+e.toLowerCase()}),mo=fe(function(t,e,n){return t+(n?" ":"")+e.toLowerCase()}),vo=he("toLowerCase"),yo=fe(function(t,e,n){return t+(n?"_":"")+e.toLowerCase()}),bo=fe(function(t,e,n){return t+(n?" ":"")+_o(e)}),wo=fe(function(t,e,n){return t+(n?" ":"")+e.toUpperCase()}),_o=he("toUpperCase"),xo=s(function(t,e){try{return Ho(t,es,e)}catch(t){return kn(t)?t:new Kn(t)}}),Co=Ne(function(e,t){return Bo(t,function(t){t=en(t),Q(e,t,Di(e[t],e))}),e}),ko=me(),So=me(!0),Oo=s(function(e,n){return function(t){return dt(t,e,n)}}),Eo=s(function(e,n){return function(t){return dt(e,t,n)}}),To=we(zo),Ao=we(xs),Io=we(Os),jo=Ce(),Po=Ce(!0),No=be(function(t,e){return t+e},0),Do=Oe("ceil"),Ro=be(function(t,e){return t/e},1),Mo=Oe("floor"),Wo=be(function(t,e){return t*e},1),Lo=Oe("round"),Fo=be(function(t,e){return t-e},0);return d.after=function(t,e){if("function"!=typeof e)throw new j(ns);return t=k(t),function(){if(--t<1)return e.apply(this,arguments)}},d.ary=yn,d.assign=Qi,d.assignIn=Ji,d.assignInWith=Zi,d.assignWith=to,d.at=eo,d.before=bn,d.bind=Di,d.bindAll=Co,d.bindKey=Ri,d.castArray=function(){var t;return arguments.length?M(t=arguments[0])?t:[t]:[]},d.chain=pn,d.chunk=function(t,e,n){e=(n?f(t,e,n):e===es)?1:N(k(e),0);var r=null==t?0:t.length;if(!r||e<1)return[];for(var i=0,o=0,s=A(Sr(r/e));i<r;)s[o++]=c(t,i,i+=e);return s},d.compact=function(t){for(var e=-1,n=null==t?0:t.length,r=0,i=[];++e<n;){var o=t[e];o&&(i[r++]=o)}return i},d.concat=function(){var t=arguments.length;if(!t)return[];for(var e=A(t-1),n=arguments[0],r=t;r--;)e[r-1]=arguments[r];return Uo(M(n)?x(n):[n],u(e,1))},d.cond=function(r){var i=null==r?0:r.length,e=h();return r=i?zo(r,function(t){if("function"!=typeof t[1])throw new j(ns);return[e(t[0]),t[1]]}):[],s(function(t){for(var e=-1;++e<i;){var n=r[e];if(Ho(n[0],this,t))return Ho(n[1],this,t)}})},d.conforms=function(t){return e=v(t,1),n=O(e),function(t){return tt(t,e,n)};var e,n},d.constant=qn,d.countBy=Si,d.create=function(t,e){return t=Xr(t),null==e?t:K(t,e)},d.curry=function t(e,n,r){e=Te(e,8,es,es,es,es,es,n=r?es:n);return e.placeholder=t.placeholder,e},d.curryRight=function t(e,n,r){e=Te(e,16,es,es,es,es,es,n=r?es:n);return e.placeholder=t.placeholder,e},d.debounce=wn,d.defaults=no,d.defaultsDeep=ro,d.defer=Cr,d.delay=Mi,d.difference=Yn,d.differenceBy=Mr,d.differenceWith=t,d.drop=function(t,e,n){var r=null==t?0:t.length;return r?c(t,(e=n||e===es?1:k(e))<0?0:e,r):[]},d.dropRight=function(t,e,n){var r=null==t?0:t.length;return r?c(t,0,(e=r-(e=n||e===es?1:k(e)))<0?0:e):[]},d.dropRightWhile=function(t,e){return t&&t.length?Vt(t,h(e,3),!0,!0):[]},d.dropWhile=function(t,e){return t&&t.length?Vt(t,h(e,3),!0):[]},d.fill=function(t,e,n,r){var i=null==t?0:t.length;if(i){n&&"number"!=typeof n&&f(t,e,n)&&(n=0,r=i);var o=t,s=e,a=n,u=r,i=o.length;for((a=k(a))<0&&(a=i<-a?0:i+a),(u=u===es||i<u?i:k(u))<0&&(u+=i),u=u<a?0:Nn(u);a<u;)o[a++]=s;return o}return[]},d.filter=function(t,e){return(M(t)?qo:ot)(t,h(e,3))},d.flatMap=function(t,e){return u(vn(t,e),1)},d.flatMapDeep=function(t,e){return u(vn(t,e),1/0)},d.flatMapDepth=function(t,e,n){return n=n===es?1:k(n),u(vn(t,e),n)},d.flatten=an,d.flattenDeep=function(t){return(null==t?0:t.length)?u(t,1/0):[]},d.flattenDepth=function(t,e){return(null==t?0:t.length)?u(t,e=e===es?1:k(e)):[]},d.flip=function(t){return Te(t,512)},d.flow=ko,d.flowRight=So,d.fromPairs=function(t){for(var e=-1,n=null==t?0:t.length,r={};++e<n;){var i=t[e];r[i[0]]=i[1]}return r},d.functions=function(t){return null==t?[]:at(t,O(t))},d.functionsIn=function(t){return null==t?[]:at(t,E(t))},d.groupBy=Ti,d.initial=function(t){return(null==t?0:t.length)?c(t,0,-1):[]},d.intersection=ir,d.intersectionBy=kr,d.intersectionWith=e,d.invert=io,d.invertBy=oo,d.invokeMap=Ai,d.iteratee=zn,d.keyBy=Ii,d.keys=O,d.keysIn=E,d.map=vn,d.mapKeys=function(t,r){var i={};return r=h(r,3),a(t,function(t,e,n){Q(i,r(t,e,n),t)}),i},d.mapValues=function(t,r){var i={};return r=h(r,3),a(t,function(t,e,n){Q(i,e,r(t,e,n))}),i},d.matches=function(t){return kt(v(t,1))},d.matchesProperty=function(t,e){return St(t,v(e,1))},d.memoize=_n,d.merge=ao,d.mergeWith=uo,d.method=Oo,d.methodOf=Eo,d.mixin=Un,d.negate=xn,d.nthArg=function(e){return e=k(e),s(function(t){return Et(t,e)})},d.omit=co,d.omitBy=function(t,e){return Wn(t,xn(h(e)))},d.once=function(t){return bn(2,t)},d.orderBy=function(t,e,n,r){return null==t?[]:Tt(t,e=M(e)?e:null==e?[]:[e],n=M(n=r?es:n)?n:null==n?[]:[n])},d.over=To,d.overArgs=ri,d.overEvery=Ao,d.overSome=Io,d.partial=Wi,d.partialRight=Li,d.partition=ji,d.pick=lo,d.pickBy=Wn,d.property=Vn,d.propertyOf=function(e){return function(t){return null==e?es:ut(e,t)}},d.pull=xr,d.pullAll=cn,d.pullAllBy=function(t,e,n){return t&&t.length&&e&&e.length?It(t,e,h(n,2)):t},d.pullAllWith=function(t,e,n){return t&&t.length&&e&&e.length?It(t,e,es,n):t},d.pullAt=di,d.range=jo,d.rangeRight=Po,d.rearg=Fi,d.reject=function(t,e){return(M(t)?qo:ot)(t,xn(h(e,3)))},d.remove=function(t,e){var n=[];if(t&&t.length){var r=-1,i=[],o=t.length;for(e=h(e,3);++r<o;){var s=t[r];e(s,r,t)&&(n.push(s),i.push(r))}jt(t,i)}return n},d.rest=function(t,e){if("function"!=typeof t)throw new j(ns);return s(t,e=e===es?e:k(e))},d.reverse=ln,d.sampleSize=function(t,e,n){return e=(n?f(t,e,n):e===es)?1:k(e),(M(t)?U:Rt)(t,e)},d.set=function(t,e,n){return null==t?t:Mt(t,e,n)},d.setWith=function(t,e,n,r){return r="function"==typeof r?r:es,null==t?t:Mt(t,e,n,r)},d.shuffle=function(t){return(M(t)?G:Wt)(t)},d.slice=function(t,e,n){var r=null==t?0:t.length;return r?(n=n&&"number"!=typeof n&&f(t,e,n)?(e=0,r):(e=null==e?0:k(e),n===es?r:k(n)),c(t,e,n)):[]},d.sortBy=Pi,d.sortedUniq=function(t){return t&&t.length?Bt(t):[]},d.sortedUniqBy=function(t,e){return t&&t.length?Bt(t,h(e,2)):[]},d.split=function(t,e,n){return n&&"number"!=typeof n&&f(t,e,n)&&(e=n=es),(n=n===es?is:n>>>0)?(t=S(t))&&("string"==typeof e||null!=e&&!Vi(e))&&(!(e=l(e))&&Yo(t))?Zt(ts(t),0,n):t.split(e,n):[]},d.spread=function(n,r){if("function"!=typeof n)throw new j(ns);return r=null==r?0:N(k(r),0),s(function(t){var e=t[r],t=Zt(t,0,r);return e&&Uo(t,e),Ho(n,this,t)})},d.tail=function(t){var e=null==t?0:t.length;return e?c(t,1,e):[]},d.take=function(t,e,n){return t&&t.length?c(t,0,(e=n||e===es?1:k(e))<0?0:e):[]},d.takeRight=function(t,e,n){var r=null==t?0:t.length;return r?c(t,(e=r-(e=n||e===es?1:k(e)))<0?0:e,r):[]},d.takeRightWhile=function(t,e){return t&&t.length?Vt(t,h(e,3),!1,!0):[]},d.takeWhile=function(t,e){return t&&t.length?Vt(t,h(e,3)):[]},d.tap=function(t,e){return e(t),t},d.throttle=function(t,e,n){var r=!0,i=!0;if("function"!=typeof t)throw new j(ns);return C(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),wn(t,e,{leading:r,maxWait:e,trailing:i})},d.thru=dn,d.toArray=jn,d.toPairs=ho,d.toPairsIn=fo,d.toPath=function(t){return M(t)?zo(t,en):y(t)?[t]:x(pi(S(t)))},d.toPlainObject=Dn,d.transform=function(t,r,i){var e,n=M(t),o=n||Ui(t)||Xi(t);return r=h(r,4),null==i&&(e=t&&t.constructor,i=o?n?new e:[]:C(t)&&Sn(e)?Xr(dr(t)):{}),(o?Bo:a)(t,function(t,e,n){return r(i,t,e,n)}),i},d.unary=function(t){return yn(t,1)},d.union=gi,d.unionBy=mi,d.unionWith=vi,d.uniq=function(t){return t&&t.length?zt(t):[]},d.uniqBy=function(t,e){return t&&t.length?zt(t,h(e,2)):[]},d.uniqWith=function(t,e){return e="function"==typeof e?e:es,t&&t.length?zt(t,es,e):[]},d.unset=function(t,e){return null==t||Ut(t,e)},d.unzip=hn,d.unzipWith=fn,d.update=function(t,e,n){return null==t?t:Gt(t,e,Qt(n))},d.updateWith=function(t,e,n,r){return r="function"==typeof r?r:es,null==t?t:Gt(t,e,Qt(n),r)},d.values=Ln,d.valuesIn=function(t){return null==t?[]:Ms(t,E(t))},d.without=yi,d.words=Bn,d.wrap=function(t,e){return Wi(Qt(e),t)},d.xor=bi,d.xorBy=wi,d.xorWith=_i,d.zip=xi,d.zipObject=function(t,e){return Yt(t||[],e||[],$)},d.zipObjectDeep=function(t,e){return Yt(t||[],e||[],Mt)},d.zipWith=Ci,d.entries=ho,d.entriesIn=fo,d.extend=Ji,d.extendWith=Zi,Un(d,d),d.add=No,d.attempt=xo,d.camelCase=po,d.capitalize=Fn,d.ceil=Do,d.clamp=function(t,e,n){return n===es&&(n=e,e=es),n!==es&&(n=(n=b(n))==n?n:0),e!==es&&(e=(e=b(e))==e?e:0),Z(b(t),e,n)},d.clone=function(t){return v(t,4)},d.cloneDeep=function(t){return v(t,5)},d.cloneDeepWith=function(t,e){return v(t,5,e="function"==typeof e?e:es)},d.cloneWith=function(t,e){return v(t,4,e="function"==typeof e?e:es)},d.conformsTo=function(t,e){return null==e||tt(t,e,O(e))},d.deburr=Hn,d.defaultTo=function(t,e){return null==t||t!=t?e:t},d.divide=Ro,d.endsWith=function(t,e,n){t=S(t),e=l(e);var r=t.length,r=n=n===es?r:Z(k(n),0,r);return 0<=(n-=e.length)&&t.slice(n,r)==e},d.eq=Cn,d.escape=function(t){return(t=S(t))&&ga.test(t)?t.replace(pa,ou):t},d.escapeRegExp=function(t){return(t=S(t))&&ka.test(t)?t.replace(Ca,"\\$&"):t},d.every=function(t,e,n){return(M(t)?xs:rt)(t,h(e=n&&f(t,e,n)?es:e,3))},d.find=Oi,d.findIndex=on,d.findKey=function(t,e){return Es(t,h(e,3),a)},d.findLast=Ei,d.findLastIndex=sn,d.findLastKey=function(t,e){return Es(t,h(e,3),st)},d.floor=Mo,d.forEach=gn,d.forEachRight=mn,d.forIn=function(t,e){return null==t?t:ti(t,h(e,3),E)},d.forInRight=function(t,e){return null==t?t:ei(t,h(e,3),E)},d.forOwn=function(t,e){return t&&a(t,h(e,3))},d.forOwnRight=function(t,e){return t&&st(t,h(e,3))},d.get=Rn,d.gt=Hi,d.gte=Bi,d.has=function(t,e){return null!=t&&Be(t,e,ht)},d.hasIn=Mn,d.head=un,d.identity=T,d.includes=function(t,e,n,r){return t=p(t)?t:Ln(t),n=n&&!r?k(n):0,r=t.length,n<0&&(n=N(r+n,0)),In(t)?n<=r&&-1<t.indexOf(e,n):!!r&&-1<Vo(t,e,n)},d.indexOf=function(t,e,n){var r=null==t?0:t.length;return r?Vo(t,e,t=(t=null==n?0:k(n))<0?N(r+t,0):t):-1},d.inRange=function(t,e,n){return e=Pn(e),n===es?(n=e,e=0):n=Pn(n),(t=t=b(t))>=D(e=e,n=n)&&t<N(e,n)},d.invoke=so,d.isArguments=qi,d.isArray=M,d.isArrayBuffer=zi,d.isArrayLike=p,d.isArrayLikeObject=_,d.isBoolean=function(t){return!0===t||!1===t||o(t)&&n(t)==ss},d.isBuffer=Ui,d.isDate=nr,d.isElement=function(t){return o(t)&&1===t.nodeType&&!An(t)},d.isEmpty=function(t){if(null!=t){if(p(t)&&(M(t)||"string"==typeof t||"function"==typeof t.splice||Ui(t)||Xi(t)||qi(t)))return!t.length;var e,n=R(t);if(n==us||n==fs)return!t.size;if($e(t))return!wt(t).length;for(e in t)if(P.call(t,e))return!1}return!0},d.isEqual=function(t,e){return mt(t,e)},d.isEqualWith=function(t,e,n){var r=(n="function"==typeof n?n:es)?n(t,e):es;return r===es?mt(t,e,es,n):!!r},d.isError=kn,d.isFinite=function(t){return"number"==typeof t&&Tr(t)},d.isFunction=Sn,d.isInteger=On,d.isLength=En,d.isMap=Gi,d.isMatch=function(t,e){return t===e||vt(t,e,Fe(e))},d.isMatchWith=function(t,e,n){return n="function"==typeof n?n:es,vt(t,e,Fe(e),n)},d.isNaN=function(t){return Tn(t)&&t!=+t},d.isNative=function(t){if(ci(t))throw new Kn("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return yt(t)},d.isNil=function(t){return null==t},d.isNull=function(t){return null===t},d.isNumber=Tn,d.isObject=C,d.isObjectLike=o,d.isPlainObject=An,d.isRegExp=Vi,d.isSafeInteger=function(t){return On(t)&&-rs<=t&&t<=rs},d.isSet=$i,d.isString=In,d.isSymbol=y,d.isTypedArray=Xi,d.isUndefined=function(t){return t===es},d.isWeakMap=function(t){return o(t)&&R(t)==Js},d.isWeakSet=function(t){return o(t)&&"[object WeakSet]"==n(t)},d.join=function(t,e){return null==t?"":Ar.call(t,e)},d.kebabCase=go,d.last=r,d.lastIndexOf=function(t,e,n){var r=null==t?0:t.length;if(r){var i=r;if(n!==es&&(i=(i=k(n))<0?N(r+i,0):D(i,r-1)),e==e){var o=t;var s=e;n=i;for(var a=n+1;a--;)if(o[a]===s)return a;return a;return}else return Ts(t,Is,i,!0)}return-1},d.lowerCase=mo,d.lowerFirst=vo,d.lt=Yi,d.lte=Ki,d.max=function(t){return t&&t.length?it(t,T,lt):es},d.maxBy=function(t,e){return t&&t.length?it(t,h(e,2),lt):es},d.mean=function(t){return js(t,T)},d.meanBy=function(t,e){return js(t,h(e,2))},d.min=function(t){return t&&t.length?it(t,T,xt):es},d.minBy=function(t,e){return t&&t.length?it(t,h(e,2),xt):es},d.stubArray=$n,d.stubFalse=Xn,d.stubObject=function(){return{}},d.stubString=function(){return""},d.stubTrue=function(){return!0},d.multiply=Wo,d.nth=function(t,e){return t&&t.length?Et(t,k(e)):es},d.noConflict=function(){return ms._===this&&(ms._=lr),this},d.noop=Gn,d.now=Ni,d.pad=function(t,e,n){t=S(t);var r=(e=k(e))?Zo(t):0;return!e||e<=r?t:_e(Or(e=(e-r)/2),n)+t+_e(Sr(e),n)},d.padEnd=function(t,e,n){t=S(t);var r=(e=k(e))?Zo(t):0;return e&&r<e?t+_e(e-r,n):t},d.padStart=function(t,e,n){t=S(t);var r=(e=k(e))?Zo(t):0;return e&&r<e?_e(e-r,n)+t:t},d.parseInt=function(t,e,n){return e=n||null==e?0:e&&+e,Pr(S(t).replace(Oa,""),e||0)},d.random=function(t,e,n){var r;return n&&"boolean"!=typeof n&&f(t,e,n)&&(e=n=es),n===es&&("boolean"==typeof e?(n=e,e=es):"boolean"==typeof t&&(n=t,t=es)),t===es&&e===es?(t=0,e=1):(t=Pn(t),e===es?(e=t,t=0):e=Pn(e)),e<t&&(r=t,t=e,e=r),n||t%1||e%1?(r=Nr(),D(t+r*(e-t+Ya("1e-"+((r+"").length-1))),e)):Pt(t,e)},d.reduce=function(t,e,n){var r=M(t)?Go:Ns,i=arguments.length<3;return r(t,h(e,4),n,i,Jr)},d.reduceRight=function(t,e,n){var r=M(t)?Ss:Ns,i=arguments.length<3;return r(t,h(e,4),n,i,Zr)},d.repeat=function(t,e,n){return e=(n?f(t,e,n):e===es)?1:k(e),Nt(S(t),e)},d.replace=function(){var t=arguments,e=S(t[0]);return t.length<3?e:e.replace(t[1],t[2])},d.result=function(t,e,n){var r=-1,i=(e=Jt(e,t)).length;for(i||(i=1,t=es);++r<i;){var o=null==t?es:t[en(e[r])];o===es&&(r=i,o=n),t=Sn(o)?o.call(t):o}return t},d.round=Lo,d.runInContext=i,d.sample=function(t){return(M(t)?z:Dt)(t)},d.size=function(t){var e;return null==t?0:p(t)?In(t)?Zo(t):t.length:(e=R(t))==us||e==fs?t.size:wt(t).length},d.snakeCase=yo,d.some=function(t,e,n){return(M(t)?Os:Lt)(t,h(e=n&&f(t,e,n)?es:e,3))},d.sortedIndex=function(t,e){return Ft(t,e)},d.sortedIndexBy=function(t,e,n){return Ht(t,e,h(n,2))},d.sortedIndexOf=function(t,e){var n=null==t?0:t.length;if(n){var r=Ft(t,e);if(r<n&&Cn(t[r],e))return r}return-1},d.sortedLastIndex=function(t,e){return Ft(t,e,!0)},d.sortedLastIndexBy=function(t,e,n){return Ht(t,e,h(n,2),!0)},d.sortedLastIndexOf=function(t,e){if(null==t?0:t.length){var n=Ft(t,e,!0)-1;if(Cn(t[n],e))return n}return-1},d.startCase=bo,d.startsWith=function(t,e,n){return t=S(t),n=null==n?0:Z(k(n),0,t.length),e=l(e),t.slice(n,n+e.length)==e},d.subtract=Fo,d.sum=function(t){return t&&t.length?Ds(t,T):0},d.sumBy=function(t,e){return t&&t.length?Ds(t,h(e,2)):0},d.template=function(s,t,e){var n=d.templateSettings;e&&f(s,t,e)&&(t=es),s=S(s),t=Zi({},t,n,Ae);var a,u,r=O(e=Zi({},t.imports,n.imports,Ae)),i=Ms(e,r),c=0,n=t.interpolate||Ba,l="__p += '",e=Zn((t.escape||Ba).source+"|"+n.source+"|"+(n===ya?Na:Ba).source+"|"+(t.evaluate||Ba).source+"|$","g"),o="//# sourceURL="+("sourceURL"in t?t.sourceURL:"lodash.templateSources["+ ++Xa+"]")+"\n";if(s.replace(e,function(t,e,n,r,i,o){return n=n||r,l+=s.slice(c,o).replace(qa,Fs),e&&(a=!0,l+="' +\n__e("+e+") +\n'"),i&&(u=!0,l+="';\n"+i+";\n__p += '"),n&&(l+="' +\n((__t = ("+n+")) == null ? '' : __t) +\n'"),c=o+t.length,t}),l+="';\n",(n=t.variable)||(l="with (obj) {\n"+l+"\n}\n"),l=(u?l.replace(ca,""):l).replace(la,"$1").replace(ha,"$1;"),l="function("+(n||"obj")+") {\n"+(n?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(a?", __e = _.escape":"")+(u?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+l+"return __p\n}",(e=xo(function(){return Qn(r,o+"return "+l).apply(es,i)})).source=l,kn(e))throw e;return e},d.times=function(t,e){if((t=k(t))<1||rs<t)return[];var n=is,r=D(t,is);for(e=h(e),t-=is,r=Rs(r,e);++n<t;)e(n);return r},d.toFinite=Pn,d.toInteger=k,d.toLength=Nn,d.toLower=function(t){return S(t).toLowerCase()},d.toNumber=b,d.toSafeInteger=function(t){return t?Z(k(t),-rs,rs):0===t?t:0},d.toString=S,d.toUpper=function(t){return S(t).toUpperCase()},d.trim=function(t,e,n){return(t=S(t))&&(n||e===es)?t.replace(Sa,""):t&&(e=l(e))?Zt(n=ts(t),Ws(n,e=ts(e)),Ls(n,e)+1).join(""):t},d.trimEnd=function(t,e,n){return(t=S(t))&&(n||e===es)?t.replace(Ea,""):t&&(e=l(e))?Zt(n=ts(t),0,Ls(n,ts(e))+1).join(""):t},d.trimStart=function(t,e,n){return(t=S(t))&&(n||e===es)?t.replace(Oa,""):t&&(e=l(e))?Zt(n=ts(t),Ws(n,ts(e))).join(""):t},d.truncate=function(t,e){var n,r=30,i="...",e=(C(e)&&(n="separator"in e?e.separator:n,r="length"in e?k(e.length):r,i="omission"in e?l(e.omission):i),(t=S(t)).length);if((e=Yo(t)?(o=ts(t)).length:e)<=r)return t;if((e=r-Zo(i))<1)return i;var o,r=o?Zt(o,0,e).join(""):t.slice(0,e);if(n!==es)if(o&&(e+=r.length-e),Vi(n)){if(t.slice(e).search(n)){var s,a=r;for((n=n.global?n:Zn(n.source,S(Da.exec(n))+"g")).lastIndex=0;s=n.exec(a);)var u=s.index;r=r.slice(0,u===es?e:u)}}else t.indexOf(l(n),e)==e||-1<(o=r.lastIndexOf(n))&&(r=r.slice(0,o));return r+i},d.unescape=function(t){return(t=S(t))&&da.test(t)?t.replace(fa,su):t},d.uniqueId=function(t){var e=++sr;return S(t)+e},d.upperCase=wo,d.upperFirst=_o,d.each=gn,d.eachRight=mn,d.first=un,Un(d,(Qr={},a(d,function(t,e){P.call(d.prototype,e)||(Qr[e]=t)}),Qr),{chain:!1}),d.VERSION="4.17.4",Bo(["bind","bindKey","curry","curryRight","partial","partialRight"],function(t){d[t].placeholder=d}),Bo(["drop","take"],function(n,r){m.prototype[n]=function(t){t=t===es?1:N(k(t),0);var e=this.__filtered__&&!r?new m(this):this.clone();return e.__filtered__?e.__takeCount__=D(t,e.__takeCount__):e.__views__.push({size:D(t,is),type:n+(e.__dir__<0?"Right":"")}),e},m.prototype[n+"Right"]=function(t){return this.reverse()[n](t).reverse()}}),Bo(["filter","map","takeWhile"],function(t,e){var n=e+1,r=1==n||3==n;m.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:h(t,3),type:n}),e.__filtered__=e.__filtered__||r,e}}),Bo(["head","last"],function(t,e){var n="take"+(e?"Right":"");m.prototype[t]=function(){return this[n](1).value()[0]}}),Bo(["initial","tail"],function(t,e){var n="drop"+(e?"":"Right");m.prototype[t]=function(){return this.__filtered__?new m(this):this[n](1)}}),m.prototype.compact=function(){return this.filter(T)},m.prototype.find=function(t){return this.filter(t).head()},m.prototype.findLast=function(t){return this.reverse().find(t)},m.prototype.invokeMap=s(function(e,n){return"function"==typeof e?new m(this):this.map(function(t){return dt(t,e,n)})}),m.prototype.reject=function(t){return this.filter(xn(h(t)))},m.prototype.slice=function(t,e){t=k(t);var n=this;return n.__filtered__&&(0<t||e<0)?new m(n):(t<0?n=n.takeRight(-t):t&&(n=n.drop(t)),n=e!==es?(e=k(e))<0?n.dropRight(-e):n.take(e-t):n)},m.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},m.prototype.toArray=function(){return this.take(is)},a(m.prototype,function(c,t){var l=/^(?:filter|find|map|reject)|While$/.test(t),h=/^(?:head|last)$/.test(t),f=d[h?"take"+("last"==t?"Right":""):t],p=h||/^find/.test(t);f&&(d.prototype[t]=function(){function t(t){return t=f.apply(d,Uo([t],r)),h&&a?t[0]:t}var e,n=this.__wrapped__,r=h?[1]:arguments,i=n instanceof m,o=r[0],s=i||M(n),a=(s&&l&&"function"==typeof o&&1!=o.length&&(i=s=!1),this.__chain__),o=!!this.__actions__.length,u=p&&!a,i=i&&!o;return!p&&s?(n=i?n:new m(this),(e=c.apply(n,r)).__actions__.push({func:dn,args:[t],thisArg:es}),new g(e,a)):u&&i?c.apply(this,r):(e=this.thru(t),u?h?e.value()[0]:e.value():e)})}),Bo(["pop","push","shift","sort","splice","unshift"],function(t){var n=er[t],r=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",i=/^(?:pop|shift)$/.test(t);d.prototype[t]=function(){var t,e=arguments;return i&&!this.__chain__?(t=this.value(),n.apply(M(t)?t:[],e)):this[r](function(t){return n.apply(M(t)?t:[],e)})}}),a(m.prototype,function(t,e){var n,r=d[e];r&&(n=r.name+"",(Hr[n]||(Hr[n]=[])).push({name:e,func:r}))}),Hr[ve(es,2).name]=[{name:"wrapper",func:es}],m.prototype.clone=function(){var t=new m(this.__wrapped__);return t.__actions__=x(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=x(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=x(this.__views__),t},m.prototype.reverse=function(){var t;return this.__filtered__?((t=new m(this)).__dir__=-1,t.__filtered__=!0):(t=this.clone()).__dir__*=-1,t},m.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,n=M(t),r=e<0,i=n?t.length:0,o=function(t,e,n){for(var r=-1,i=n.length;++r<i;){var o=n[r],s=o.size;switch(o.type){case"drop":t+=s;break;case"dropRight":e-=s;break;case"take":e=D(e,t+s);break;case"takeRight":t=N(t,e-s)}}return{start:t,end:e}}(0,i,this.__views__),s=o.start,a=(o=o.end)-s,u=r?o:s-1,c=this.__iteratees__,l=c.length,h=0,f=D(a,this.__takeCount__);if(!n||!r&&i==a&&f==a)return $t(t,this.__actions__);var p=[];t:for(;a--&&h<f;){for(var d=-1,g=t[u+=e];++d<l;){var m=c[d],v=m.iteratee,m=m.type,v=v(g);if(2==m)g=v;else if(!v){if(1==m)continue t;break t}}p[h++]=g}return p},d.prototype.at=ki,d.prototype.chain=function(){return pn(this)},d.prototype.commit=function(){return new g(this.value(),this.__chain__)},d.prototype.next=function(){this.__values__===es&&(this.__values__=jn(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?es:this.__values__[this.__index__++]}},d.prototype.plant=function(t){for(var e,n=this;n instanceof W;)var r=rn(n),i=(r.__index__=0,r.__values__=es,e?i.__wrapped__=r:e=r,r),n=n.__wrapped__;return i.__wrapped__=t,e},d.prototype.reverse=function(){var t=this.__wrapped__;return t instanceof m?(t=t,(t=(t=this.__actions__.length?new m(this):t).reverse()).__actions__.push({func:dn,args:[ln],thisArg:es}),new g(t,this.__chain__)):this.thru(ln)},d.prototype.toJSON=d.prototype.valueOf=d.prototype.value=function(){return $t(this.__wrapped__,this.__actions__)},d.prototype.first=d.prototype.head,br&&(d.prototype[br]=function(){return this}),d}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(ms._=vs,define(function(){return vs})):l?((l.exports=vs)._=vs,r._=vs):ms._=vs}.call(this),!function(t,e){"object"==typeof exports?module.exports=e():"function"==typeof define&&define.amd?define(e):t.jStat=e()}(this,function(){var c,l,h,o,n,f=function(a,u){var e=Array.prototype.concat,t=Array.prototype.slice,n=Object.prototype.toString;function c(t,e){t=e<t?t:e;return a.pow(10,17-~~(a.log(0<t?t:-t)*a.LOG10E))}var l=Array.isArray||function(t){return"[object Array]"===n.call(t)};function h(t){return"[object Function]"===n.call(t)}function f(t){return"number"==typeof t&&t-t==0}function p(){return new p._init(arguments)}function r(){return 0}function i(){return 1}function o(t,e){return t===e?1:0}function s(t,e,n,r){var i,o=[],s=t.length;if(e===u&&n===u&&r===u)return p.copy(t);if(r=r||1,(e=0<=(e=e||0)?e:s+e)===(n=0<=(n=n||t.length)?n:s+n)||0===r)return[];if(e<n&&r<0)return[];if(n<e&&0<r)return[];if(0<r)for(i=e;i<n;i+=r)o.push(t[i]);else for(i=e;n<i;i+=r)o.push(t[i]);return o}p.fn=p.prototype,(p._init=function(t){if(l(t[0]))if(l(t[0][0])){h(t[1])&&(t[0]=p.map(t[0],t[1]));for(var e=0;e<t[0].length;e++)this[e]=t[0][e];this.length=t[0].length}else this[0]=h(t[1])?p.map(t[0],t[1]):t[0],this.length=1;else{if(f(t[0]))this[0]=p.seq.apply(null,t);else{if(t[0]instanceof p)return p(t[0].toArray());this[0]=[]}this.length=1}return this}).prototype=p.prototype,(p._init.constructor=p).utils={calcRdx:c,isArray:l,isFunction:h,isNumber:f,toVector:function(t){return e.apply([],t)}},p._random_fn=a.random,p.setRandom=function(t){if("function"!=typeof t)throw new TypeError("fn is not a function");p._random_fn=t},p.extend=function(t){var e,n;if(1===arguments.length){for(n in t)p[n]=t[n];return this}for(e=1;e<arguments.length;e++)for(n in arguments[e])t[n]=arguments[e][n];return t},p.rows=function(t){return t.length||1},p.cols=function(t){return t[0].length||1},p.dimensions=function(t){return{rows:p.rows(t),cols:p.cols(t)}},p.row=function(e,t){return l(t)?t.map(function(t){return p.row(e,t)}):e[t]},p.rowa=function(t,e){return p.row(t,e)},p.col=function(r,t){var i;if(l(t))return i=p.arange(r.length).map(function(){return new Array(t.length)}),t.forEach(function(e,n){p.arange(r.length).forEach(function(t){i[t][n]=r[t][e]})}),i;for(var e=new Array(r.length),n=0;n<r.length;n++)e[n]=[r[n][t]];return e},p.cola=function(t,e){return p.col(t,e).map(function(t){return t[0]})},p.diag=function(t){for(var e=p.rows(t),n=new Array(e),r=0;r<e;r++)n[r]=[t[r][r]];return n},p.antidiag=function(t){for(var e=p.rows(t)-1,n=new Array(e),r=0;0<=e;e--,r++)n[r]=[t[r][e]];return n},p.transpose=function(t){for(var e,n,r=[],i=(t=l(t[0])?t:[t]).length,o=t[0].length,s=0;s<o;s++){for(e=new Array(i),n=0;n<i;n++)e[n]=t[n][s];r.push(e)}return 1===r.length?r[0]:r},p.map=function(t,e,n){for(var r,i=(t=l(t[0])?t:[t]).length,o=t[0].length,s=n?t:new Array(i),a=0;a<i;a++)for(s[a]||(s[a]=new Array(o)),r=0;r<o;r++)s[a][r]=e(t[a][r],a,r);return 1===s.length?s[0]:s},p.cumreduce=function(t,e,n){for(var r,i=(t=l(t[0])?t:[t]).length,o=t[0].length,s=n?t:new Array(i),a=0;a<i;a++)for(s[a]||(s[a]=new Array(o)),0<o&&(s[a][0]=t[a][0]),r=1;r<o;r++)s[a][r]=e(s[a][r-1],t[a][r]);return 1===s.length?s[0]:s},p.alter=function(t,e){return p.map(t,e,!0)},p.create=function(t,e,n){var r,i,o=new Array(t);for(h(e)&&(n=e,e=t),r=0;r<t;r++)for(o[r]=new Array(e),i=0;i<e;i++)o[r][i]=n(r,i);return o},p.zeros=function(t,e){return f(e)||(e=t),p.create(t,e,r)},p.ones=function(t,e){return f(e)||(e=t),p.create(t,e,i)},p.rand=function(t,e){return f(e)||(e=t),p.create(t,e,p._random_fn)},p.identity=function(t,e){return f(e)||(e=t),p.create(t,e,o)},p.symmetric=function(t){var e,n,r=t.length;if(t.length!==t[0].length)return!1;for(e=0;e<r;e++)for(n=0;n<r;n++)if(t[n][e]!==t[e][n])return!1;return!0},p.clear=function(t){return p.alter(t,r)},p.seq=function(t,e,n,r){h(r)||(r=!1);for(var i=[],o=c(t,e),s=(e*o-t*o)/((n-1)*o),a=t,u=0;a<=e&&u<n;a=(t*o+s*o*++u)/o)i.push(r?r(a,u):a);return i},p.arange=function(t,e,n){var r,i=[];if(n=n||1,e===u&&(e=t,t=0),t===e||0===n)return[];if(t<e&&n<0)return[];if(e<t&&0<n)return[];if(0<n)for(r=t;r<e;r+=n)i.push(r);else for(r=t;e<r;r+=n)i.push(r);return i},p.slice=function(t,e){var n,r;return f((e=e||{}).row)?f(e.col)?t[e.row][e.col]:s(p.rowa(t,e.row),(n=e.col||{}).start,n.end,n.step):f(e.col)?s(p.cola(t,e.col),(r=e.row||{}).start,r.end,r.step):(r=e.row||{},n=e.col||{},s(t,r.start,r.end,r.step).map(function(t){return s(t,n.start,n.end,n.step)}))},p.sliceAssign=function(i,t,o){var e,n;if(f(t.row)){if(f(t.col))return i[t.row][t.col]=o;t.col=t.col||{},t.col.start=t.col.start||0,t.col.end=t.col.end||i[0].length,t.col.step=t.col.step||1;var s=p.arange(t.col.start,a.min(i.length,t.col.end),t.col.step),r=t.row;s.forEach(function(t,e){i[r][t]=o[e]})}else f(t.col)?(t.row=t.row||{},t.row.start=t.row.start||0,t.row.end=t.row.end||i.length,t.row.step=t.row.step||1,e=p.arange(t.row.start,a.min(i[0].length,t.row.end),t.row.step),n=t.col,e.forEach(function(t,e){i[t][n]=o[e]})):(o[0].length===u&&(o=[o]),t.row.start=t.row.start||0,t.row.end=t.row.end||i.length,t.row.step=t.row.step||1,t.col.start=t.col.start||0,t.col.end=t.col.end||i[0].length,t.col.step=t.col.step||1,e=p.arange(t.row.start,a.min(i.length,t.row.end),t.row.step),s=p.arange(t.col.start,a.min(i[0].length,t.col.end),t.col.step),e.forEach(function(n,r){s.forEach(function(t,e){i[n][t]=o[r][e]})}));return i},p.diagonal=function(t){var n=p.zeros(t.length,t.length);return t.forEach(function(t,e){n[e][e]=t}),n},p.copy=function(t){return t.map(function(t){return f(t)?t:t.map(function(t){return t})})};for(var d=p.prototype,g=(d.length=0,d.push=Array.prototype.push,d.sort=Array.prototype.sort,d.splice=Array.prototype.splice,d.slice=Array.prototype.slice,d.toArray=function(){return 1<this.length?t.call(this):t.call(this)[0]},d.map=function(t,e){return p(p.map(this,t,e))},d.cumreduce=function(t,e){return p(p.cumreduce(this,t,e))},d.alter=function(t){return p.alter(this,t),this},"transpose clear symmetric rows cols dimensions diag antidiag".split(" ")),m=0;m<g.length;m++)!function(r){d[r]=function(t){var e,n=this;return t?(setTimeout(function(){t.call(n,d[r].call(n))}),this):(e=p[r](this),l(e)?p(e):e)}}(g[m]);for(var v="row col".split(" "),y=0;y<v.length;y++)!function(r){d[r]=function(t,e){var n=this;return e?(setTimeout(function(){e.call(n,d[r].call(n,t))}),this):p(p[r](this,t))}}(v[y]);for(var b="create zeros ones rand identity".split(" "),w=0;w<b.length;w++)!function(t){d[t]=function(){return p(p[t].apply(null,arguments))}}(b[w]);return p}(Math),u=f,p=Math,d=u.utils.isFunction;function g(t,e){return t-e}function m(t,e,n){return p.max(e,p.min(t,n))}u.sum=function(t){for(var e=0,n=t.length;0<=--n;)e+=t[n];return e},u.sumsqrd=function(t){for(var e=0,n=t.length;0<=--n;)e+=t[n]*t[n];return e},u.sumsqerr=function(t){for(var e,n=u.mean(t),r=0,i=t.length;0<=--i;)r+=(e=t[i]-n)*e;return r},u.sumrow=function(t){for(var e=0,n=t.length;0<=--n;)e+=t[n];return e},u.product=function(t){for(var e=1,n=t.length;0<=--n;)e*=t[n];return e},u.min=function(t){for(var e=t[0],n=0;++n<t.length;)t[n]<e&&(e=t[n]);return e},u.max=function(t){for(var e=t[0],n=0;++n<t.length;)t[n]>e&&(e=t[n]);return e},u.unique=function(t){for(var e={},n=[],r=0;r<t.length;r++)e[t[r]]||(e[t[r]]=!0,n.push(t[r]));return n},u.mean=function(t){return u.sum(t)/t.length},u.meansqerr=function(t){return u.sumsqerr(t)/t.length},u.geomean=function(t){return p.pow(u.product(t),1/t.length)},u.median=function(t){var e=t.length,t=t.slice().sort(g);return 1&e?t[e/2|0]:(t[e/2-1]+t[e/2])/2},u.cumsum=function(t){return u.cumreduce(t,function(t,e){return t+e})},u.cumprod=function(t){return u.cumreduce(t,function(t,e){return t*e})},u.diff=function(t){for(var e=[],n=t.length,r=1;r<n;r++)e.push(t[r]-t[r-1]);return e},u.rank=function(t){for(var e=[],n={},r=0;r<t.length;r++)n[a=t[r]]?n[a]++:(n[a]=1,e.push(a));var i=e.sort(g),o={},s=1;for(r=0;r<i.length;r++){var a,u=n[a=i[r]],c=s;o[a]=(c+(s+u-1))/2,s+=u}return t.map(function(t){return o[t]})},u.mode=function(t){for(var e=t.length,n=t.slice().sort(g),r=1,i=0,o=0,s=[],a=0;a<e;a++)n[a]===n[a+1]?r++:(i<r?(s=[n[a]],i=r,o=0):r===i&&(s.push(n[a]),o++),r=1);return 0===o?s[0]:s},u.range=function(t){return u.max(t)-u.min(t)},u.variance=function(t,e){return u.sumsqerr(t)/(t.length-(e?1:0))},u.pooledvariance=function(t){return t.reduce(function(t,e){return t+u.sumsqerr(e)},0)/(t.reduce(function(t,e){return t+e.length},0)-t.length)},u.deviation=function(t){for(var e=u.mean(t),n=t.length,r=new Array(n),i=0;i<n;i++)r[i]=t[i]-e;return r},u.stdev=function(t,e){return p.sqrt(u.variance(t,e))},u.pooledstdev=function(t){return p.sqrt(u.pooledvariance(t))},u.meandev=function(t){for(var e=u.mean(t),n=[],r=t.length-1;0<=r;r--)n.push(p.abs(t[r]-e));return u.mean(n)},u.meddev=function(t){for(var e=u.median(t),n=[],r=t.length-1;0<=r;r--)n.push(p.abs(t[r]-e));return u.median(n)},u.coeffvar=function(t){return u.stdev(t)/u.mean(t)},u.quartiles=function(t){var e=t.length,t=t.slice().sort(g);return[t[p.round(e/4)-1],t[p.round(e/2)-1],t[p.round(3*e/4)-1]]},u.quantiles=function(t,e,n,r){var i,o,s,a=t.slice().sort(g),u=[e.length],c=t.length;for(void 0===n&&(n=3/8),void 0===r&&(r=3/8),i=0;i<e.length;i++)s=e[i],o=p.floor(m(s=c*s+(n+s*(1-n-r)),1,c-1)),s=m(s-o,0,1),u[i]=(1-s)*a[o-1]+s*a[o];return u},u.percentile=function(t,e,n){t=t.slice().sort(g),e=e*(t.length+(n?1:-1))+(n?0:1),n=parseInt(e);return n+1<t.length?t[n-1]+(e-n)*(t[n]-t[n-1]):t[n-1]},u.percentileOfScore=function(t,e,n){for(var r,i=0,o=t.length,s="strict"===n?!0:!1,a=0;a<o;a++)r=t[a],(s&&r<e||!s&&r<=e)&&i++;return i/o},u.histogram=function(t,e){e=e||4;for(var n=u.min(t),r=(u.max(t)-n)/e,i=t.length,o=[],s=0;s<e;s++)o[s]=0;for(s=0;s<i;s++)o[p.min(p.floor((t[s]-n)/r),e-1)]+=1;return o},u.covariance=function(t,e){for(var n=u.mean(t),r=u.mean(e),i=t.length,o=new Array(i),s=0;s<i;s++)o[s]=(t[s]-n)*(e[s]-r);return u.sum(o)/(i-1)},u.corrcoeff=function(t,e){return u.covariance(t,e)/u.stdev(t,1)/u.stdev(e,1)},u.spearmancoeff=function(t,e){return t=u.rank(t),e=u.rank(e),u.corrcoeff(t,e)},u.stanMoment=function(t,e){for(var n=u.mean(t),r=u.stdev(t),i=t.length,o=0,s=0;s<i;s++)o+=p.pow((t[s]-n)/r,e);return o/t.length},u.skewness=function(t){return u.stanMoment(t,3)},u.kurtosis=function(t){return u.stanMoment(t,4)-3};for(var v=u.prototype,t="cumsum cumprod".split(" "),e=0;e<t.length;e++)!function(o){v[o]=function(t,e){var n=[],r=0,i=this;if(d(t)&&(e=t,t=!1),e)return setTimeout(function(){e.call(i,v[o].call(i,t))}),this;if(1<this.length){for(i=!0===t?this:this.transpose();r<i.length;r++)n[r]=u[o](i[r]);return n}return u[o](this[0],t)}}(t[e]);for(var r="sum sumsqrd sumsqerr sumrow product min max unique mean meansqerr geomean median diff rank mode range variance deviation stdev meandev meddev coeffvar quartiles histogram skewness kurtosis".split(" "),i=0;i<r.length;i++)!function(o){v[o]=function(t,e){var n=[],r=0,i=this;if(d(t)&&(e=t,t=!1),e)return setTimeout(function(){e.call(i,v[o].call(i,t))}),this;if(1<this.length){for("sumrow"!==o&&(i=!0===t?this:this.transpose());r<i.length;r++)n[r]=u[o](i[r]);return!0===t?u[o](u.utils.toVector(n)):n}return u[o](this[0],t)}}(r[i]);for(var s="quantiles percentileOfScore".split(" "),a=0;a<s.length;a++)!function(a){v[a]=function(){var t,e,n,r=[],i=0,o=this,s=Array.prototype.slice.call(arguments);if(d(s[s.length-1]))return t=s[s.length-1],e=s.slice(0,s.length-1),setTimeout(function(){t.call(o,v[a].apply(o,e))}),this;if(t=void 0,n=function(t){return u[a].apply(o,[t].concat(s))},1<this.length){for(o=o.transpose();i<o.length;i++)r[i]=n(o[i]);return r}return n(this[0])}}(s[a]);var y=f,b=Math;y.gammaln=function(t){var e,n=0,r=[76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18],i=1.000000000190015,o=(e=t=t)+5.5;for(o-=(t+.5)*b.log(o);n<6;n++)i+=r[n]/++e;return b.log(2.5066282746310007*i/t)-o},y.loggam=function(t){var e,n,r,i,o,s=[.08333333333333333,-.002777777777777778,.0007936507936507937,-.0005952380952380952,.0008417508417508418,-.001917526917526918,.00641025641025641,-.02955065359477124,.1796443723688307,-1.3924322169059],a=t,u=0;if(1==t||2==t)return 0;for(e=1/((a=t<=7?t+(u=b.floor(7-t)):a)*a),n=2*b.PI,i=s[9],o=8;0<=o;o--)i=i*e+s[o];if(r=i/a+.5*b.log(n)+(a-.5)*b.log(a)-a,t<=7)for(o=1;o<=u;o++)r-=b.log(a-1),--a;return r},y.gammafn=function(t){var e,n,r,i=[-1.716185138865495,24.76565080557592,-379.80425647094563,629.3311553128184,866.9662027904133,-31451.272968848367,-36144.413418691176,66456.14382024054],o=[-30.8402300119739,315.35062697960416,-1015.1563674902192,-3107.771671572311,22538.11842098015,4755.846277527881,-134659.9598649693,-115132.2596755535],s=!1,a=0,u=0,c=0,l=t;if(171.6243769536076<t)return 1/0;if(l<=0){if(!(r=l%1+36e-17))return 1/0;s=(1&l?-1:1)*b.PI/b.sin(b.PI*r),l=1-l}for(n=(t=l)<1?l++:(l-=a=(0|l)-1)-1,e=0;e<8;++e)c=(c+i[e])*n,u=u*n+o[e];if(r=c/u+1,t<l)r/=t;else if(l<t)for(e=0;e<a;++e)r*=l,l++;return r=s?s/r:r},y.gammap=function(t,e){return y.lowRegGamma(t,e)*y.gammafn(t)},y.lowRegGamma=function(t,e){var n,r=y.gammaln(t),i=t,o=1/t,s=o,a=e+1-t,u=1/1e-30,c=1/a,l=c,h=1,f=-~(8.5*b.log(1<=t?t:1/t)+.4*t+17);if(e<0||t<=0)return NaN;if(e<t+1){for(;h<=f;h++)o+=s*=e/++i;return o*b.exp(-e+t*b.log(e)-r)}for(;h<=f;h++)l*=(c=1/(c=(n=-h*(h-t))*c+(a+=2)))*(u=a+n/u);return 1-l*b.exp(-e+t*b.log(e)-r)},y.factorialln=function(t){return t<0?NaN:y.gammaln(t+1)},y.factorial=function(t){return t<0?NaN:y.gammafn(t+1)},y.combination=function(t,e){return 170<t||170<e?b.exp(y.combinationln(t,e)):y.factorial(t)/y.factorial(e)/y.factorial(t-e)},y.combinationln=function(t,e){return y.factorialln(t)-y.factorialln(e)-y.factorialln(t-e)},y.permutation=function(t,e){return y.factorial(t)/y.factorial(t-e)},y.betafn=function(t,e){if(!(t<=0||e<=0))return 170<t+e?b.exp(y.betaln(t,e)):y.gammafn(t)*y.gammafn(e)/y.gammafn(t+e)},y.betaln=function(t,e){return y.gammaln(t)+y.gammaln(e)-y.gammaln(t+e)},y.betacf=function(t,e,n){for(var r,i,o=1e-30,s=1,a=e+n,u=e+1,c=e-1,l=1,h=1-a*t/u,f=h=1/(h=b.abs(h)<o?o:h);s<=100&&(f=(f*=(h=1/(h=b.abs(h=1+(r=s*(n-s)*t/((c+(i=2*s))*(e+i)))*h)<o?o:h))*(l=b.abs(l=1+r/l)<o?o:l))*(i=(h=1/(h=b.abs(h=1+(r=-(e+s)*(a+s)*t/((e+i)*(u+i)))*h)<o?o:h))*(l=b.abs(l=1+r/l)<o?o:l)),!(b.abs(i-1)<3e-7));s++);return f},y.gammapinv=function(t,e){var n,r,i,o,s,a=0,u=e-1,c=y.gammaln(e);if(1<=t)return b.max(100,e+100*b.sqrt(e));if(t<=0)return 0;for(n=1<e?(o=b.log(u),s=b.exp(u*(o-1)-c),n=(2.30753+.27061*(r=b.sqrt(-2*b.log(t<.5?t:1-t))))/(1+r*(.99229+.04481*r))-r,b.max(.001,e*b.pow(1-1/(9*e)-(n=t<.5?-n:n)/(3*b.sqrt(e)),3))):t<(r=1-e*(.253+.12*e))?b.pow(t/r,1/e):1-b.log(1-(t-r)/(1-r));a<12;a++){if(n<=0)return 0;if((n-=r=(i=(y.lowRegGamma(e,n)-t)/(r=1<e?s*b.exp(-(n-u)+u*(b.log(n)-o)):b.exp(-n+u*b.log(n)-c)))/(1-.5*b.min(1,i*((e-1)/n-1))))<=0&&(n=.5*(n+r)),b.abs(r)<1e-8*n)break}return n},y.erf=function(t){var e,n,r,i=[-1.3026537197817094,.6419697923564902,.019476473204185836,-.00956151478680863,-.000946595344482036,.000366839497852761,42523324806907e-18,-20278578112534e-18,-1624290004647e-18,130365583558e-17,1.5626441722e-8,-8.5238095915e-8,6.529054439e-9,5.059343495e-9,-9.91364156e-10,-2.27365122e-10,96467911e-18,2394038e-18,-6886027e-18,894487e-18,313092e-18,-112708e-18,381e-18,7106e-18,-1523e-18,-94e-18,121e-18,-28e-18],o=i.length-1,s=!1,a=0,u=0;for(t<0&&(t=-t,s=!0),e=4*(r=2/(2+t))-2;0<o;o--)a=e*(n=a)-u+i[o],u=n;return r=r*b.exp(-t*t+.5*(i[0]+e*a)-u),s?r-1:1-r},y.erfc=function(t){return 1-y.erf(t)},y.erfcinv=function(t){var e,n,r,i,o=0;if(2<=t)return-100;if(t<=0)return 100;for(e=-.70711*((2.30753+.27061*(r=b.sqrt(-2*b.log((i=t<1?t:2-t)/2))))/(1+r*(.99229+.04481*r))-r);o<2;o++)e+=(n=y.erfc(e)-i)/(1.1283791670955126*b.exp(-e*e)-e*n);return t<1?e:-e},y.ibetainv=function(t,e,n){var r,i,o,s,a,u,c,l=e-1,h=n-1,f=0;if(t<=0)return 0;if(1<=t)return 1;for(s=1<=e&&1<=n?(s=(2.30753+.27061*(i=b.sqrt(-2*b.log(t<.5?t:1-t))))/(1+i*(.99229+.04481*i))-i,u=(s=t<.5?-s:s)*b.sqrt((u=(s*s-3)/6)+(a=2/(1/(2*e-1)+1/(2*n-1))))/a-(1/(2*n-1)-1/(2*e-1))*(u+5/6-2/(3*a)),e/(e+n*b.exp(2*u))):(a=b.log(e/(e+n)),r=b.log(n/(e+n)),t<(i=b.exp(e*a)/e)/(u=i+(o=b.exp(n*r)/n))?b.pow(e*u*t,1/e):1-b.pow(n*u*(1-t),1/n)),c=-y.gammaln(e)-y.gammaln(n)+y.gammaln(e+n);f<10;f++){if(0===s||1===s)return s;if(1<=(s=(s-=i=(o=(y.ibeta(s,e,n)-t)/(i=b.exp(l*b.log(s)+h*b.log(1-s)+c)))/(1-.5*b.min(1,o*(l/s-h/(1-s)))))<=0?.5*(s+i):s)&&(s=.5*(s+i+1)),b.abs(i)<1e-8*s&&0<f)break}return s},y.ibeta=function(t,e,n){var r=0===t||1===t?0:b.exp(y.gammaln(e+n)-y.gammaln(e)-y.gammaln(n)+e*b.log(t)+n*b.log(1-t));return!(t<0||1<t)&&(t<(e+1)/(e+n+2)?r*y.betacf(t,e,n)/e:1-r*y.betacf(1-t,n,e)/n)},y.randn=function(t,e){var n,r,i,o;if(e=e||t,t)return y.create(t,e,function(){return y.randn()});for(;n=y._random_fn(),r=1.7156*(y._random_fn()-.5),.27597<(o=(i=n-.449871)*i+(o=b.abs(r)+.386595)*(.196*o-.25472*i))&&(.27846<o||r*r>-4*b.log(n)*n*n););return r/n},y.randg=function(t,e,n){var r,i,o,s,a,u=t;if(n=n||e,t=t||1,e)return(e=y.zeros(e,n)).alter(function(){return y.randg(t)}),e;t<1&&(t+=1),r=t-1/3,i=1/b.sqrt(9*r);do{for(;(s=1+i*(a=y.randn()))<=0;);}while(s*=s*s,(o=y._random_fn())>1-.331*b.pow(a,4)&&b.log(o)>.5*a*a+r*(1-s+b.log(s)));if(t==u)return r*s;for(;0===(o=y._random_fn()););return b.pow(o,1/u)*r*s};for(var w="gammaln gammafn factorial factorialln".split(" "),_=0;_<w.length;_++)!function(e){y.fn[e]=function(){return y(y.map(this,function(t){return y[e](t)}))}}(w[_]);for(var x="randn".split(" "),C=0;C<x.length;C++)!function(t){y.fn[t]=function(){return y(y[t].apply(null,arguments))}}(x[C]);for(var k=f,S=Math,O="beta centralF cauchy chisquare exponential gamma invgamma kumaraswamy laplace lognormal noncentralt normal pareto studentt weibull uniform binomial negbin hypgeom poisson triangular tukey arcsine".split(" "),E=0;E<O.length;E++)!function(o){k[o]=function(t,e,n){return this instanceof arguments.callee?(this._a=t,this._b=e,this._c=n,this):new arguments.callee(t,e,n)},k.fn[o]=function(t,e,n){t=k[o](t,e,n);return t.data=this,t},k[o].prototype.sample=function(t){var e=this._a,n=this._b,r=this._c;return t?k.alter(t,function(){return k[o].sample(e,n,r)}):k[o].sample(e,n,r)};for(var t="pdf cdf inv".split(" "),e=0;e<t.length;e++)!function(i){k[o].prototype[i]=function(t){var e=this._a,n=this._b,r=this._c;return"number"!=typeof(t=t||0===t?t:this.data)?k.fn.map.call(t,function(t){return k[o][i](t,e,n,r)}):k[o][i](t,e,n,r)}}(t[e]);for(var n="mean median mode variance".split(" "),r=0;r<n.length;r++)!function(t){k[o].prototype[t]=function(){return k[o][t](this._a,this._b,this._c)}}(n[r])}(O[E]);function T(t,e,n,r){for(var i,o=0,s=1,a=1,u=1,c=0,l=0;S.abs((a-l)/a)>r;)s=u+(i=-(e+c)*(e+n+c)*t/(e+2*c)/(e+2*c+1))*s,a=(o=(l=a)+i*o)+(i=(c+=1)*(n-c)*t/(e+2*c-1)/(e+2*c))*a,o/=u=s+i*u,s/=u,a/=u,u=1;return a/e}function A(t,e,n){var r=[.9815606342467192,.9041172563704749,.7699026741943047,.5873179542866175,.3678314989981802,.1252334085114689],i=[.04717533638651183,.10693932599531843,.16007832854334622,.20316742672306592,.2334925365383548,.24914704581340277],o=.5*t;if(8<=o)return 1;for(var s=(s=2*k.normal.cdf(o,0,1,1,0)-1)>=S.exp(-50/n)?S.pow(s,n):0,a=3<t?2:3,u=o,c=(8-o)/a,l=u+c,h=0,f=n-1,p=1;p<=a;p++){for(var d=0,g=.5*(l+u),m=.5*(l-u),v=1;v<=12;v++){var y,b=6<v?r[(y=12-v+1)-1]:-r[(y=v)-1],b=g+m*b,w=b*b;if(60<w)break;b=.5*(2*k.normal.cdf(b,0,1,1,0))-.5*(2*k.normal.cdf(b,t,1,1,0));b>=S.exp(-30/f)&&(d+=i[y-1]*S.exp(-.5*w)*S.pow(b,f))}h+=d*=2*m*n/S.sqrt(2*S.PI),u=l,l+=c}return(s+=h)<=S.exp(-30/e)?0:1<=(s=S.pow(s,e))?1:s}k.extend(k.beta,{pdf:function(t,e,n){return 1<t||t<0?0:1==e&&1==n?1:e<512&&n<512?S.pow(t,e-1)*S.pow(1-t,n-1)/k.betafn(e,n):S.exp((e-1)*S.log(t)+(n-1)*S.log(1-t)-k.betaln(e,n))},cdf:function(t,e,n){return 1<t||t<0?+(1<t):k.ibeta(t,e,n)},inv:function(t,e,n){return k.ibetainv(t,e,n)},mean:function(t,e){return t/(t+e)},median:function(t,e){return k.ibetainv(.5,t,e)},mode:function(t,e){return(t-1)/(t+e-2)},sample:function(t,e){t=k.randg(t);return t/(t+k.randg(e))},variance:function(t,e){return t*e/(S.pow(t+e,2)*(t+e+1))}}),k.extend(k.centralF,{pdf:function(t,e,n){return t<0?0:e<=2?0===t&&e<2?1/0:0===t&&2===e?1:1/k.betafn(e/2,n/2)*S.pow(e/n,e/2)*S.pow(t,e/2-1)*S.pow(1+e/n*t,-(e+n)/2):e*(n/(n+t*e))/2*k.binomial.pdf((e-2)/2,(e+n-2)/2,e*t/(n+t*e))},cdf:function(t,e,n){return t<0?0:k.ibeta(e*t/(e*t+n),e/2,n/2)},inv:function(t,e,n){return n/(e*(1/k.ibetainv(t,e/2,n/2)-1))},mean:function(t,e){return 2<e?e/(e-2):void 0},mode:function(t,e){return 2<t?e*(t-2)/(t*(e+2)):void 0},sample:function(t,e){return 2*k.randg(t/2)/t/(2*k.randg(e/2)/e)},variance:function(t,e){if(!(e<=4))return 2*e*e*(t+e-2)/(t*(e-2)*(e-2)*(e-4))}}),k.extend(k.cauchy,{pdf:function(t,e,n){return n<0?0:n/(S.pow(t-e,2)+S.pow(n,2))/S.PI},cdf:function(t,e,n){return S.atan((t-e)/n)/S.PI+.5},inv:function(t,e,n){return e+n*S.tan(S.PI*(t-.5))},median:function(t){return t},mode:function(t){return t},sample:function(t,e){return k.randn()*S.sqrt(1/(2*k.randg(.5)))*e+t}}),k.extend(k.chisquare,{pdf:function(t,e){return t<0?0:0===t&&2===e?.5:S.exp((e/2-1)*S.log(t)-t/2-e/2*S.log(2)-k.gammaln(e/2))},cdf:function(t,e){return t<0?0:k.lowRegGamma(e/2,t/2)},inv:function(t,e){return 2*k.gammapinv(t,.5*e)},mean:function(t){return t},median:function(t){return t*S.pow(1-2/(9*t),3)},mode:function(t){return 0<t-2?t-2:0},sample:function(t){return 2*k.randg(t/2)},variance:function(t){return 2*t}}),k.extend(k.exponential,{pdf:function(t,e){return t<0?0:e*S.exp(-e*t)},cdf:function(t,e){return t<0?0:1-S.exp(-e*t)},inv:function(t,e){return-S.log(1-t)/e},mean:function(t){return 1/t},median:function(t){return 1/t*S.log(2)},mode:function(){return 0},sample:function(t){return-1/t*S.log(k._random_fn())},variance:function(t){return S.pow(t,-2)}}),k.extend(k.gamma,{pdf:function(t,e,n){return t<0?0:0===t&&1===e?1/n:S.exp((e-1)*S.log(t)-t/n-k.gammaln(e)-e*S.log(n))},cdf:function(t,e,n){return t<0?0:k.lowRegGamma(e,t/n)},inv:function(t,e,n){return k.gammapinv(t,e)*n},mean:function(t,e){return t*e},mode:function(t,e){if(1<t)return(t-1)*e},sample:function(t,e){return k.randg(t)*e},variance:function(t,e){return t*e*e}}),k.extend(k.invgamma,{pdf:function(t,e,n){return t<=0?0:S.exp(-(e+1)*S.log(t)-n/t-k.gammaln(e)+e*S.log(n))},cdf:function(t,e,n){return t<=0?0:1-k.lowRegGamma(e,n/t)},inv:function(t,e,n){return n/k.gammapinv(1-t,e)},mean:function(t,e){return 1<t?e/(t-1):void 0},mode:function(t,e){return e/(t+1)},sample:function(t,e){return e/k.randg(t)},variance:function(t,e){if(!(t<=2))return e*e/((t-1)*(t-1)*(t-2))}}),k.extend(k.kumaraswamy,{pdf:function(t,e,n){return 0===t&&1===e?n:1===t&&1===n?e:S.exp(S.log(e)+S.log(n)+(e-1)*S.log(t)+(n-1)*S.log(1-S.pow(t,e)))},cdf:function(t,e,n){return t<0?0:1<t?1:1-S.pow(1-S.pow(t,e),n)},inv:function(t,e,n){return S.pow(1-S.pow(1-t,1/n),1/e)},mean:function(t,e){return e*k.gammafn(1+1/t)*k.gammafn(e)/k.gammafn(1+1/t+e)},median:function(t,e){return S.pow(1-S.pow(2,-1/e),1/t)},mode:function(t,e){if(1<=t&&1<=e&&1!==t&&1!==e)return S.pow((t-1)/(t*e-1),1/t)},variance:function(){throw new Error("variance not yet implemented")}}),k.extend(k.lognormal,{pdf:function(t,e,n){return t<=0?0:S.exp(-S.log(t)-.5*S.log(2*S.PI)-S.log(n)-S.pow(S.log(t)-e,2)/(2*n*n))},cdf:function(t,e,n){return t<0?0:.5+.5*k.erf((S.log(t)-e)/S.sqrt(2*n*n))},inv:function(t,e,n){return S.exp(-1.4142135623730951*n*k.erfcinv(2*t)+e)},mean:function(t,e){return S.exp(t+e*e/2)},median:function(t){return S.exp(t)},mode:function(t,e){return S.exp(t-e*e)},sample:function(t,e){return S.exp(k.randn()*e+t)},variance:function(t,e){return(S.exp(e*e)-1)*S.exp(2*t+e*e)}}),k.extend(k.noncentralt,{pdf:function(t,e,n){return S.abs(n)<1e-14?k.studentt.pdf(t,e):S.abs(t)<1e-14?S.exp(k.gammaln((e+1)/2)-n*n/2-.5*S.log(S.PI*e)-k.gammaln(e/2)):e/t*(k.noncentralt.cdf(t*S.sqrt(1+2/e),e+2,n)-k.noncentralt.cdf(t,e,n))},cdf:function(t,e,n){if(S.abs(n)<1e-14)return k.studentt.cdf(t,e);for(var r=!1,i=(t<0&&(r=!0,n=-n),k.normal.cdf(-n,0,1)),o=1e-14+1,s=o,a=t*t/(t*t+e),u=0,c=S.exp(-n*n/2),l=S.exp(-n*n/2-.5*S.log(2)-k.gammaln(1.5))*n;u<200||1e-14<s||1e-14<o;)s=o,0<u&&(c*=n*n/(2*u),l*=n*n/(2*(u+.5))),i+=.5*(o=c*k.beta.cdf(a,u+.5,e/2)+l*k.beta.cdf(a,u+1,e/2)),u++;return r?1-i:i}}),k.extend(k.normal,{pdf:function(t,e,n){return S.exp(-.5*S.log(2*S.PI)-S.log(n)-S.pow(t-e,2)/(2*n*n))},cdf:function(t,e,n){return.5*(1+k.erf((t-e)/S.sqrt(2*n*n)))},inv:function(t,e,n){return-1.4142135623730951*n*k.erfcinv(2*t)+e},mean:function(t){return t},median:function(t){return t},mode:function(t){return t},sample:function(t,e){return k.randn()*e+t},variance:function(t,e){return e*e}}),k.extend(k.pareto,{pdf:function(t,e,n){return t<e?0:n*S.pow(e,n)/S.pow(t,n+1)},cdf:function(t,e,n){return t<e?0:1-S.pow(e/t,n)},inv:function(t,e,n){return e/S.pow(1-t,1/n)},mean:function(t,e){if(!(e<=1))return e*S.pow(t,e)/(e-1)},median:function(t,e){return t*(e*S.SQRT2)},mode:function(t){return t},variance:function(t,e){if(!(e<=2))return t*t*e/(S.pow(e-1,2)*(e-2))}}),k.extend(k.studentt,{pdf:function(t,e){return 1/(S.sqrt(e=1e100<e?1e100:e)*k.betafn(.5,e/2))*S.pow(1+t*t/e,-(e+1)/2)},cdf:function(t,e){var n=e/2;return k.ibeta((t+S.sqrt(t*t+e))/(2*S.sqrt(t*t+e)),n,n)},inv:function(t,e){var n=k.ibetainv(2*S.min(t,1-t),.5*e,.5),n=S.sqrt(e*(1-n)/n);return.5<t?n:-n},mean:function(t){return 1<t?0:void 0},median:function(){return 0},mode:function(){return 0},sample:function(t){return k.randn()*S.sqrt(t/(2*k.randg(t/2)))},variance:function(t){return 2<t?t/(t-2):1<t?1/0:void 0}}),k.extend(k.weibull,{pdf:function(t,e,n){return t<0||e<0||n<0?0:n/e*S.pow(t/e,n-1)*S.exp(-S.pow(t/e,n))},cdf:function(t,e,n){return t<0?0:1-S.exp(-S.pow(t/e,n))},inv:function(t,e,n){return e*S.pow(-S.log(1-t),1/n)},mean:function(t,e){return t*k.gammafn(1+1/e)},median:function(t,e){return t*S.pow(S.log(2),1/e)},mode:function(t,e){return e<=1?0:t*S.pow((e-1)/e,1/e)},sample:function(t,e){return t*S.pow(-S.log(k._random_fn()),1/e)},variance:function(t,e){return t*t*k.gammafn(1+2/e)-S.pow(k.weibull.mean(t,e),2)}}),k.extend(k.uniform,{pdf:function(t,e,n){return t<e||n<t?0:1/(n-e)},cdf:function(t,e,n){return t<e?0:t<n?(t-e)/(n-e):1},inv:function(t,e,n){return e+t*(n-e)},mean:function(t,e){return.5*(t+e)},median:function(t,e){return k.mean(t,e)},mode:function(){throw new Error("mode is not yet implemented")},sample:function(t,e){return t/2+e/2+(e/2-t/2)*(2*k._random_fn()-1)},variance:function(t,e){return S.pow(e-t,2)/12}}),k.extend(k.binomial,{pdf:function(t,e,n){return 0===n||1===n?e*n===t?1:0:k.combination(e,t)*S.pow(n,t)*S.pow(1-n,e-t)},cdf:function(t,e,n){var r,i;return t<0?0:e<=t?1:n<0||1<n||e<=0?NaN:(n=n,r=(t=S.floor(t))+1,i=S.exp(k.gammaln(t=r+(e=e-t))-k.gammaln(e)-k.gammaln(r)+r*S.log(n)+e*S.log(1-n)),t=n<(r+1)/(t+2)?i*T(n,r,e,1e-10):1-i*T(1-n,e,r,1e-10),S.round(1e10*(1-t))/1e10)}}),k.extend(k.negbin,{pdf:function(t,e,n){return t===t>>>0&&(t<0?0:k.combination(t+e-1,e-1)*S.pow(1-n,t)*S.pow(n,e))},cdf:function(t,e,n){var r=0,i=0;if(t<0)return 0;for(;i<=t;i++)r+=k.negbin.pdf(i,e,n);return r}}),k.extend(k.hypgeom,{pdf:function(t,e,n,r){if(t!=t|0)return!1;if(t<0||t<n-(e-r))return 0;if(r<t||n<t)return 0;if(e<2*n)return e<2*r?k.hypgeom.pdf(e-n-r+t,e,e-n,e-r):k.hypgeom.pdf(r-t,e,e-n,r);if(e<2*r)return k.hypgeom.pdf(n-t,e,n,e-r);if(n<r)return k.hypgeom.pdf(t,e,r,n);for(var i=1,o=0,s=0;s<t;s++){for(;1<i&&o<r;)i*=1-n/(e-o),o++;i*=(r-s)*(n-s)/((s+1)*(e-n-r+s+1))}for(;o<r;o++)i*=1-n/(e-o);return S.min(1,S.max(0,i))},cdf:function(t,e,n,r){if(t<0||t<n-(e-r))return 0;if(r<=t||n<=t)return 1;if(e<2*n)return e<2*r?k.hypgeom.cdf(e-n-r+t,e,e-n,e-r):1-k.hypgeom.cdf(r-t-1,e,e-n,r);if(e<2*r)return 1-k.hypgeom.cdf(n-t-1,e,n,e-r);if(n<r)return k.hypgeom.cdf(t,e,r,n);for(var i=1,o=1,s=0,a=0;a<t;a++){for(;1<i&&s<r;){var u=1-n/(e-s);o*=u,i*=u,s++}i+=o*=(r-a)*(n-a)/((a+1)*(e-n-r+a+1))}for(;s<r;s++)i*=1-n/(e-s);return S.min(1,S.max(0,i))}}),k.extend(k.poisson,{pdf:function(t,e){return e<0||t%1!=0||t<0?0:S.pow(e,t)*S.exp(-e)/k.factorial(t)},cdf:function(t,e){var n=[],r=0;if(t<0)return 0;for(;r<=t;r++)n.push(k.poisson.pdf(r,e));return k.sum(n)},mean:function(t){return t},variance:function(t){return t},sampleSmall:function(t){for(var e=1,n=0,r=S.exp(-t);n++,r<(e*=k._random_fn()););return n-1},sampleLarge:function(t){for(var e,n,r,i=t,t=S.sqrt(i),o=S.log(i),s=.931+2.53*t,a=.02483*s-.059,u=1.1239+1.1328/(s-3.4),c=.9277-3.6224/(s-2);;){if(e=S.random()-.5,n=S.random(),r=.5-S.abs(e),e=S.floor((2*a/r+s)*e+i+.43),.07<=r&&n<=c)return e;if(!(e<0||r<.013&&r<n)&&S.log(n)+S.log(u)-S.log(a/(r*r)+s)<=e*o-i-k.loggam(e+1))return e}},sample:function(t){return t<10?this.sampleSmall(t):this.sampleLarge(t)}}),k.extend(k.triangular,{pdf:function(t,e,n,r){return n<=e||r<e||n<r?NaN:t<e||n<t?0:t<r?2*(t-e)/((n-e)*(r-e)):t===r?2/(n-e):2*(n-t)/((n-e)*(n-r))},cdf:function(t,e,n,r){return n<=e||r<e||n<r?NaN:t<=e?0:n<=t?1:t<=r?S.pow(t-e,2)/((n-e)*(r-e)):1-S.pow(n-t,2)/((n-e)*(n-r))},inv:function(t,e,n,r){return n<=e||r<e||n<r?NaN:t<=(r-e)/(n-e)?e+(n-e)*S.sqrt(t*((r-e)/(n-e))):e+(n-e)*(1-S.sqrt((1-t)*(1-(r-e)/(n-e))))},mean:function(t,e,n){return(t+e+n)/3},median:function(t,e,n){return n<=(t+e)/2?e-S.sqrt((e-t)*(e-n))/S.sqrt(2):(t+e)/2<n?t+S.sqrt((e-t)*(n-t))/S.sqrt(2):void 0},mode:function(t,e,n){return n},sample:function(t,e,n){var r=k._random_fn();return r<(n-t)/(e-t)?t+S.sqrt(r*(e-t)*(n-t)):e-S.sqrt((1-r)*(e-t)*(e-n))},variance:function(t,e,n){return(t*t+e*e+n*n-t*e-t*n-e*n)/18}}),k.extend(k.arcsine,{pdf:function(t,e,n){return n<=e?NaN:t<=e||n<=t?0:2/S.PI*S.pow(S.pow(n-e,2)-S.pow(2*t-e-n,2),-.5)},cdf:function(t,e,n){return t<e?0:t<n?2/S.PI*S.asin(S.sqrt((t-e)/(n-e))):1},inv:function(t,e,n){return e+(.5-.5*S.cos(S.PI*t))*(n-e)},mean:function(t,e){return e<=t?NaN:(t+e)/2},median:function(t,e){return e<=t?NaN:(t+e)/2},mode:function(){throw new Error("mode is not yet implemented")},sample:function(t,e){return(t+e)/2+(e-t)/2*S.sin(2*S.PI*k.uniform.sample(0,1))},variance:function(t,e){return e<=t?NaN:S.pow(e-t,2)/8}}),k.extend(k.laplace,{pdf:function(t,e,n){return n<=0?0:S.exp(-S.abs(t-e)/n)/(2*n)},cdf:function(t,e,n){return n<=0?0:t<e?.5*S.exp((t-e)/n):1-.5*S.exp(-(t-e)/n)},mean:function(t){return t},median:function(t){return t},mode:function(t){return t},variance:function(t,e){return 2*e*e},sample:function(t,e){var n=k._random_fn()-.5;return t-e*(n/S.abs(n))*S.log(1-2*S.abs(n))}}),k.extend(k.tukey,{cdf:function(t,e,n){var r=e,i=[.9894009349916499,.9445750230732326,.8656312023878318,.755404408355003,.6178762444026438,.45801677765722737,.2816035507792589,.09501250983763744],o=[.027152459411754096,.062253523938647894,.09515851168249279,.12462897125553388,.14959598881657674,.16915651939500254,.18260341504492358,.1894506104550685];if(t<=0)return 0;if(n<2||r<2)return NaN;if(!Number.isFinite(t))return 1;if(25e3<n)return A(t,1,r);var e=.5*n,s=e*S.log(n)-n*S.log(2)-k.gammaln(e),a=e-1,u=.25*n,c=n<=100?1:n<=800?.5:n<=5e3?.25:.125;s+=S.log(c);for(var l=0,h=1;h<=50;h++){for(var f=0,p=(2*h-1)*c,d=1;d<=16;d++){var g,m=8<d?s+a*S.log(p+i[g=d-8-1]*c)-(i[g]*c+p)*u:s+a*S.log(p-i[g=d-1]*c)+(i[g]*c-p)*u;-30<=m&&(f+=A(8<d?t*S.sqrt(.5*(i[g]*c+p)):t*S.sqrt(.5*(-i[g]*c+p)),1,r)*o[g]*S.exp(m))}if(1<=h*c&&f<=1e-14)break;l+=f}if(1e-14<f)throw new Error("tukey.cdf failed to converge");return l=1<l?1:l},inv:function(t,e,n){if(n<2||e<2)return NaN;if(t<0||1<t)return NaN;if(0===t)return 0;if(1===t)return 1/0;i=e,o=n,r=.5-.5*(r=t),r=(r=S.sqrt(S.log(1/(r*r))))+((((-453642210148e-16*r-.204231210125)*r-.342242088547)*r-1)*r+.322232421088)/((((.0038560700634*r+.10353775285)*r+.531103462366)*r+.588581570495)*r+.099348462606),o<120&&(r+=(r*r*r+r)/o/4),s=.8832-.2368*r,o<120&&(s+=-1.214/o+1.208*r/o);for(var r,i,o,s,a,u=r*(s*S.log(i-1)+1.4142),c=k.tukey.cdf(u,e,n)-t,l=(f=0<c?S.max(0,u-1):u+1,k.tukey.cdf(f,e,n)-t),h=1;h<50;h++){a=f-l*(f-u)/(l-c),c=l,u=f,a<0&&(a=0,l=-t);var l=k.tukey.cdf(a,e,n)-t,f=a;if(S.abs(f-u)<1e-4)return a}throw new Error("tukey.inv failed to converge")}});var I,j,P=f,N=Math,M=Array.prototype.push,W=P.utils.isArray;function D(t){return W(t)||t instanceof P}P.extend({add:function(t,r){return D(r)?(D(r[0])||(r=[r]),P.map(t,function(t,e,n){return t+r[e][n]})):P.map(t,function(t){return t+r})},subtract:function(t,r){return D(r)?(D(r[0])||(r=[r]),P.map(t,function(t,e,n){return t-r[e][n]||0})):P.map(t,function(t){return t-r})},divide:function(t,e){return D(e)?(D(e[0])||(e=[e]),P.multiply(t,P.inv(e))):P.map(t,function(t){return t/e})},multiply:function(t,e){var n,r,i,o,s,a,u,c;if(void 0===t.length&&void 0===e.length)return t*e;if(s=t.length,a=t[0].length,u=P.zeros(s,i=D(e)?e[0].length:a),c=0,D(e)){for(;c<i;c++)for(n=0;n<s;n++){for(r=o=0;r<a;r++)o+=t[n][r]*e[r][c];u[n][c]=o}return 1===s&&1===c?u[0][0]:u}return P.map(t,function(t){return t*e})},outer:function(t,e){return P.multiply(t.map(function(t){return[t]}),[e])},dot:function(t,e){D(t[0])||(t=[t]),D(e[0])||(e=[e]);for(var n,r,i=1===t[0].length&&1!==t.length?P.transpose(t):t,o=1===e[0].length&&1!==e.length?P.transpose(e):e,s=[],a=0,u=i.length,c=i[0].length;a<u;a++){for(s[a]=[],r=n=0;r<c;r++)n+=i[a][r]*o[a][r];s[a]=n}return 1===s.length?s[0]:s},pow:function(t,e){return P.map(t,function(t){return N.pow(t,e)})},exp:function(t){return P.map(t,function(t){return N.exp(t)})},log:function(t){return P.map(t,function(t){return N.log(t)})},abs:function(t){return P.map(t,function(t){return N.abs(t)})},norm:function(t,e){var n=0,r=0;for(isNaN(e)&&(e=2),D(t[0])&&(t=t[0]);r<t.length;r++)n+=N.pow(N.abs(t[r]),e);return N.pow(n,1/e)},angle:function(t,e){return N.acos(P.dot(t,e)/(P.norm(t)*P.norm(e)))},aug:function(t,e){for(var n=[],r=0;r<t.length;r++)n.push(t[r].slice());for(r=0;r<n.length;r++)M.apply(n[r],e[r]);return n},inv:function(t){for(var e,n=t.length,r=t[0].length,i=P.identity(n,r),o=P.gauss_jordan(t,i),s=[],a=0;a<n;a++)for(s[a]=[],e=r;e<o[0].length;e++)s[a][e-r]=o[a][e];return s},det:function(t){var e,n=t.length,r=2*n,i=new Array(r),o=n-1,s=r-1,a=o-n+1,u=s,c=0,l=0;if(2===n)return t[0][0]*t[1][1]-t[0][1]*t[1][0];for(;c<r;c++)i[c]=1;for(c=0;c<n;c++){for(e=0;e<n;e++)i[a<0?a+n:a]*=t[c][e],i[u<n?u+n:u]*=t[c][e],a++,u--;a=--o-n+1,u=--s}for(c=0;c<n;c++)l+=i[c];for(;c<r;c++)l-=i[c];return l},gauss_elimination:function(t,e){for(var n,r,i,o,s=0,a=0,u=t.length,c=t[0].length,l=0,h=[],f=(t=P.aug(t,e))[0].length,s=0;s<u;s++){for(r=t[s][s],o=(a=s)+1;o<c;o++)r<N.abs(t[o][s])&&(r=t[o][s],a=o);if(a!=s)for(o=0;o<f;o++)i=t[s][o],t[s][o]=t[a][o],t[a][o]=i;for(a=s+1;a<u;a++)for(n=t[a][s]/t[s][s],o=s;o<f;o++)t[a][o]=t[a][o]-n*t[s][o]}for(s=u-1;0<=s;s--){for(l=0,a=s+1;a<=u-1;a++)l+=h[a]*t[s][a];h[s]=(t[s][f-1]-l)/t[s][s]}return h},gauss_jordan:function(t,e){for(var n,r=P.aug(t,e),i=r.length,o=r[0].length,s=0,a=0;a<i;a++){for(var u=a,c=a+1;c<i;c++)N.abs(r[c][a])>N.abs(r[u][a])&&(u=c);var l=r[a];for(r[a]=r[u],r[u]=l,c=a+1;c<i;c++)for(s=r[c][a]/r[a][a],n=a;n<o;n++)r[c][n]-=r[a][n]*s}for(a=i-1;0<=a;a--){for(s=r[a][a],c=0;c<a;c++)for(n=o-1;a-1<n;n--)r[c][n]-=r[a][n]*r[c][a]/s;for(r[a][a]/=s,n=i;n<o;n++)r[a][n]/=s}return r},triaUpSolve:function(n,t){var r,i=n[0].length,o=P.zeros(1,i)[0],e=!1;return null!=t[0].length&&(t=t.map(function(t){return t[0]}),e=!0),P.arange(i-1,-1,-1).forEach(function(e){r=P.arange(e+1,i).map(function(t){return o[t]*n[e][t]}),o[e]=(t[e]-P.sum(r))/n[e][e]}),e?o.map(function(t){return[t]}):o},triaLowSolve:function(n,t){var r,e=n[0].length,i=P.zeros(1,e)[0],o=!1;return null!=t[0].length&&(t=t.map(function(t){return t[0]}),o=!0),P.arange(e).forEach(function(e){r=P.arange(e).map(function(t){return n[e][t]*i[t]}),i[e]=(t[e]-P.sum(r))/n[e][e]}),o?i.map(function(t){return[t]}):i},lu:function(r){var t,e=r.length,i=P.identity(e),o=P.zeros(r.length,r[0].length);return P.arange(e).forEach(function(t){o[0][t]=r[0][t]}),P.arange(1,e).forEach(function(n){P.arange(n).forEach(function(e){t=P.arange(e).map(function(t){return i[n][t]*o[t][e]}),i[n][e]=(r[n][e]-P.sum(t))/o[e][e]}),P.arange(n,e).forEach(function(e){t=P.arange(n).map(function(t){return i[n][t]*o[t][e]}),o[n][e]=r[t.length][e]-P.sum(t)})}),[i,o]},cholesky:function(t){var r,e=t.length,i=P.zeros(t.length,t[0].length);return P.arange(e).forEach(function(n){r=P.arange(n).map(function(t){return N.pow(i[n][t],2)}),i[n][n]=N.sqrt(t[n][n]-P.sum(r)),P.arange(n+1,e).forEach(function(e){r=P.arange(n).map(function(t){return i[n][t]*i[e][t]}),i[e][n]=(t[n][e]-P.sum(r))/i[n][n]})}),i},gauss_jacobi:function(t,e,n,r){for(var i,o,s,a,u=0,c=0,l=t.length,h=[],f=[],p=[];u<l;u++)for(h[u]=[],f[u]=[],p[u]=[],c=0;c<l;c++)c<u?(h[u][c]=t[u][c],f[u][c]=p[u][c]=0):u<c?(f[u][c]=t[u][c],h[u][c]=p[u][c]=0):(p[u][c]=t[u][c],h[u][c]=f[u][c]=0);for(s=P.multiply(P.multiply(P.inv(p),P.add(h,f)),-1),o=P.multiply(P.inv(p),e),a=P.add(P.multiply(s,i=n),o),u=2;N.abs(P.norm(P.subtract(a,i)))>r;)i=a,a=P.add(P.multiply(s,i),o),u++;return a},gauss_seidel:function(t,e,n,r){for(var i,o,s,a,u,c=0,l=t.length,h=[],f=[],p=[];c<l;c++)for(h[c]=[],f[c]=[],p[c]=[],i=0;i<l;i++)i<c?(h[c][i]=t[c][i],f[c][i]=p[c][i]=0):c<i?(f[c][i]=t[c][i],h[c][i]=p[c][i]=0):(p[c][i]=t[c][i],h[c][i]=f[c][i]=0);for(a=P.multiply(P.multiply(P.inv(P.add(p,h)),f),-1),s=P.multiply(P.inv(P.add(p,h)),e),u=P.add(P.multiply(a,o=n),s),c=2;N.abs(P.norm(P.subtract(u,o)))>r;)o=u,u=P.add(P.multiply(a,o),s),c+=1;return u},SOR:function(t,e,n,r,i){for(var o,s,a,u,c,l=0,h=t.length,f=[],p=[],d=[];l<h;l++)for(f[l]=[],p[l]=[],d[l]=[],o=0;o<h;o++)o<l?(f[l][o]=t[l][o],p[l][o]=d[l][o]=0):l<o?(p[l][o]=t[l][o],f[l][o]=d[l][o]=0):(d[l][o]=t[l][o],f[l][o]=p[l][o]=0);for(u=P.multiply(P.inv(P.add(d,P.multiply(f,i))),P.subtract(P.multiply(d,1-i),P.multiply(p,i))),a=P.multiply(P.multiply(P.inv(P.add(d,P.multiply(f,i))),e),i),c=P.add(P.multiply(u,s=n),a),l=2;N.abs(P.norm(P.subtract(c,s)))>r;)s=c,c=P.add(P.multiply(u,s),a),l++;return c},householder:function(t){for(var e,n,r,i,o,s=t.length,a=t[0].length,u=0,c=[];u<s-1;u++){for(n=0,o=u+1;o<a;o++)n+=t[o][u]*t[o][u];for(n=(0<t[u+1][u]?-1:1)*N.sqrt(n),r=N.sqrt((n*n-t[u+1][u]*n)/2),(c=P.zeros(s,1))[u+1][0]=(t[u+1][u]-n)/(2*r),i=u+2;i<s;i++)c[i][0]=t[i][u]/(2*r);e=P.subtract(P.identity(s,a),P.multiply(P.multiply(c,P.transpose(c)),2)),t=P.multiply(e,P.multiply(t,e))}return t},QR:(I=P.sum,j=P.arange,function(e){var t,n,r,i=e.length,o=e[0].length,s=P.zeros(o,o);for(e=P.copy(e),n=0;n<o;n++){for(s[n][n]=N.sqrt(I(j(i).map(function(t){return e[t][n]*e[t][n]}))),t=0;t<i;t++)e[t][n]=e[t][n]/s[n][n];for(r=n+1;r<o;r++)for(s[n][r]=I(j(i).map(function(t){return e[t][n]*e[t][r]})),t=0;t<i;t++)e[t][r]=e[t][r]-e[t][n]*s[n][r]}return[e,s]}),lstsq:function(t,e){var o,s,n=!1;void 0===e[0].length&&(e=e.map(function(t){return[t]}),n=!0);var r=(i=P.QR(t))[0],i=i[1],t=t[0].length,r=P.slice(r,{col:{end:t}}),i=P.slice(i,{row:{end:t}}),i=(o=i,t=(o=P.copy(o)).length,s=P.identity(t),P.arange(t-1,-1,-1).forEach(function(i){P.sliceAssign(s,{row:i},P.divide(P.slice(s,{row:i}),o[i][i])),P.sliceAssign(o,{row:i},P.divide(P.slice(o,{row:i}),o[i][i])),P.arange(i).forEach(function(t){var e=P.multiply(o[t][i],-1),n=P.slice(o,{row:t}),r=P.multiply(P.slice(o,{row:i}),e),n=(P.sliceAssign(o,{row:t},P.add(n,r)),P.slice(s,{row:t})),r=P.multiply(P.slice(s,{row:i}),e);P.sliceAssign(s,{row:t},P.add(n,r))})}),s);return void 0===(t=P.transpose(r))[0].length&&(t=[t]),void 0===(r=P.multiply(P.multiply(i,t),e)).length&&(r=[[r]]),n?r.map(function(t){return t[0]}):r},jacobi:function(t){for(var e,n,r,i,o,s,a,u=1,c=t.length,l=P.identity(c,c),h=[];1===u;){for(o=t[0][1],i=1,e=r=0;e<c;e++)for(n=0;n<c;n++)e!=n&&o<N.abs(t[e][n])&&(o=N.abs(t[e][n]),r=e,i=n);for(s=t[r][r]===t[i][i]?0<t[r][i]?N.PI/4:-N.PI/4:N.atan(2*t[r][i]/(t[r][r]-t[i][i]))/2,(a=P.identity(c,c))[r][r]=N.cos(s),a[r][i]=-N.sin(s),a[i][r]=N.sin(s),a[i][i]=N.cos(s),l=P.multiply(l,a),t=P.multiply(P.multiply(P.inv(a),t),a),u=0,e=1;e<c;e++)for(n=1;n<c;n++)e!=n&&.001<N.abs(t[e][n])&&(u=1)}for(e=0;e<c;e++)h.push(t[e][e]);return[l,h]},rungekutta:function(t,e,n,r,i,o){var s,a,u;if(2===o)for(;r<=n;)i=i+((s=e*t(r,i))+(a=e*t(r+e,i+s)))/2,r+=e;if(4===o)for(;r<=n;)i=i+((s=e*t(r,i))+2*(a=e*t(r+e/2,i+s/2))+2*(u=e*t(r+e/2,i+a/2))+e*t(r+e,i+u))/6,r+=e;return i},romberg:function(t,e,n,r){for(var i,o,s,a,u,c=0,l=(n-e)/2,h=[],f=[],p=[];c<r/2;){for(u=t(e),s=e,a=0;s<=n;s+=l,a++)h[a]=s;for(i=h.length,s=1;s<i-1;s++)u+=(s%2!=0?4:2)*t(h[s]);u=l/3*(u+t(n)),p[c]=u,l/=2,c++}for(o=p.length,i=1;1!==o;){for(s=0;s<o-1;s++)f[s]=(N.pow(4,i)*p[s+1]-p[s])/(N.pow(4,i)-1);o=f.length,p=f,f=[],i++}return p},richardson:function(t,e,n,r){function i(t,e){for(var n,r=0,i=t.length;r<i;r++)t[r]===e&&(n=r);return n}for(var o,s,a,u,c,l=N.abs(n-t[i(t,n)+1]),h=0,f=[],p=[];l<=r;)o=i(t,n+r),s=i(t,n),f[h]=(e[o]-2*e[s]+e[2*s-o])/(r*r),r/=2,h++;for(u=f.length,a=1;1!=u;){for(c=0;c<u-1;c++)p[c]=(N.pow(4,a)*f[c+1]-f[c])/(N.pow(4,a)-1);u=p.length,f=p,p=[],a++}return f},simpson:function(t,e,n,r){for(var i,o=(n-e)/r,s=t(e),a=[],u=e,c=0,l=1;u<=n;u+=o,c++)a[c]=u;for(i=a.length;l<i-1;l++)s+=(l%2!=0?4:2)*t(a[l]);return o/3*(s+t(n))},hermite:function(t,e,n,r){for(var i,o=t.length,s=0,a=0,u=[],c=[],l=[],h=[];a<o;a++){for(u[a]=1,i=0;i<o;i++)a!=i&&(u[a]*=(r-t[i])/(t[a]-t[i]));for(i=c[a]=0;i<o;i++)a!=i&&(c[a]+=1/(t[a]-t[i]));l[a]=(1-2*(r-t[a])*c[a])*(u[a]*u[a]),h[a]=(r-t[a])*(u[a]*u[a]),s+=l[a]*e[a]+h[a]*n[a]}return s},lagrange:function(t,e,n){for(var r,i,o=0,s=0,a=t.length;s<a;s++){for(i=e[s],r=0;r<a;r++)s!=r&&(i*=(n-t[r])/(t[s]-t[r]));o+=i}return o},cubic_spline:function(t,e,n){for(var r,i,o=t.length,s=0,a=[],u=[],c=[],l=[],h=[],f=[];s<o-1;s++)l[s]=t[s+1]-t[s];for(c[0]=0,s=1;s<o-1;s++)c[s]=3/l[s]*(e[s+1]-e[s])-3/l[s-1]*(e[s]-e[s-1]);for(s=1;s<o-1;s++)a[s]=[],u[s]=[],a[s][s-1]=l[s-1],a[s][s]=2*(l[s-1]+l[s]),a[s][s+1]=l[s],u[s][0]=c[s];for(i=P.multiply(P.inv(a),u),r=0;r<o-1;r++)h[r]=(e[r+1]-e[r])/l[r]-l[r]*(i[r+1][0]+2*i[r][0])/3,f[r]=(i[r+1][0]-i[r][0])/(3*l[r]);for(r=0;r<o&&!(t[r]>n);r++);return e[--r]+(n-t[r])*h[r]+P.sq(n-t[r])*i[r]+(n-t[r])*P.sq(n-t[r])*f[r]},gauss_quadrature:function(){throw new Error("gauss_quadrature not yet implemented")},PCA:function(t){for(var e,n,r,i,o,s=t.length,a=t[0].length,u=0,c=[],l=[],h=[],f=[],p=[],d=[],u=0;u<s;u++)c[u]=P.sum(t[u])/a;for(u=0;u<a;u++)for(f[u]=[],e=0;e<s;e++)f[u][e]=t[e][u]-c[e];for(f=P.transpose(f),u=0;u<s;u++)for(p[u]=[],e=0;e<s;e++)p[u][e]=P.dot([f[u]],[f[e]])/(a-1);for(o=(n=P.jacobi(p))[0],l=n[1],d=P.transpose(o),u=0;u<l.length;u++)for(e=u;e<l.length;e++)l[u]<l[e]&&(r=l[u],l[u]=l[e],l[e]=r,r=d[u],d[u]=d[e],d[e]=r);for(i=P.transpose(f),u=0;u<s;u++)for(h[u]=[],e=0;e<i.length;e++)h[u][e]=P.dot([d[u]],[i[e]]);return[t,l,d,h]}});for(var L="add divide multiply subtract dot pow exp log abs norm angle".split(" "),R=0;R<L.length;R++)!function(r){P.fn[r]=function(t,e){var n=this;return e?(setTimeout(function(){e.call(n,P.fn[r].call(n,t))},15),this):"number"==typeof P[r](this,t)?P[r](this,t):P(P[r](this,t))}}(L[R]);function F(t,e,n,r){if(1<t||1<n||t<=0||n<=0)throw new Error("Proportions should be greater than 0 and less than 1");var i=(t*e+n*r)/(e+r);return(t-n)/l.sqrt(i*(1-i)*(1/e+1/r))}function H(t,e){var n=t.length,r=e[0].length-1,i=n-r-1,o=f.lstsq(e,t),s=f.multiply(e,o.map(function(t){return[t]})).map(function(t){return t[0]}),a=f.subtract(t,s),u=f.mean(t),c=f.sum(s.map(function(t){return Math.pow(t-u,2)})),l=f.sum(t.map(function(t,e){return Math.pow(t-s[e],2)})),h=c+l;return{exog:e,endog:t,nobs:n,df_model:r,df_resid:i,coef:o,predict:s,resid:a,ybar:u,SST:h,SSE:c,SSR:l,R2:c/h}}function B(e){n=e.exog,r=n[0].length;var n,r,t=f.arange(r).map(function(e){var t=f.arange(r).filter(function(t){return t!==e});return H(f.col(n,e).map(function(t){return t[0]}),f.col(n,t))}),i=Math.sqrt(e.SSR/e.df_resid),o=t.map(function(t){var e=t.SST,t=t.R2;return i/Math.sqrt(e*(1-t))}),t=e.coef.map(function(t,e){return+t/o[e]}),s=t.map(function(t){t=f.studentt.cdf(t,e.df_resid);return 2*(.5<t?1-t:t)}),a=f.studentt.inv(.975,e.df_resid),u=e.coef.map(function(t,e){e=a*o[e];return[t-e,t+e]});return{se:o,t:t,p:s,sigmaHat:i,interval95:u}}return c=f,l=Math,h=[].slice,o=c.utils.isNumber,n=c.utils.isArray,c.extend({zscore:function(){var t=h.call(arguments);return o(t[1])?(t[0]-t[1])/t[2]:(t[0]-c.mean(t[1]))/c.stdev(t[1],t[2])},ztest:function(){var t,e=h.call(arguments);return n(e[1])?(t=c.zscore(e[0],e[1],e[3]),1===e[2]?c.normal.cdf(-l.abs(t),0,1):2*c.normal.cdf(-l.abs(t),0,1)):2<e.length?(t=c.zscore(e[0],e[1],e[2]),1===e[3]?c.normal.cdf(-l.abs(t),0,1):2*c.normal.cdf(-l.abs(t),0,1)):(t=e[0],1===e[1]?c.normal.cdf(-l.abs(t),0,1):2*c.normal.cdf(-l.abs(t),0,1))}}),c.extend(c.fn,{zscore:function(t,e){return(t-this.mean())/this.stdev(e)},ztest:function(t,e,n){t=l.abs(this.zscore(t,n));return 1===e?c.normal.cdf(-t,0,1):2*c.normal.cdf(-t,0,1)}}),c.extend({tscore:function(){var t=h.call(arguments);return 4===t.length?(t[0]-t[1])/(t[2]/l.sqrt(t[3])):(t[0]-c.mean(t[1]))/(c.stdev(t[1],!0)/l.sqrt(t[1].length))},ttest:function(){var t,e=h.call(arguments);return 5===e.length?(t=l.abs(c.tscore(e[0],e[1],e[2],e[3])),1===e[4]?c.studentt.cdf(-t,e[3]-1):2*c.studentt.cdf(-t,e[3]-1)):o(e[1])?(t=l.abs(e[0]),1==e[2]?c.studentt.cdf(-t,e[1]-1):2*c.studentt.cdf(-t,e[1]-1)):(t=l.abs(c.tscore(e[0],e[1])),1==e[2]?c.studentt.cdf(-t,e[1].length-1):2*c.studentt.cdf(-t,e[1].length-1))}}),c.extend(c.fn,{tscore:function(t){return(t-this.mean())/(this.stdev(!0)/l.sqrt(this.cols()))},ttest:function(t,e){return 1===e?1-c.studentt.cdf(l.abs(this.tscore(t)),this.cols()-1):2*c.studentt.cdf(-l.abs(this.tscore(t)),this.cols()-1)}}),c.extend({anovafscore:function(){var t,e,n,r,i,o,s,a,u=h.call(arguments);if(1===u.length){for(i=new Array(u[0].length),s=0;s<u[0].length;s++)i[s]=u[0][s];u=i}for(e=new Array,s=0;s<u.length;s++)e=e.concat(u[s]);for(n=c.mean(e),s=t=0;s<u.length;s++)t+=u[s].length*l.pow(c.mean(u[s])-n,2);for(t/=u.length-1,s=o=0;s<u.length;s++)for(r=c.mean(u[s]),a=0;a<u[s].length;a++)o+=l.pow(u[s][a]-r,2);return t/(o/=e.length-u.length)},anovaftest:function(){var t=h.call(arguments);if(o(t[0]))return 1-c.centralF.cdf(t[0],t[1],t[2]);for(var e=c.anovafscore(t),n=t.length-1,r=0,i=0;i<t.length;i++)r+=t[i].length;return 1-c.centralF.cdf(e,n,r-n-1)},ftest:function(t,e,n){return 1-c.centralF.cdf(t,e,n)}}),c.extend(c.fn,{anovafscore:function(){return c.anovafscore(this.toArray())},anovaftes:function(){for(var t=0,e=0;e<this.length;e++)t+=this[e].length;return c.ftest(this.anovafscore(),this.length-1,t-this.length)}}),c.extend({qscore:function(){var t,e,n,r,i=h.call(arguments),i=o(i[0])?(t=i[0],e=i[1],n=i[2],r=i[3],i[4]):(t=c.mean(i[0]),e=c.mean(i[1]),n=i[0].length,r=i[1].length,i[2]);return l.abs(t-e)/(i*l.sqrt((1/n+1/r)/2))},qtest:function(){var t,e=h.call(arguments),n=(e=3===e.length?(t=e[0],e.slice(1)):7===e.length?(t=c.qscore(e[0],e[1],e[2],e[3],e[4]),e.slice(5)):(t=c.qscore(e[0],e[1],e[2]),e.slice(3)))[0],e=e[1];return 1-c.tukey.cdf(t,e,n-e)},tukeyhsd:function(t){for(var e=c.pooledstdev(t),n=t.map(function(t){return c.mean(t)}),r=t.reduce(function(t,e){return t+e.length},0),i=[],o=0;o<t.length;++o)for(var s=o+1;s<t.length;++s){var a=c.qtest(n[o],n[s],t[o].length,t[s].length,e,r,t.length);i.push([[o,s],a])}return i}}),c.extend({normalci:function(){var t=h.call(arguments),e=new Array(2),n=4===t.length?l.abs(c.normal.inv(t[1]/2,0,1)*t[2]/l.sqrt(t[3])):l.abs(c.normal.inv(t[1]/2,0,1)*c.stdev(t[2])/l.sqrt(t[2].length));return e[0]=t[0]-n,e[1]=t[0]+n,e},tci:function(){var t=h.call(arguments),e=new Array(2),n=4===t.length?l.abs(c.studentt.inv(t[1]/2,t[3]-1)*t[2]/l.sqrt(t[3])):l.abs(c.studentt.inv(t[1]/2,t[2].length-1)*c.stdev(t[2],!0)/l.sqrt(t[2].length));return e[0]=t[0]-n,e[1]=t[0]+n,e},significant:function(t,e){return t<e}}),c.extend(c.fn,{normalci:function(t,e){return c.normalci(t,e,this.toArray())},tci:function(t,e){return c.tci(t,e,this.toArray())}}),c.extend(c.fn,{oneSidedDifferenceOfProportions:function(t,e,n,r){t=F(t,e,n,r);return c.ztest(t,1)},twoSidedDifferenceOfProportions:function(t,e,n,r){t=F(t,e,n,r);return c.ztest(t,2)}}),f.models={ols:function(t,e){var n,e=B(t=H(t,e)),r=(i=(r=t).R2/r.df_model/((1-r.R2)/r.df_resid),n=1-(n=r.df_model,r=r.df_resid,f.beta.cdf(i/(r/n+i),n/2,r/2)),{F_statistic:i,pvalue:n}),i=1-(1-t.R2)*((t.nobs-1)/t.df_resid);return t.t=e,t.f=r,t.adjust_R2=i,t}},f.extend({buildxmatrix:function(){for(var t=new Array(arguments.length),e=0;e<arguments.length;e++)t[e]=[1].concat(arguments[e]);return f(t)},builddxmatrix:function(){for(var t=new Array(arguments[0].length),e=0;e<arguments[0].length;e++)t[e]=[1].concat(arguments[0][e]);return f(t)},buildjxmatrix:function(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=t[n];return f.builddxmatrix(e)},buildymatrix:function(t){return f(t).transpose()},buildjymatrix:function(t){return t.transpose()},matrixmult:function(t,e){var n,r,i,o,s;if(t.cols()==e.rows()){if(1<e.rows())for(o=[],n=0;n<t.rows();n++)for(o[n]=[],r=0;r<e.cols();r++){for(i=s=0;i<t.cols();i++)s+=t.toArray()[n][i]*e.toArray()[i][r];o[n][r]=s}else for(o=[],n=0;n<t.rows();n++)for(o[n]=[],r=0;r<e.cols();r++){for(i=s=0;i<t.cols();i++)s+=t.toArray()[n][i]*e.toArray()[r];o[n][r]=s}return f(o)}},regress:function(t,e){var n=f.xtranspxinv(t),t=t.transpose(),n=f.matrixmult(f(n),t);return f.matrixmult(n,e)},regresst:function(t,e,n){for(var r,i,o,s=f.regress(t,e),a={anova:{}},u=f.jMatYBar(t,s),c=(a.yBar=u,e.mean()),l=(a.anova.residuals=f.residuals(e,u),a.anova.ssr=f.ssr(u,c),a.anova.msr=a.anova.ssr/(t[0].length-1),a.anova.sse=f.sse(e,u),a.anova.mse=a.anova.sse/(e.length-(t[0].length-1)-1),a.anova.sst=f.sst(e,c),a.anova.mst=a.anova.sst/(e.length-1),a.anova.r2=1-a.anova.sse/a.anova.sst,a.anova.r2<0&&(a.anova.r2=0),a.anova.fratio=a.anova.msr/a.anova.mse,a.anova.pvalue=f.anovaftest(a.anova.fratio,t[0].length-1,e.length-(t[0].length-1)-1),a.anova.rmse=Math.sqrt(a.anova.mse),a.anova.r2adj=1-a.anova.mse/a.anova.mst,a.anova.r2adj<0&&(a.anova.r2adj=0),a.stats=new Array(t[0].length),f.xtranspxinv(t)),h=0;h<s.length;h++)r=Math.sqrt(a.anova.mse*Math.abs(l[h][h])),i=Math.abs(s[h]/r),o=f.ttest(i,e.length-t[0].length-1,n),a.stats[h]=[s[h],r,i,o];return a.regress=s,a},xtranspx:function(t){return f.matrixmult(t.transpose(),t)},xtranspxinv:function(t){t=f.matrixmult(t.transpose(),t);return f.inv(t)},jMatYBar:function(t,e){t=f.matrixmult(t,e);return new f(t)},residuals:function(t,e){return f.matrixsubtract(t,e)},ssr:function(t,e){for(var n=0,r=0;r<t.length;r++)n+=Math.pow(t[r]-e,2);return n},sse:function(t,e){for(var n=0,r=0;r<t.length;r++)n+=Math.pow(t[r]-e[r],2);return n},sst:function(t,e){for(var n=0,r=0;r<t.length;r++)n+=Math.pow(t[r]-e,2);return n},matrixsubtract:function(t,e){for(var n=new Array(t.length),r=0;r<t.length;r++){n[r]=new Array(t[r].length);for(var i=0;i<t[r].length;i++)n[r][i]=t[r][i]-e[r][i]}return f(n)}}),f.jStat=f});var CryptoApi=function(n){var r={};function i(t){var e;return(r[t]||(e=r[t]={i:t,l:!1,exports:{}},n[t].call(e.exports,e,e.exports,i),e.l=!0,e)).exports}return i.m=n,i.c=r,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=29)}([function(t,e,n){"use strict";function r(t,e){return t<<e|t>>>32-e|0}function i(t,e){return t>>>e|t<<32-e|0}function o(t,e,n){return 32===n?e:32<n?o(e,t,n-32):4294967295&(t>>>n|e<<32-n)}function s(t,e,n){return 32===n?t:32<n?s(e,t,n-32):4294967295&(e>>>n|t<<32-n)}n.d(e,"a",function(){return r}),n.d(e,"b",function(){return i}),n.d(e,"d",function(){return s}),n.d(e,"c",function(){return o})},function(t,e,n){"use strict";function r(t){for(var e="",n=0,r=t.length;n<r;n++){var i=t.charCodeAt(n);i<128?e+=String.fromCharCode(i):(i<2048?e+=String.fromCharCode(192|i>>6):(i<55296||57344<=i?e+=String.fromCharCode(224|i>>12):(n++,i=65536+((1023&i)<<10|1023&t.charCodeAt(n)),e=(e+=String.fromCharCode(240|i>>18))+String.fromCharCode(128|i>>12&63)),e+=String.fromCharCode(128|i>>6&63)),e+=String.fromCharCode(128|63&i))}return e}n.d(e,"a",function(){return r})},function(t,e,n){"use strict";function r(t){for(var e="",n=0,r=t.length;n<r;n++)e+=(t.charCodeAt(n)<16?"0":"")+t.charCodeAt(n).toString(16);return e}n.d(e,"a",function(){return r})},function(t,e,n){"use strict";var a=n(4);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,e){return(l=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}n=function(){function e(t){if(this instanceof e)return(t=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,c(e).call(this,t))).unitOrder=1,t.blockUnits=[],t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&l(t,n);for(var t=[{key:"process",value:function(){for(;this.state.message.length>=this.blockSizeInBytes;){this.blockUnits=[];for(var t=0;t<this.blockSizeInBytes;t+=4)this.blockUnits.push(this.state.message.charCodeAt(t)<<24|this.state.message.charCodeAt(t+1)<<16|this.state.message.charCodeAt(t+2)<<8|this.state.message.charCodeAt(t+3));this.state.message=this.state.message.substr(this.blockSizeInBytes),this.processBlock(this.blockUnits)}}},{key:"processBlock",value:function(t){}},{key:"getStateHash",value:function(t){t=t||this.state.hash.length;for(var e="",n=0;n<t;n++)e+=String.fromCharCode(this.state.hash[n]>>24&255)+String.fromCharCode(this.state.hash[n]>>16&255)+String.fromCharCode(this.state.hash[n]>>8&255)+String.fromCharCode(255&this.state.hash[n]);return e}},{key:"addLengthBits",value:function(){this.state.message+="\0\0\0"+String.fromCharCode(this.state.length>>29&255)+String.fromCharCode(this.state.length>>21&255)+String.fromCharCode(this.state.length>>13&255)+String.fromCharCode(this.state.length>>5&255)+String.fromCharCode(this.state.length<<3&255)}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";var r=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.unitSize=4,this.unitOrder=0,this.blockSize=16,this.blockSizeInBytes=this.blockSize*this.unitSize,this.options=t||{},this.reset()}for(var t=[{key:"reset",value:function(){this.state={},this.state.message="",this.state.length=0}},{key:"getState",value:function(){return JSON.parse(JSON.stringify(this.state))}},{key:"setState",value:function(t){this.state=t}},{key:"update",value:function(t){this.state.message+=t,this.state.length+=t.length,this.process()}},{key:"process",value:function(){}},{key:"finalize",value:function(){return""}},{key:"getStateHash",value:function(t){return""}},{key:"addPaddingPKCS7",value:function(t){this.state.message+=new Array(t+1).join(String.fromCharCode(t))}},{key:"addPaddingISO7816",value:function(t){this.state.message+=""+new Array(t).join("\0")}},{key:"addPaddingZero",value:function(t){this.state.message+=new Array(t+1).join("\0")}}],n=e.prototype,r=t,i=0;i<r.length;i++){var o=r[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(n,o.key,o)}return e}();e.a=r},function(t,e,n){"use strict";var a=n(4);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,e){return(l=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}n=function(){function e(t){if(this instanceof e)return(t=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,c(e).call(this,t))).blockUnits=[],t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&l(t,n);for(var t=[{key:"process",value:function(){for(;this.state.message.length>=this.blockSizeInBytes;){this.blockUnits=[];for(var t=0;t<this.blockSizeInBytes;t+=4)this.blockUnits.push(this.state.message.charCodeAt(t)|this.state.message.charCodeAt(t+1)<<8|this.state.message.charCodeAt(t+2)<<16|this.state.message.charCodeAt(t+3)<<24);this.state.message=this.state.message.substr(this.blockSizeInBytes),this.processBlock(this.blockUnits)}}},{key:"processBlock",value:function(t){}},{key:"getStateHash",value:function(t){t=t||this.state.hash.length;for(var e="",n=0;n<t;n++)e+=String.fromCharCode(255&this.state.hash[n])+String.fromCharCode(this.state.hash[n]>>8&255)+String.fromCharCode(this.state.hash[n]>>16&255)+String.fromCharCode(this.state.hash[n]>>24&255);return e}},{key:"addLengthBits",value:function(){this.state.message+=String.fromCharCode(this.state.length<<3&255)+String.fromCharCode(this.state.length>>5&255)+String.fromCharCode(this.state.length>>13&255)+String.fromCharCode(this.state.length>>21&255)+String.fromCharCode(this.state.length>>29&255)+"\0\0\0"}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";var a=n(3),T=n(0);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t,e,n){return(c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function l(t){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var A=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591],n=function(){function i(t){if(this instanceof i)return(t=t||{}).length=t.length||512,t.rounds=t.rounds||160,(t=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,l(i).call(this,t))).blockSize=32,t.blockSizeInBytes=t.blockSize*t.unitSize,t.W=new Array(160),t;throw new TypeError("Cannot call a class as a function")}var t=i,e=a.a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&h(t,e);for(var t=[{key:"reset",value:function(){switch(c(l(i.prototype),"reset",this).call(this),this.options.length){case 384:this.state.hash=[-876896931,-1056596264,1654270250,914150663,-1856437926,812702999,355462360,-150054599,1731405415,-4191439,-1900787065,1750603025,-619958771,1694076839,1203062813,-1090891868];break;case 512:this.state.hash=[1779033703,-205731576,-1150833019,-2067093701,1013904242,-23791573,-1521486534,1595750129,1359893119,-1377402159,-1694144372,725511199,528734635,-79577749,1541459225,327033209];break;default:for(var t=new i,e=0;e<16;e++)t.state.hash[e]=2779096485^t.state.hash[e];t.update("SHA-512/"+this.options.length);var n=t.finalize();this.state.hash=[];for(var r=0;r<64;r+=4)this.state.hash.push(n.charCodeAt(r)<<24|n.charCodeAt(r+1)<<16|n.charCodeAt(r+2)<<8|n.charCodeAt(r+3))}}},{key:"processBlock",value:function(t){for(var e=this.state.hash[0],n=this.state.hash[1],r=this.state.hash[2],i=this.state.hash[3],o=this.state.hash[4],s=this.state.hash[5],a=this.state.hash[6],u=this.state.hash[7],c=this.state.hash[8],l=this.state.hash[9],h=this.state.hash[10],f=this.state.hash[11],p=this.state.hash[12],d=this.state.hash[13],g=this.state.hash[14],m=this.state.hash[15],v=0;v<this.options.rounds;v+=2){v<32?(this.W[v]=t[v],this.W[v+1]=t[v+1]):(y=Object(T.c)(this.W[v-30],this.W[v-29],1)^Object(T.c)(this.W[v-30],this.W[v-29],8)^this.W[v-30]>>>7,b=Object(T.d)(this.W[v-30],this.W[v-29],1)^Object(T.d)(this.W[v-30],this.W[v-29],8)^(this.W[v-29]>>>7|this.W[v-30]<<25),w=Object(T.c)(this.W[v-4],this.W[v-3],19)^Object(T.c)(this.W[v-4],this.W[v-3],61)^this.W[v-4]>>>6,_=Object(T.d)(this.W[v-4],this.W[v-3],19)^Object(T.d)(this.W[v-4],this.W[v-3],61)^(this.W[v-3]>>>6|this.W[v-4]<<26),O=(65535&this.W[v-13])+(65535&this.W[v-31])+(65535&b)+(65535&_)|0,k=(this.W[v-13]>>>16)+(this.W[v-31]>>>16)+(b>>>16)+(_>>>16)+(O>>>16)|0,C=(65535&this.W[v-14])+(65535&this.W[v-32])+(65535&y)+(65535&w)+(k>>>16)|0,x=(this.W[v-14]>>>16)+(this.W[v-32]>>>16)+(y>>>16)+(w>>>16)+(C>>>16)|0,this.W[v]=4294967295&(x<<16|65535&C),this.W[v+1]=4294967295&(k<<16|65535&O)),y=Object(T.c)(e,n,28)^Object(T.c)(e,n,34)^Object(T.c)(e,n,39),b=Object(T.d)(e,n,28)^Object(T.d)(e,n,34)^Object(T.d)(e,n,39);var y,b,w,_,x=l&f^~l&d,C=e&r^e&o^r&o,k=n&i^n&s^i&s,S=((g+(w=Object(T.c)(c,l,14)^Object(T.c)(c,l,18)^Object(T.c)(c,l,41))+((O=m+(_=Object(T.d)(c,l,14)^Object(T.d)(c,l,18)^Object(T.d)(c,l,41))|0)>>>0<m>>>0?1:0)|0)+(c&h^~c&p)+((O=O+x|0)>>>0<x>>>0?1:0)|0)+A[v]+((O=O+A[v+1]|0)>>>0<A[v+1]>>>0?1:0)|0,O=O+this.W[v+1]|0,E=b+k|0,g=p,m=d,p=h,d=f,h=c,f=l,c=a+(S=S+this.W[v]+(O>>>0<this.W[v+1]>>>0?1:0)|0)+((l=u+O|0)>>>0<u>>>0?1:0)|0,a=o,u=s,o=r,s=i,r=e,i=n,e=S+(y+C+(E>>>0<b>>>0?1:0)|0)+((n=O+E|0)>>>0<O>>>0?1:0)|0}this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[0]=this.state.hash[0]+e+(this.state.hash[1]>>>0<n>>>0?1:0)|0,this.state.hash[3]=this.state.hash[3]+i|0,this.state.hash[2]=this.state.hash[2]+r+(this.state.hash[3]>>>0<i>>>0?1:0)|0,this.state.hash[5]=this.state.hash[5]+s|0,this.state.hash[4]=this.state.hash[4]+o+(this.state.hash[5]>>>0<s>>>0?1:0)|0,this.state.hash[7]=this.state.hash[7]+u|0,this.state.hash[6]=this.state.hash[6]+a+(this.state.hash[7]>>>0<u>>>0?1:0)|0,this.state.hash[9]=this.state.hash[9]+l|0,this.state.hash[8]=this.state.hash[8]+c+(this.state.hash[9]>>>0<l>>>0?1:0)|0,this.state.hash[11]=this.state.hash[11]+f|0,this.state.hash[10]=this.state.hash[10]+h+(this.state.hash[11]>>>0<f>>>0?1:0)|0,this.state.hash[13]=this.state.hash[13]+d|0,this.state.hash[12]=this.state.hash[12]+p+(this.state.hash[13]>>>0<d>>>0?1:0)|0,this.state.hash[15]=this.state.hash[15]+m|0,this.state.hash[14]=this.state.hash[14]+g+(this.state.hash[15]>>>0<m>>>0?1:0)|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<112?112-this.state.message.length|0:240-this.state.message.length|0),this.state.message+="\0\0\0\0\0\0\0\0",this.addLengthBits(),this.process(),this.getStateHash(this.options.length/32|0)}}],n=i.prototype,r=t,o=0;o<r.length;o++){var s=r[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(n,s.key,s)}return i}();e.a=n},function(t,e,n){"use strict";var a=n(3),f=n(0);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t,e,n){return(c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function l(t){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var p=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],n=function(){function e(t){if(this instanceof e)return(t=t||{}).length=t.length||256,t.rounds=t.rounds||64,(t=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,l(e).call(this,t))).W=new Array(64),t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&h(t,n);for(var t=[{key:"reset",value:function(){224===(c(l(e.prototype),"reset",this).call(this),this.options.length)?this.state.hash=[-1056596264,914150663,812702999,-150054599,-4191439,1750603025,1694076839,-1090891868]:this.state.hash=[1779033703,-1150833019,1013904242,-1521486534,1359893119,-1694144372,528734635,1541459225]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],o=0|this.state.hash[4],s=0|this.state.hash[5],a=0|this.state.hash[6],u=0|this.state.hash[7],c=0;c<this.options.rounds;c++){this.W[c]=c<16?0|t[c]:this.W[c-16]+(Object(f.b)(this.W[c-15],7)^Object(f.b)(this.W[c-15],18)^this.W[c-15]>>>3)+this.W[c-7]+(Object(f.b)(this.W[c-2],17)^Object(f.b)(this.W[c-2],19)^this.W[c-2]>>>10)|0;var l=u+(Object(f.b)(o,6)^Object(f.b)(o,11)^Object(f.b)(o,25))+(o&s^~o&a)+p[c]+this.W[c]|0,h=(Object(f.b)(e,2)^Object(f.b)(e,13)^Object(f.b)(e,22))+(e&n^e&r^n&r)|0,u=a,a=s,s=o,o=i+l|0,i=r,r=n,n=e,e=l+h|0}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+i|0,this.state.hash[4]=this.state.hash[4]+o|0,this.state.hash[5]=this.state.hash[5]+s|0,this.state.hash[6]=this.state.hash[6]+a|0,this.state.hash[7]=this.state.hash[7]+u|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash(this.options.length/32|0)}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";var r=n(5),g=n(0);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var m=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],v=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],y=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],b=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11],n=function(){function d(t){if(!(this instanceof d))throw new TypeError("Cannot call a class as a function");(t=t||{}).length=t.length||160;var e=this,t=a(d).call(this,t);if(!t||"object"!==i(t)&&"function"!=typeof t){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t}var t=d,e=r.a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&u(t,e),t=[{key:"F",value:function(t,e,n){return t^e^n}},{key:"G",value:function(t,e,n){return t&e|~t&n}},{key:"H",value:function(t,e,n){return(t|~e)^n}},{key:"I",value:function(t,e,n){return t&n|e&~n}},{key:"J",value:function(t,e,n){return t^(e|~n)}},{key:"T",value:function(t,e,n,r){return t<16?this.F(e,n,r):t<32?this.G(e,n,r)+1518500249|0:t<48?this.H(e,n,r)+1859775393|0:t<64?this.I(e,n,r)+2400959708|0:this.J(e,n,r)+2840853838|0}},{key:"T64",value:function(t,e,n,r){return t<16?this.I(e,n,r)+1352829926|0:t<32?this.H(e,n,r)+1548603684|0:t<48?this.G(e,n,r)+1836072691|0:this.F(e,n,r)}},{key:"T80",value:function(t,e,n,r){return t<16?this.J(e,n,r)+1352829926|0:t<32?this.I(e,n,r)+1548603684|0:t<48?this.H(e,n,r)+1836072691|0:t<64?this.G(e,n,r)+2053994217|0:this.F(e,n,r)}}],o((e=d).prototype,[{key:"reset",value:function(){switch(s(a(d.prototype),"reset",this).call(this),this.options.length){case 128:this.state.hash=[1732584193,4023233417,2562383102,271733878],this.processBlock=this.processBlock128;break;case 256:this.state.hash=[1732584193,4023233417,2562383102,271733878,1985229328,4275878552,2309737967,19088743],this.processBlock=this.processBlock256;break;case 320:this.state.hash=[1732584193,4023233417,2562383102,271733878,3285377520,1985229328,4275878552,2309737967,19088743,1009589775],this.processBlock=this.processBlock320;break;default:this.state.hash=[1732584193,4023233417,2562383102,271733878,3285377520],this.processBlock=this.processBlock160}}},{key:"processBlock128",value:function(t){for(var e=a=0|this.state.hash[0],n=l=0|this.state.hash[1],r=c=0|this.state.hash[2],i=u=0|this.state.hash[3],o=0;o<64;o++){var s=(a+t[m[o]]|0)+d.T(o,l,c,u)|0,a=u,u=c,c=l,l=Object(g.a)(s,y[o]);s=(e+t[v[o]]|0)+d.T64(o,n,r,i)|0,e=i,i=r,r=n,n=Object(g.a)(s,b[o])}var h=this.state.hash[1]+c+i|0;this.state.hash[1]=this.state.hash[2]+u+e|0,this.state.hash[2]=this.state.hash[3]+a+n|0,this.state.hash[3]=this.state.hash[0]+l+r|0,this.state.hash[0]=h}},{key:"processBlock160",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],o=0|this.state.hash[4],s=e,a=n,u=r,c=i,l=o,h=0;h<80;h++){var f=(e+t[m[h]]|0)+d.T(h,n,r,i)|0;f=Object(g.a)(f,y[h])+o|0,e=o,o=i,i=Object(g.a)(r,10),r=n,n=f,f=(s+t[v[h]]|0)+d.T80(h,a,u,c)|0,f=Object(g.a)(f,b[h])+l|0,s=l,l=c,c=Object(g.a)(u,10),u=a,a=f}var p=this.state.hash[1]+r+c|0;this.state.hash[1]=this.state.hash[2]+i+l|0,this.state.hash[2]=this.state.hash[3]+o+s|0,this.state.hash[3]=this.state.hash[4]+e+a|0,this.state.hash[4]=this.state.hash[0]+n+u|0,this.state.hash[0]=p}},{key:"processBlock256",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],o=0|this.state.hash[4],s=0|this.state.hash[5],a=0|this.state.hash[6],u=0|this.state.hash[7],c=0;c<64;c+=1){var l=(l=e+t[m[c]]|0)+d.T(c,n,r,i)|0,e=i,i=r,r=n,n=l=Object(g.a)(l,y[c]);switch(l=(l=o+t[v[c]]|0)+d.T64(c,s,a,u)|0,o=u,u=a,a=s,s=l=Object(g.a)(l,b[c]),c){case 15:l=e,e=o,o=l;break;case 31:l=n,n=s,s=l;break;case 47:l=r,r=a,a=l;break;case 63:l=i,i=u,u=l}}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+i|0,this.state.hash[4]=this.state.hash[4]+o|0,this.state.hash[5]=this.state.hash[5]+s|0,this.state.hash[6]=this.state.hash[6]+a|0,this.state.hash[7]=this.state.hash[7]+u|0}},{key:"processBlock320",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],o=0|this.state.hash[4],s=0|this.state.hash[5],a=0|this.state.hash[6],u=0|this.state.hash[7],c=0|this.state.hash[8],l=0|this.state.hash[9],h=0;h<80;h+=1){var f=(f=e+t[m[h]]|0)+d.T(h,n,r,i)|0;switch(f=(f=Object(g.a)(f,y[h]))+o|0,e=o,o=i,i=Object(g.a)(r,10),r=n,n=f,f=(f=s+t[v[h]]|0)+d.T80(h,a,u,c)|0,f=(f=Object(g.a)(f,b[h]))+l|0,s=l,l=c,c=Object(g.a)(u,10),u=a,a=f,h){case 15:f=n,n=a,a=f;break;case 31:f=i,i=c,c=f;break;case 47:f=e,e=s,s=f;break;case 63:f=r,r=u,u=f;break;case 79:f=o,o=l,l=f}}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+i|0,this.state.hash[4]=this.state.hash[4]+o|0,this.state.hash[5]=this.state.hash[5]+s|0,this.state.hash[6]=this.state.hash[6]+a|0,this.state.hash[7]=this.state.hash[7]+u|0,this.state.hash[8]=this.state.hash[8]+c|0,this.state.hash[9]=this.state.hash[9]+l|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}]),o(e,t),d}();e.a=n},function(t,e,n){"use strict";var a=n(3),c=n(0);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e,n){return(l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=h(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var o=[10097,32533,76520,13586,34673,54876,80959,9117,39292,74945,37542,4805,64894,74296,24805,24037,20636,10402,822,91665,8422,68953,19645,9303,23209,2560,15953,34764,35080,33606,99019,2529,9376,70715,38311,31165,88676,74397,4436,27659,12807,99970,80157,36147,64032,36653,98951,16877,12171,76833,66065,74717,34072,76850,36697,36170,65813,39885,11199,29170,31060,10805,45571,82406,35303,42614,86799,7439,23403,9732,85269,77602,2051,65692,68665,74818,73053,85247,18623,88579,63573,32135,5325,47048,90553,57548,28468,28709,83491,25624,73796,45753,3529,64778,35808,34282,60935,20344,35273,88435,98520,17767,14905,68607,22109,40558,60970,93433,50500,73998,11805,5431,39808,27732,50725,68248,29405,24201,52775,67851,83452,99634,6288,98083,13746,70078,18475,40610,68711,77817,88685,40200,86507,58401,36766,67951,90364,76493,29609,11062,99594,67348,87517,64969,91826,8928,93785,61368,23478,34113,65481,17674,17468,50950,58047,76974,73039,57186,40218,16544,80124,35635,17727,8015,45318,22374,21115,78253,14385,53763,74350,99817,77402,77214,43236,210,45521,64237,96286,2655,69916,26803,66252,29148,36936,87203,76621,13990,94400,56418,9893,20505,14225,68514,46427,56788,96297,78822,54382,14598,91499,14523,68479,27686,46162,83554,94750,89923,37089,20048,80336,94598,26940,36858,70297,34135,53140,33340,42050,82341,44104,81949,85157,47954,32979,26575,57600,40881,22222,6413,12550,73742,11100,2040,12860,74697,96644,89439,28707,25815,63606,49329,16505,34484,40219,52563,43651,77082,7207,31790,61196,90446,26457,47774,51924,33729,65394,59593,42582,60527,15474,45266,95270,79953,59367,83848,82396,10118,33211,59466,94557,28573,67897,54387,54622,44431,91190,42592,92927,45973,42481,16213,97344,8721,16868,48767,3071,12059,25701,46670,23523,78317,73208,89837,68935,91416,26252,29663,5522,82562,4493,52494,75246,33824,45862,51025,61962,79335,65337,12472,549,97654,64051,88159,96119,63896,54692,82391,23287,29529,35963,15307,26898,9354,33351,35462,77974,50024,90103,39333,59808,8391,45427,26842,83609,49700,13021,24892,78565,20106,46058,85236,1390,92286,77281,44077,93910,83647,70617,42941,32179,597,87379,25241,5567,7007,86743,17157,85394,11838,69234,61406,20117,45204,15956,6e4,18743,92423,97118,96338,19565,41430,1758,75379,40419,21585,66674,36806,84962,85207,45155,14938,19476,7246,43667,94543,59047,90033,20826,69541,94864,31994,36168,10851,34888,81553,1540,35456,5014,51176,98086,24826,45240,28404,44999,8896,39094,73407,35441,31880,33185,16232,41941,50949,89435,48581,88695,41994,37548,73043,80951,406,96382,70774,20151,23387,25016,25298,94624,61171,79752,49140,71961,28296,69861,2591,74852,20539,387,59579,18633,32537,98145,6571,31010,24674,5455,61427,77938,91936,74029,43902,77557,32270,97790,17119,52527,58021,80814,51748,54178,45611,80993,37143,5335,12969,56127,19255,36040,90324,11664,49883,52079,84827,59381,71539,9973,33440,88461,23356,48324,77928,31249,64710,2295,36870,32307,57546,15020,9994,69074,94138,87637,91976,35584,4401,10518,21615,1848,76938,9188,20097,32825,39527,4220,86304,83389,87374,64278,58044,90045,85497,51981,50654,94938,81997,91870,76150,68476,64659,73189,50207,47677,26269,62290,64464,27124,67018,41361,82760,75768,76490,20971,87749,90429,12272,95375,5871,93823,43178,54016,44056,66281,31003,682,27398,20714,53295,7706,17813,8358,69910,78542,42785,13661,58873,4618,97553,31223,8420,28306,3264,81333,10591,40510,7893,32604,60475,94119,1840,53840,86233,81594,13628,51215,90290,28466,68795,77762,20791,91757,53741,61613,62269,50263,90212,55781,76514,83483,47055,89415,92694,397,58391,12607,17646,48949,72306,94541,37408,77513,3820,86864,29901,68414,82774,51908,13980,72893,55507,19502,37174,69979,20288,55210,29773,74287,75251,65344,67415,21818,59313,93278,81757,5686,73156,7082,85046,31853,38452,51474,66499,68107,23621,94049,91345,42836,9191,8007,45449,99559,68331,62535,24170,69777,12830,74819,78142,43860,72834,33713,48007,93584,72869,51926,64721,58303,29822,93174,93972,85274,86893,11303,22970,28834,34137,73515,90400,71148,43643,84133,89640,44035,52166,73852,70091,61222,60561,62327,18423,56732,16234,17395,96131,10123,91622,85496,57560,81604,18880,65138,56806,87648,85261,34313,65861,45875,21069,85644,47277,38001,2176,81719,11711,71602,92937,74219,64049,65584,49698,37402,96397,1304,77586,56271,10086,47324,62605,40030,37438,97125,40348,87083,31417,21815,39250,75237,62047,15501,29578,21826,41134,47143,34072,64638,85902,49139,6441,3856,54552,73135,42742,95719,9035,85794,74296,8789,88156,64691,19202,7638,77929,3061,18072,96207,44156,23821,99538,4713,66994,60528,83441,7954,19814,59175,20695,5533,52139,61212,6455,83596,35655,6958,92983,5128,9719,77433,53783,92301,50498,10850,62746,99599,10507,13499,6319,53075,71839,6410,19362,39820,98952,43622,63147,64421,80814,43800,9351,31024,73167,59580,6478,75569,78800,88835,54486,23768,6156,4111,8408,38508,7341,23793,48763,90822,97022,17719,4207,95954,49953,30692,70668,94688,16127,56196,80091,82067,63400,5462,69200,65443,95659,18288,27437,49632,24041,8337,65676,96299,90836,27267,50264,13192,72294,7477,44606,17985,48911,97341,30358,91307,6991,19072,24210,36699,53728,28825,35793,28976,66252,68434,94688,84473,13622,62126,98408,12843,82590,9815,93146,48908,15877,54745,24591,35700,4754,83824,52692,54130,55160,6913,45197,42672,78601,11883,9528,63011,98901,14974,40344,10455,16019,14210,33712,91342,37821,88325,80851,43667,70883,12883,97343,65027,61184,4285,1392,17974,15077,90712,26769,21778,30976,38807,36961,31649,42096,63281,2023,8816,47449,19523,59515,65122,59659,86283,68258,69572,13798,16435,91529,67245,52670,35583,16563,79246,86686,76463,34222,26655,90802,60584,47377,7500,37992,45134,26529,26760,83637,41326,44344,53853,41377,36066,94850,58838,73859,49364,73331,96240,43642,24637,38736,74384,89342,52623,7992,12369,18601,3742,83873,83080,12451,38992,22815,7759,51777,97377,27585,51972,37867,16444,24334,36151,99073,27493,70939,85130,32552,54846,54759,60790,18157,57178,65762,11161,78576,45819,52979,65130,4860,3991,10461,93716,16894,66083,24653,84609,58232,88618,19161,38555,95554,32886,59780,8355,60860,29735,47762,71299,23853,17546,73704,92052,46215,55121,29281,59076,7936,27954,58909,32643,52861,95819,6831,911,98936,76355,93779,80863,514,69572,68777,39510,35905,14060,40619,29549,69616,33564,60780,24122,66591,27699,6494,14845,46672,61958,77100,90899,75754,61196,30231,92962,61773,41839,55382,17267,70943,78038,70267,30532,21704,10274,12202,39685,23309,10061,68829,55986,66485,3788,97599,75867,20717,74416,53166,35208,33374,87539,8823,48228,63379,85783,47619,53152,67433,35663,52972,16818,60311,60365,94653,35075,33949,42614,29297,1918,28316,98953,73231,83799,42402,56623,34442,34994,41374,70071,14736,9958,18065,32960,7405,36409,83232,99385,41600,11133,7586,15917,6253,19322,53845,57620,52606,66497,68646,78138,66559,19640,99413,11220,94747,7399,37408,48509,23929,27482,45476,85244,35159,31751,57260,68980,5339,15470,48355,88651,22596,3152,19121,88492,99382,14454,4504,20094,98977,74843,93413,22109,78508,30934,47744,7481,83828,73788,6533,28597,20405,94205,20380,22888,48893,27499,98748,60530,45128,74022,84617,82037,10268,78212,16993,35902,91386,44372,15486,65741,14014,87481,37220,41849,84547,46850,52326,34677,58300,74910,64345,19325,81549,46352,33049,69248,93460,45305,7521,61318,31855,14413,70951,11087,96294,14013,31792,59747,67277,76503,34513,39663,77544,52701,8337,56303,87315,16520,69676,11654,99893,2181,68161,57275,36898,81304,48585,68652,27376,92852,55866,88448,3584,20857,73156,70284,24326,79375,95220,1159,63267,10622,48391,15633,84924,90415,93614,33521,26665,55823,47641,86225,31704,92694,48297,39904,2115,59589,49067,66821,41575,49767,4037,77613,19019,88152,80,20554,91409,96277,48257,50816,97616,38688,32486,45134,63545,59404,72059,43947,51680,43852,59693,25163,1889,70014,15021,41290,67312,71857,15957,68971,11403,65251,7629,37239,33295,5870,1119,92784,26340,18477,65622,36815,43625,18637,37509,82444,99005,4921,73701,14707,93997,64397,11692,5327,82162,20247,81759,45197,25332,83745,22567,4515,25624,95096,67946,48460,85558,15191,18782,16930,33361,83761,60873,43253,84145,60833,25983,1291,41349,20368,7126,14387,6345,80854,9279,43529,6318,38384,74761,41196,37480,51321,92246,80088,77074,88722,56736,66164,49431,66919,31678,72472,8,80890,18002,94813,31900,54155,83436,35352,54131,5466,55306,93128,18464,74457,90561,72848,11834,79982,68416,39528,72484,82474,25593,48545,35247,18619,13674,18611,19241,81616,18711,53342,44276,75122,11724,74627,73707,58319,15997,7586,16120,82641,22820,92904,13141,32392,19763,61199,67940,90767,4235,13574,17200,69902,63742,78464,22501,18627,90872,40188,28193,29593,88627,94972,11598,62095,36787,441,58997,34414,82157,86887,55087,19152,23,12302,80783,32624,68691,63439,75363,44989,16822,36024,867,76378,41605,65961,73488,67049,9070,93399,45547,94458,74284,5041,49807,20288,34060,79495,4146,52162,90286,54158,34243,46978,35482,59362,95938,91704,30552,4737,21031,75051,93029,47665,64382,99782,93478,94015,46874,32444,48277,59820,96163,64654,25843,41145,42820,74108,88222,88570,74015,25704,91035,1755,14750,48968,38603,62880,87873,95160,59221,22304,90314,72877,17334,39283,4149,11748,12102,80580,41867,17710,59621,6554,7850,73950,79552,17944,5600,60478,3343,25852,58905,57216,39618,49856,99326,66067,42792,95043,52680,46780,56487,9971,59481,37006,22186,54244,91030,45547,70818,59849,96169,61459,21647,87417,17198,30945,57589,31732,57260,47670,7654,46376,25366,94746,49580,69170,37403,86995,90307,94304,71803,26825,5511,12459,91314,8345,88975,35841,85771,8105,59987,87112,21476,14713,71181,27767,43584,85301,88977,29490,69714,73035,41207,74699,9310,13025,14338,54066,15243,47724,66733,47431,43905,31048,56699,80217,36292,98525,24335,24432,24896,43277,58874,11466,16082,10875,62004,90391,61105,57411,6368,53856,30743,8670,84741,54127,57326,26629,19087,24472,88779,30540,27886,61732,75454,60311,42824,37301,42678,45990,43242,17374,52003,70707,70214,49739,71484,92003,98086,76668,73209,59202,11973,2902,33250,78626,51594,16453,94614,39014,97066,83012,9832,25571,77628,66692,13986,99837,582,81232,44987,9504,96412,90193,79568,44071,28091,7362,97703,76447,42537,98524,97831,65704,9514,41468,85149,49554,17994,14924,39650,95294,556,70481,6905,94559,37559,49678,53119,70312,5682,66986,34099,74474,20740,41615,70360,64114,58660,90850,64618,80620,51790,11436,38072,50273,93113,41794,86861,24781,89683,55411,85667,77535,99892,41396,80504,90670,8289,40902,5069,95083,6783,28102,57816,25807,24260,71529,78920,72682,7385,90726,57166,98884,8583,6170,97965,88302,98041,21443,41808,68984,83620,89747,98882,60808,54444,74412,81105,1176,28838,36421,16489,18059,51061,80940,44893,10408,36222,80582,71944,92638,40333,67054,16067,19516,90120,46759,71643,13177,55292,21036,82808,77501,97427,49386,54480,23604,23554,21785,41101,91178,10174,29420,90438,6312,88940,15995,69321,47458,64809,98189,81851,29651,84215,60942,307,11897,92674,40405,68032,96717,54244,10701,41393,92329,98932,78284,46347,71209,92061,39448,93136,25722,8564,77936,63574,31384,51924,85561,29671,58137,17820,22751,36518,38101,77756,11657,13897,95889,57067,47648,13885,70669,93406,39641,69457,91339,22502,92613,89719,11947,56203,19324,20504,84054,40455,99396,63680,67667,60631,69181,96845,38525,11600,47468,3577,57649,63266,24700,71594,14004,23153,69249,5747,43321,31370,28977,23896,76479,68562,62342,7589,8899,5985,64281,61826,18555,64937,13173,33365,78851,16499,87064,13075,66847,70495,32350,2985,86716,38746,26313,77463,55387,72681,72461,33230,21529,53424,92581,2262,78438,66276,18396,73538,21032,91050,13058,16218,12470,56500,15292,76139,59526,52113,95362,67011,6651,16136,1016,857,55018,56374,35824,71708,49712,97380,10404,55452,34030,60726,75211,10271,36633,68424,58275,61764,97586,54716,50259,46345,87195,46092,26787,60939,89514,11788,68224,23417,73959,76145,30342,40277,11049,72049,15472,50669,48139,36732,46874,37088,73465,9819,58869,35220,12120,86124,51247,44302,60883,52109,21437,36786,49226,77837,19612,78430,11661,94770,77603,65669,86868,12665,30012,75989,39141,77400,28e3,64238,73258,71794,31340,26256,66453,37016,64756,80457,8747,12836,3469,50678,3274,43423,66677,82556,92901,51878,56441,22998,29718,38447,6453,25311,7565,53771,3551,90070,9483,94050,45938,18135,36908,43321,11073,51803,98884,66209,6830,53656,14663,56346,71430,4909,19818,5707,27369,86882,53473,7541,53633,70863,3748,12822,19360,49088,59066,75974,63335,20483,43514,37481,58278,26967,49325,43951,91647,93783,64169,49022,98588,9495,49829,59068,38831,4838,83605,92419,39542,7772,71568,75673,35185,89759,44901,74291,24895,88530,70774,35439,46758,70472,70207,92675,91623,61275,35720,26556,95596,20094,73750,85788,34264,1703,46833,65248,14141,53410,38649,6343,57256,61342,72709,75318,90379,37562,27416,75670,92176,72535,93119,56077,6886,18244,92344,31374,82071,7429,81007,47749,40744,56974,23336,88821,53841,10536,21445,82793,24831,93241,14199,76268,70883,68002,3829,17443,72513,76400,52225,92348,62308,98481,29744,33165,33141,61020,71479,45027,76160,57411,13780,13632,52308,77762,88874,33697,83210,51466,9088,50395,26743,5306,21706,70001,99439,80767,68749,95148,94897,78636,96750,9024,94538,91143,96693,61886,5184,75763,47075,88158,5313,53439,14908,8830,60096,21551,13651,62546,96892,25240,47511,58483,87342,78818,7855,39269,566,21220,292,24069,25072,29519,52548,54091,21282,21296,50958,17695,58072,68990,60329,95955,71586,63417,35947,67807,57621,64547,46850,37981,38527,9037,64756,3324,4986,83666,9282,25844,79139,78435,35428,43561,69799,63314,12991,93516,23394,94206,93432,37836,94919,26846,2555,74410,94915,48199,5280,37470,93622,4345,15092,19510,18094,16613,78234,50001,95491,97976,38306,32192,82639,54624,72434,92606,23191,74693,78521,104,18248,75583,90326,50785,54034,66251,35774,14692,96345,44579,85932,44053,75704,20840,86583,83944,52456,73766,77963,31151,32364,91691,47357,40338,23435,24065,8458,95366,7520,11294,23238,1748,41690,67328,54814,37777,10057,42332,38423,2309,70703,85736,46148,14258,29236,12152,5088,65825,2463,65533,21199,60555,33928,1817,7396,89215,30722,22102,15880,92261,17292,88190,61781,48898,92525,21283,88581,60098,71926,819,59144,224,30570,90194,18329,6999,26857,19238,64425,28108,16554,16016,42,83229,10333,36168,65617,94834,79782,23924,49440,30432,81077,31543,95216,64865,13658,51081,35337,74538,44553,64672,90960,41849,93865,44608,93176,34851,5249,29329,19715,94082,14738,86667,43708,66354,93692,25527,56463,99380,38793,85774,19056,13939,46062,27647,66146,63210,96296,33121,54196,34108,75814,85986,71171,15102,28992,63165,98380,36269,60014,7201,62448,46385,42175,88350,46182,49126,52567,64350,16315,53969,80395,81114,54358,64578,47269,15747,78498,90830,25955,99236,43286,91064,99969,95144,64424,77377,49553,24241,8150,89535,8703,91041,77323,81079,45127,93686,32151,7075,83155,10252,73100,88618,23891,87418,45417,20268,11314,50363,26860,27799,49416,83534,19187,8059,76677,2110,12364,71210,87052,50241,90785,97889,81399,58130,64439,5614,59467,58309,87834,57213,37510,33689,1259,62486,56320,46265,73452,17619,56421,40725,23439,41701,93223,41682,45026,47505,27635,56293,91700,4391,67317,89604,73020,69853,61517,51207,86040,2596,1655,9918,45161,222,54577,74821,47335,8582,52403,94255,26351,46527,68224,90183,85057,72310,34963,83462,49465,46581,61499,4844,94626,2963,41482,83879,44942,63915,94365,92560,12363,30246,2086,75036,88620,91088,67691,67762,34261,8769,91830,23313,18256,28850,37639,92748,57791,71328,37110,66538,39318,15626,44324,82827,8782,65960,58167,1305,83950,45424,72453,19444,68219,64733,94088,62006,89985,36936,61630,97966,76537,46467,30942,7479,67971,14558,22458,35148,1929,17165,12037,74558,16250,71750,55546,29693,94984,37782,41659,39098,23982,29899,71594,77979,54477,13764,17315,72893,32031,39608,75992,73445,1317,50525,87313,45191,30214,19769,90043,93478,58044,6949,31176,88370,50274,83987,45316,38551,79418,14322,91065,7841,36130,86602,10659,40859,964,71577,85447,61079,96910,72906,7361,84338,34114,52096,66715,51091,86219,81115,49625,48799,89485,24855,13684,68433,70595,70102,71712,88559,92476,32903,68009,58417,87962,11787,16644,72964,29776,63075,13270,84758,49560,10317,28778,23006,31036,84906,81488,17340,74154,42801,27917,89792,62604,62234,13124,76471,51667,37589,87147,24743,48023,6325,79794,35889,13255,4925,99004,70322,60832,76636,56907,56534,72615,46288,36788,93196,68656,66492,35933,52293,47953,95495,95304,50009,83464,28608,38074,74083,9337,7965,65047,36871,59015,21769,30398,44855,1020,80680,59328,8712,48190,45332,27284,31287,66011,9376,86379,74508,33579,77114,92955,23085,92824,3054,25242,16322,48498,9938,44420,13484,52319,58875,2012,88591,52500,95795,41800,95363,54142,17482,32705,60564,12505,40954,46174,64130,63026,96712,79883,39225,52653,69549,36693,59822,22684,31661,88298,15489,16030,42480,15372,38781,71995,77438,91161,10192,7839,62735,99218,25624,2547,27445,69187,55749,32322,15504,73298,51108,48717,92926,75705,89787,96114,99902,37749,96305,12829,70474,838,50385,91711,80370,56504,56857,80906,9018,76569,61072,48568,36491,22587,44363,39592,61546,90181,37348,41665,41339,62106,44203,6732,76111,79840,67999,32231,76869,58652,49983,1669,27464,79553,52855,25988,18087,38052,17529,13607,657,76173,43357,77334,24140,53860,2906,89863,44651,55715,26203,65933,51087,98234,40625,45545,63563,89148,82581,4110,66683,99001,9796,47349,65003,66524,81970,71262,14479,31300,8681,58068,44115,40064,77879,23965,69019,73985,19453,26225,97543,37044,7494,85778,35345,61115,92498,49737,64599,7158,82763,25072,38478,57782,75291,62155,52056,4786,11585,71251,25572,79771,93328,66927,54069,58752,26624,50463,77361,29991,96526,2820,91659,12818,96356,49499,1507,40223,9171,83642,21057,2677,9367,38097,16100,19355,6120,15378,56559,69167,30235,6767,66323,78294,14916,19124,88044,16673,66102,86018,29406,75415,22038,27056,26906,25867,14751,92380,30434,44114,6026,79553,55091,95385,41212,37882,46864,54717,97038,53805,64150,70915,63127,63695,41288,38192,72437,75075,18570,52065,8853,30104,79937,66913,53200,84570,78079,28970,53859,37632,80274,35240,32960,74859,7359,55176,3930,38984,35151,82576,82805,94031,12779,90879,24109,25367,77861,9541,85739,69023,64971,99321,7521,95909,43897,71724,92581,5471,64337,98949,3606,78236,78985,29212,57369,34857,67757,58019,58872,96526,28749,56592,37871,72905,70198,57319,54116,47014,18285,33692,72111,60958,96848,17893,40993,50445,14186,76877,87867,50335,9513,44346,26439,55293,6449,44301,63740,40158,72703,88321,85062,57345,66231,15409,3451,95261,43561,15673,28956,90303,62469,82517,43035,36850,15592,64098,59022,31752,4370,50486,11885,23085,41712,80692,48492,16495,99721,36912,28267,27882,16269,64483,11273,2680,1616,46138,54606,14761,5134,45144,63213,49666,27441,86989,29884,54334,6740,8368,80051,81020,17882,74973,74531,94994,24927,64894,22667,20466,82948,66831,47427,76033,31197,59817,20064,61135,28556,29695,80179,74058,18293,9963,35278,13062,83094,23373,90287,33477,48865,30348,70174,11468,25994,25343,22317,1587,30682,1,67814,59557,23362,13746,82244,42093,24671,79458,93730,45488,60234,67098,9899,25775,332,36636,57594,19958,85564,58977,12247,60774,66371,69442,20385,14486,91330,50332,46023,75768,59877,60081,92936,72302,75064,85727,52987,5750,19384,33684,78859,80458,69902,34870,88684,49762,40801,86291,18194,90366,82639,53844,96326,65728,48563,26027,52692,62406,76294,41848,63010,69841,29451,36170,21529,16525,64326,22086,24469,57407,96033,37771,31002,18311,93285,31948,14331,58335,15977,80336,81667,27286,24361,61638,57580,95270,46180,76990,53031,94366,2727,49944,19278,5756,51875,53445,33342,1965,7937,10054,97712,87693,58124,46064,39133,77385,9605,65359,70113,90563,86637,94282,12025,31926,24541,23854,58407,32131,92845,20714,27898,26917,50326,35145,50859,72119,95094,29441,42301,62460,75252,94267,38422,73047,24200,85349,72049,91723,97802,98496,12734,73432,10371,57213,53300,80847,46229,7099,72961,13767,65654,31102,82119,96946,65919,81083,3819,57888,57908,16849,77111,41429,92261,45263,1172,55926,78835,27697,48420,58865,41207,21406,8582,10785,36233,12237,7866,13706,92551,11021,63813,71512,65206,37768,94325,14721,20990,54235,71986,5345,56239,52028,1419,7215,55067,11669,21738,66605,69621,69827,8537,18638,60982,28151,98885,76431,25566,3085,23639,30849,63986,73287,26201,36174,14106,54102,57041,16141,64174,3591,90024,73332,31254,17288,59809,25061,51612,47951,16570,43330,79213,11354,55585,19646,99246,37564,32660,20632,21124,60597,69315,31312,57741,85108,21615,24365,27684,16124,33888,14966,35303,69921,15795,4020,67672,86816,63027,84470,45605,44887,26222,79888,58982,22466,98844,48353,60666,58256,31140,93507,69561,6256,88526,18655,865,75247,264,65957,98261,72706,36396,46065,85700,32121,99975,73627,78812,89638,86602,96758,65099,52777,46792,13790,55240,52002,10313,91933,71231,10053,78416,54563,96004,42215,30094,45958,48437,49591,50483,13422,69108,59952,27896,40450,79327,31962,46456,39260,51479,61882,48181,50691,64709,32902,10676,12083,35771,79656,56667,76783,3937,99859,10362,57411,40986,35045,2838,29255,64230,84418,34988,77644,39892,77327,74129,53444,35487,95803,38640,20383,55402,25793,14213,87082,42837,95030,97198,61608,97723,79390,35290,34683,81419,87133,70447,53127,97146,28299,56763,12868,1145,12147,58158,92124,60934,18414,97510,7056,54488,20719,53743,91037,44797,52110,8512,18991,20129,31441,51449,14661,71126,23180,68124,18807,70997,21913,19594,70355,73637,68266,60775,43164,52643,96363,77989,79332,39890,65379,20405,52935,43816,92740,95319,4538,60660,28982,15328,80475,34690,2293,19646,46524,96627,33159,42081,8816,74931,20674,8697,66169,46460,46326,39923,60625,28386,22919,19415,75766,43668,31626,70301,67053,3949,70082,2303,48642,38429,94053,38770,68137,68441,52928,70244,91954,17401,92693,98342,21451,84988,80487,33807,73797,49494,41878,76635,83227,76618,11946,13451,87591,78381,21407,90038,72638,69692,51599,86413,32019,64856,74730,41531,11064,1790,58817,86400,66213,92599,70905,78324,54326,43659,34206,63132,38837,40210,96346,16967,81619,96503,14881,89405,32205,49508,98425,2451,35423,56072,36810,30332,85998,49358,92748,84147,79835,94867,41224,61794,35066,82220,66684,20096,2754,41731,37068,32753,91059,13407,5607,69384,53329,95909,44968,11397,92973,50014,92997,80968,93761,57598,74703,7768,37978,73873,33475,9720,97852,98449,48722,84977,11271,11728,68318,22312,78792,87508,88466,72976,47099,84126,38595,85124,64405,90020,7492,52413,95111,34455,86311,68892,1074,60274,28136,19328,38161,57475,13771,63562,84207,94121,18901,52768,33801,82087,86091,59969,90398,56870,55756,78841,98450,54165,55106,50343,70519,14567,36780,55450,19606,83749,67562,64765,38543,16585,86841,73742,8766,39252,75678,75379,78760,37279,15280,13558,95916,89759,76686,76467,67147,63110,94008,8037,35263,53710,16667,79008,11231,29397,67136,18601,64502,90228,89109,72849,22711,65547,34542,26686,81678,87765,77654,23664,96352,14106,32938,28083,18633,80286,65507,46197,52722,75476,77816,47204,34876,45963,79262,90181,84041,3745,90041,30780,27226,92847,85572,15308,80688,5761,82638,13464,23683,81015,54214,64175,43701,86845,15569,50687,52679,87696,8285,97444,47599,94472,64150,87753,68652,60726,26213,17320,64553,81285,98126,12158,52095,64833,492,35817,55571,91300,97812,37507,4209,53515,64342,21223,16662,43265,68219,3529,43636,68417,53640,95326,93381,37113,80751,76469,96677,43054,22937,31954,13266,34140,27253,2734,99070,60077,57988,93211,92795,83795,57477,3941,39007,14619,38320,93449,31336,25279,97030,26245,47394,39475,90621,23820,29344,94859,91604,14033,41868,14816,4075,66644,87803,97815,99552,78666,3942,8175,22345,19983,76783,99044,20851,84981,59052,77178,72109,76475,21619,73017,6812,56633,50612,55289,4671,84419,94072,94446,80603,32188,93415,23464,43947,43728,74284,67177,57105,31059,10642,13803,69602,46961,66567,19359,84676,63918,40650,12923,15974,79732,20225,92525,71179,4859,91208,60430,5239,61458,24089,68852,60171,29603,42535,86365,93905,28237,45317,60718,82001,41679,20679,56304,70043,87568,21386,59049,78353,48696,77379,55309,23780,28391,5940,55583,81256,59418,97521,32846,70761,90115,45325,5490,65974,11186,15357,3568,450,96644,58976,36211,88240,92457,89200,94696,11370,91157,48487,59501,56983,89795,42789,69758,79701,29511,55968,41472,89474,84344,80517,7485,97523,17264,82840,59556,37119,30985,48866,60605,95719,70417,59083,95137,76538,44155,67286,57897,28262,4052,919,86207,79932,44236,10089,44373,65670,44285,6903,20834,49701,95735,21149,3425,17594,31427,14262,32252,68540,39427,44026,47257,45055,95091,8367,28381,57375,41562,83883,27715,10122,67745,46497,28626,87297,36568,39483,11385,63292,92305,78683,6146,81905,15038,38338,51206,65749,34119,71516,74068,51094,6665,91884,66762,11428,70908,21506,480,94183,78484,66507,75901,25728,52539,86806,69944,65036,27882,2530,4918,74351,65737,89178,8791,39342,94963,22581,56917,17541,83578,75376,65202,30935,79270,91986,99286,45236,44720,81915,70881,45886,43213,49789,97081,16075,20517,69980,25310,91953,1759,67635,88933,54558,18395,73375,62251,58871,9870,70538,48936,7757,90374,56631,88862,30487,38794,36079,32712,11130,55451,25137,38785,83558,31960,69473,45950,18225,9871,88502,75179,11551,75664,74321,67351,27703,83717,18913,42470,8816,37627,14288,62831,44047,67612,72738,26995,50933,63758,50003,43693,52661,55852,52372,59042,37595,4931,73622,68387,86478,40997,5245,75300,24902,59609,35653,15970,37681,69365,22236,86374,65550,343,98377,35354,65770,15365,41422,71356,16630,40044,19290,66449,53629,79452,71674,30260,97303,6487,62789,13005,70152,22501,49867,89294,59232,31776,54919,99851,5438,1096,72269,50486,16719,6144,82041,38332,64452,31840,99287,59928,25503,8407,46970,45907,99238,74547,19704,72035,26542,54600,79172,58779,35747,78956,11478,41195,58135,63856,33037,45753,60159,25193,71838,7526,7985,60714,88627,75790,38454,96110,39237,19792,34534,70169,24805,63215,38175,38784,38855,24826,50917,25147,17082,26997,32295,10894,21805,65245,85407,37926,69214,38579,84721,23544,88548,65626,75517,69737,55626,52175,21697,19453,16908,82841,24060,40285,19195,80281,89322,15232,70043,60691,86370,91949,19017,83846,77869,14321,95102,87073,71467,31305,64677,80358,52629,79419,22359,87867,48296,50141,46807,82184,95812,84665,74511,59914,4146,90417,58508,62875,17630,21868,9199,30322,33352,43374,25473,4119,63086,14147,14863,38020,44757,98628,57916,22199,11865,42911,62651,78290,9392,77294,63168,21043,17409,13786,27475,75979,89668,43596,74316,84489,54941,95992,45445,41059,55142,15214,42903,16799,88254,95984,48575,77822,21067,57238,35352,96779,89564,23797,99937,46379,27119,16060,30302,95327,12849,38111,97090,7598,78473,63079,18570,72803,70040,91385,96436,96263,17368,56188,85999,50026,36050,73736,13351,48321,28357,51718,65636,72903,21584,21060,39829,15564,4716,14594,22363,97639,65937,17802,31535,42767,98761,30987,57657,33398,63053,25926,20944,19306,81727,2695,97479,79172,72764,66446,78864,12698,15812,97209,38827,91016,91281,57875,45228,49211,69755,99224,43999,62879,8879,80015,74396,57146,64665,31159,6980,79069,37409,75037,69977,85919,42826,6974,61063,97640,13433,92528,91311,8440,38840,22362,93929,1836,36590,75052,89475,15437,65648,99012,70236,12307,83585,414,62851,48787,28447,21702,57033,29633,44760,34165,27548,37516,24343,63046,2081,20378,19510,42226,97134,68739,32982,56455,53129,77693,25022,55534,99375,30086,98001,7432,67126,76656,29347,28492,43108,64736,32278,84816,80440,30461,818,9136,1952,48442,91058,92590,10443,5195,34009,32141,62209,43740,54102,76895,98172,31583,4155,66492,58981,16591,11331,6838,3818,77063,12523,45570,68970,70055,77751,73743,71732,4704,61384,57343,66682,44500,89745,10436,67202,36455,42467,88801,91280,1056,27534,81619,79004,25824,66362,33280,20706,31929,57422,18730,96197,22101,47592,2180,18287,82310,60430,59627,26471,7794,60475,76713,45427,89654,14370,81674,41246,98416,8669,48883,77154,9806,94015,60347,20027,8405,33150,27368,53375,70171,59431,14534,34018,85665,77797,17944,49602,74391,48830,55029,10371,94261,16658,68400,44148,28150,40364,90913,73151,64463,50058,78191,84439,82478,62398,3113,17578,12830,6571,95934,9132,25287,78731,80683,67207,76597,42096,34934,76609,52553,47508,71561,8038,83011,72577,95790,40076,20292,32138,61197,95476,23123,26648,13611,48452,39963,85857,4855,27029,1542,72443,53688,82635,56264,7977,23090,93553,65434,12124,91087,87800,95675,99419,44659,30382,55263,82514,86800,16781,65977,65946,13033,93895,4056,75895,47878,91309,51233,81409,46773,69135,56906,84493,34530,84534,38312,54574,92933,77341,20839,36126,1143,35356,35459,7959,98335,53266,36146,78047,50607,22486,63308,8996,96056,39085,26567,6779,62663,30523,47881,41279,49864,82248,78333,29466,48151,41957,93235,53308,22682,90722,54478,7235,34306,15827,20121,96837,6283,80172,66109,92592,48238,76428,94546,45430,16288,74839,740,25553,83767,35900,5998,7493,46755,11449,88824,44906,33143,7454,56652,34755,63992,59674,65131,46358,12799,96988,51158,73176,1184,49925,63519,11785,29073,72850,47997,75172,55187,15313,40725,33225,56643,10465,38583,86440,97967,26401,17078,38765,33454,19136,57712,48446,98790,27315,71074,10157,57946,35582,49383,61324,26572,84503,3496,60449,17962,26017,65651,40400,83246,80056,75306,75147,41863,25581,87530,33193,43294,5065,99644,62771,75986,79005,44924,18703,40889,4403,5862,2571,82500,74200,36170,46836,74642,65471,26815,30937,64946,10160,15544,31962,54015,28853,66533,14573,79398,47391,73165,47805,77589,16881,13423,89452,76992,62509,9796,57540,13486,48855,25546,47589,21012,47388,78428,70196,84413,81026,87597,22445,83769,85937,38321,85485,87359,9839,67228,71179,94372,4446,62801,50775,96179,40646,44272,12417,47199,39701,30665,32775,66525,53558,78882,31939,67209,38906,34533,99914,27719,216,99225,96537,3843,90564,91110,51838,30300,9559,37795,94880,11325,44979,89696,28129,29931,89971,46292,92710,11036,74760,75307,12291,49618,16293,92408,67928,80823,32872,25460,66819,35374,4035,99087,61129,11341,39118,10891,37217,63638,75477,30068,42334,57570,6890,59353,89939,37692,15232,20033,32202,22348,2766,96791,58448,92248,5769,96684,67885,99295,47271,38655,59513,96960,31718,8974,16122,20535,52380,29769,70660,57425,50891,75044,84257,73315,38181,28673,93140,26307,82265,78382,19681,56585,8975,76764,39956,83450,84663,89963,71584,57696,30829,60527,64947,34899,28805,28397,91830,51842,99838,39839,66971,67177,74219,35637,35634,93581,81746,29991,81096,94279,2968,62561,2479,82126,25702,67953,88088,50293,83423,86206,39935,23253,43041,48941,85787,8388,6671,43574,84908,67295,33623,55060,28174,48415,2529,22009,24524,5283,30460,32399,80423,56929,40852,69969,88541,5979,91496,64730,57198,83145,39750,3568,54669,98679,4297,51047,31492,47734,31343,31180,232,19707,24823,75079,73943,17997,8446,91252,39879,58682,82972,18417,39203,36681,42895,8459,15618,17941,52594,43277,16530,40052,91100,87422,47230,95699,49794,50492,87439,86354,4546,65333,11057,77727,19748,38722,91821,18107,42125,89239,28847,54623,38783,47803,31414,38450,3697,89186,30579,44188,26532,8420,80723,48100,60748,76330,45832,8311,16051,4475,13400,48527,46073,17439,56498,94632,9021,16871,83366,14896,4219,38375,87890,90217,42370,61028,85101,76771,83715,94737,69973,74187,1958,59691,86712,86570,60984,76342,13648,85250,28323,48379,45141,36277,51845,29039,3553,5128,59866,51281,68124,17007,24729,29710,41439,40574,11774,86746,89698,56020,37810,88972,11361,95583,70786,589,74473,87513,17690,61427,72914,32517,1804,97910,6327,30246,33049,2622,41026,80875,41293,16752,84225,84414,37137,68956,8095,64981,28180,38629,76962,23840,17477,75268,48297,70340,57888,13938,38554,86836,2195,30270,55484,53364,54705,41380,56316,37723,234,21424,26664,63804,75139,36534,18579,9833,98849,72762,59767,52497,24227,83152,71794,21398,99456,89215,51632,54799,27973,68568,68465,98500,28681,18369,24279,96335,12874,82160,67202,85199,27908,67022,49810,77929,96212,81153,77884,7032,1671,53362,28119,56786,30883,28540,76029,3774,64611,19736,25589,46569,45206,48215,69523,17423,91807,90039,30393,58319,85098,66519,57571,24541,3562,14400,62731,82534,61477,89731,18421,29861,52829,838,78040,43350,74323,82892,84746,28302,13264,7595,134,12933,46831,24864,47275,20527,9110,28485,30326,99826,64005,99308,65779,42760,90066,3974,38688,39968,32604,11694,46262,73262,45405,43923,67397,88228,56405,17839,92073,57622,93328,15442,50186,7570,58001,31e3,8915,11467,14793,82691,51238,12485,51745,18192,5985,36826,89434,38669,91592,88799,65621,67237,59541,19657,93402,58705,73553,78280,69125,95591,81168,91927,25976,89077,71690,19404,64603,59752,74698,44233,67602,38615,31303,28650,53700,89819,7783,4351,77451,47350,21234,16016,41532,76508,23063,44993,43983,33356,61715,96485,22121,78004,6316,87896,99289,93981,37850,66128,92735,45064,50924,24204,58816,65290,34392,55567,66416,72353,45775,68590,85685,72683,60090,37149,85347,57414,72336,12979,5720,92754,76911,96883,74420,5220,85815,23557,80567,44365,70254,50864,36619,51479,23281,76428,18580,34240,59289,49076,18439,29522,42541,4024,84446,92434,90407,77241,19690,78143,65919,13699,91844,91241,38361,67171,90551,5709,3474,76025,97043,33834,44638,54040,82797,545,38159,16089,35870,89158,55864,98078,50563,36492,10994,85909,9018,19252,73887,67928,60045,70782,11937,4074,53814,46621,52577,94853,45968,73667,65062,73306,76045,78649,91654,53958,96537,95542,67622,54579,17279,67440,56441,20681,64011,52226,96618,32831,60664,67547,39523,2043,59748,1887,69229,94653,99271,98164,62155,9234,47367,13047,6364,35064,10073,6793,80248,29009,44969,11129,17139,79630,89772,26921,56949,23465,30036,17173,82459,96218,60768,76417,24405,18710,68887,82394,69729,82503,40873,41590,67255,30757,9657,91881,34578,9511,5417,58953,18532,10721,22029,48524,47778,881,83489,3464,57462,97459,86689,39755,39547,740,36666,7993,31671,86304,12970,73402,52849,31652,79655,11250,18463,57518,20306,25301,1374,51208,33298,87662,61849,60923,68685,69411,39266,80320,34844,89416,81569,83651,35795,40168,33501,1042,58931,3892,85188,74740,85476,23790,33842,89565,53359,25579,59049,62394,72435,12457,21904,18370,97035,57905,9581,91227,92754,37760,1411,7440,87175,88318,63242,85960,56690,12618,30493,11569,73723,7448,58830,157,65814,21118,22140,73793,57855,81830,6795,13183,12625,30635,56429,73216,12342,36722,83886,96828,82870,90954,97614,2370,42160,73370,11944,49067,59452,80495,43911,46712,17033,68037,41963,3874,44856,82985,57453,84358,16120,4454,76624,405,62369,55080,61880,51270,87807,10653,36894,70850,35660,234,14705,93418,94084,82856,25384,71555,56754,78315,18291,91656,98079,52384,43306,65205,75903,58701,99496,50048,33557,87793,90857,10143,46726,84284,43635,41213,83845,70986,91408,80220,5728,68890,46577,21152,43759,43301,93661,97252,50106,10099,13722,18572,44024,351,18173,23717,85114,85998,57782,63951,53723,86853,63851,79430,49181,46386,69666,55743,76162,71724,40028,94786,34457,16906,90040,30789,40281,94697,96584,81907,4055,53990,66397,80579,42517,78181,39251,9467,67097,95523,66568,63632,71048,15581,39904,75774,77495,75994,29911,65690,41178,47712,70355,16998,56025,5230,10093,71495,34784,70950,54680,57811,53782,39145,36829,85342,40406,35883,45668,3459,29870,78252,70088,70621,67153,5737,40933,91075,93335,86853,15860,81167,91259,16118,52401,83593,84474,2423,75608,39646,90871,70284,82100,96032,5115,63678,2225,88087,58581,44364,57468,21539,13042,64150,63754,5210,87644,54114,64013,63562,41388,32397,74152,23982,71982,71700,33026,66477,47838,46712,39848,35083,65927,97868,11067,76771,71799,43836,41014,97025,93225,8511,63096,26628,73012,12543,76269,99708,2629,49845,73677,19193,14924,57236,95564,15010,59667,73773,78515,2624,99744,13585,33746,58771,94785,62628,99585,11363,80832,59979,9444,78700,2596,85984,69438,16913,96475,93283,18625,77086,45911,39746,64722,39938,43930,54619,302,50384,2738,75714,75249,95439,80714,52555,47266,96190,78750,94973,83669,16479,53163,48071,28e3,45011,26733,67132,83362,84162,43028,8415,27236,52651,89059,64844,80910,1676,91752,57815,26264,3415,57532,29981,61200,96036,62600,20068,56530,38487,8432,89514,26883,69165,97237,22361,55276,39902,95927,82190,49269,27212,46095,37106,64254,27460,49572,51700,27679,12574,33891,3867,9925,6476,82018,45094,59014,67113,44192,75,23318,79895,70550,81717,28833,30271,15821,14999,88174,62617,57517,55256,50281,51583,96879,5225,42272,5339,20483,57596,41011,75937,22767,50120,95938,49753,63882,99616,69083,38721,73889,80236,99531,23053,71237,48861,59046,76283,60538,19732,93877,30345,64882,66660,17026,70364,45676,8039,96228,89936,59141,95585,89552,97247,59325,27848,80058,15950,61481,90906,40998,44137,16144,66300,44091,50018,81364,18211,60294,76559,20279,27414,10589,39860,23e3,31767,95618,56738,50332,16936,70342,92481,30702,76264,62619,68678,62284,83112,93032,55203,52614,36950,41796,45403,79262,2887,53596,61308,20738,34811,27099,90956,65448,3080,75795,29753,97699,80872,23830,85882,74427,99523,74904,28017,45898,57232,48525,7086,26805,74533,92470,18840,76011,93109,14344,55614,50284,15865,19458,35856,13464,53679,64603,51571,56124,79107,29596,89572,78198,57121,73649,8804,87977,87959,70859,40909,77295,87877,75158,62810,92074,23244,59516,50552,31602,41899,6347,27821,68370,48596,88577,30231,25267,84622,31449,12086,56461,22962,78213,62483,93966,60437,52239,58113,32526,38708,81607,57016,1695,90110,4649,59990,23979,3855,10297,46516,96092,82305,30760,78756,4967,82876,4773,86651,16648,53133,82439,78851,49766,24553,15273,36417,1901,33386,76979,25920,33372,2695,11982,40911,6230,91696,43907,17827,30332,89203,32215,91806,23080,49102,9174,11548,54590,75803,66108,73882,62324,26017,72716,33887,1285,31604,71039,24337,53514,58964,89901,22040,92751,12617,37007,5523,61672,62557,98540,26094,60284,19621,96230,38044,6545,9458,42988,2913,86345,67936,90174,40840,44991,24256,34989,74086,13652,68706,1363,4294,88008,78693,83068,94746,221,89299,53186,5930,61889,51341,45412,58860,72568,11381,59785,36887,10690,31347,93326,96267,86987,57565,86836,49071,90331,41248,34629,30240,27270,3864,84308,3035,61369,36902,51017,44409,17120,23823,36460,63359,8333,63173,19134,6493,303,18550,26191,19051,81502,66343,6737,90430,65478,58982,82484,16483,47704,44640,68322,44548,72787,2335,28749,39320,5436,98146,56596,812,51445,35533,35478,47573,38414,25542,38032,13442,42983,97207,77854,57806,81616,52828,79429,47389,96795,57764,19605,24767,63253,18809,65093,44449,22952,76872,30983,38948,9310,48336,87651,27110,84427,76209,56412,12760,16747,14551,82626,31224,98636,75100,84882,79479,83420,5347,6803,90063,4617,40257,79183,41766,71873,25242,12275,336,40798,42055,74066,69128,32547,76508,32530,42359,89207,49758,58984,92732,15779,7234,28884,28226,50011,35883,99606,45423,76224,75427,85747,33879,97978,57441,927,19164,74716,40702,19715,70917,60344,40236,9019,50577,15598,53136,57285,20536,7539,74832,89184,41501,39447,97422,97041,21913,40581,76081,13089,28776,54164,55736,36263,71841,34488,74988,55467,43322,9214,36746,67981,71877,81683,32461,84091,19422,88366,62054,85664,13409,8003,88276,6989,16607,33633,85349,5784,25950,97998,74110,16699,60184,92818,79705,10381,1474,18656,50434,18232,92132,66537,70141,42854,25120,39581,28249,14215,34810,19767,3409,11807,6566,66138,42997,41999,67504,87117,28961,5e3,29673,77726,73225,54753,69712,71576,92337,17713,63185,87923,91889,68351,17712,75532,93849,48280,62219,317,25290,29209,90927,92929,92762,60413,2018,31793,76290,73373,80777,60819,77375,57886,47291,99670,32605,29064,99476,80999,31217,35,91300,14892,73653,26593,25305,56797,12837,39560,27582,37253,38531,76489,49946,69108,58687,43092,73807,96282,6648,67431,87124,57694,21660,64002,6,33600,30245,60636,80164,9285,61426,4658,54130,14710,76553,1904,93668,63110,98618,5601,32199,74923,98049,49717,55539,35940,58545,43295,35810,45451,38735,42065,66769,69825,45461,83881,67372,67351,90612,79502,69460,23108,74421,82990,46821,40683,71603,55267,48192,50242,79738,96417,6664,19929,23644,41116,51056,219,45086,32747,49492,15399,24874,80825,95928,61457,45813,59037,16136,3953,83583,5910,12654,53630,92997,22168,93491,71897,74579,24022,6278,24049,71670,43044,8474,38572,77402,35800,7455,96177,41653,74493,20802,65843,73050,73349,2638,65813,96209,49196,45007,32207,14097,66059,46681,7534,71263,20582,10171,51514,52142,60961,57951,25637,37860,21683,86190,90434,94481,85697,95344,2606,74095,61133,7472,64777,94050,41482,975,23471,76052,82021,87676,91345,20196,2612,86299,44996,40312,65712,46079,88514,8610,3685,63197,9073,53105,86824,28112,99306,40706,66840,83003,51590,52755,32285,68454,85058,13645,23073,24724,52989,71880,21952,44144,74975,76715,7844,46447,86643,75579,29276,10864,83179,36721,19300,35066,29383,47478,56644,33354,31414,17643,92374,85085,88458,87191,85248,34963,76278,53230,13953,76985,70959,36663,5293,32658,56767,56997,76736,6558,64248,11907,29123,78458,17678,63805,89973,5076,39263,54404,4355,64957,74407,99838,18836,78098,6490,74888,73719,80675,86178,56283,33591,96957,38382,18772,74773,71229,2603,52673,44609,14843,58418,18060,95459,626,30914,13550,42195,44863,8871,89182,64446,78422,41140,15312,98274,48168,95651,35562,85386,56252,72136,85088,68761,78434,98143,61330,2446,64409,49406,99127,98626,55095,44808,13594,87370,89472,12833,98932,68064,58193,20225,5192,28425,23978,24542,80845,55858,4015,21454,37346,51007,17202,10242,12682,55933,96922,22280,75597,50227,70712,44236,20470,36320,49339,60536,80083,38880,93327,49522,93585,9918,55268,4671,57526,11457,48424,54610,7211,78610,9473,72923,27347,30057,76968,26177,59367,46172,88951,40229,34921,60405,88959,16779,29547,92231,61997,36002,21080,39795,77221,10012,49748,76900,15964,3803,40260,92351,92844,10288,57483,10881,70408,75688,16610,1638,93082,44282,66849,75702,69428,34047,84968,71281,72328,73143,88672,49802,50639,18129,93659,58389,49095,45971,34196,84609,59222,19332,17777,41004,47057,30688,16039,20906,41477,42915,60877,33864,75195,62294,3371,11672,1370,2486,35553,17907,90621,45136,9722,67635,12114,63055,16004,21625,24321,20491,26881,66259,94287,54751,36242,36557,5842,30687,65418,94608,24741,45887,78800,86912,42076,50287,9284,68891,76368,83094,96302,35997,30761,97081,9501,68887,32876,1705,34260,95065,45528,88241,30402,12318,52430,40139,96986,84900,72408,42027,31676,54382,73370,26184,14024,57444,57660,52173,30274,93448,63273,77681,74946,2099,69091,19372,66961,14595,58642,75760,52253,53148,26074,52293,65359,63971,4833,86492,1227,54505,19515,89889,46933,13364,33883,83389,36952,52505,67513,40071,31001,3105,87912,29610,75108,37363,28479,43546,89992,19550,54863,82633,19209,21548,35022,21960,57961,11815,95867,559,26428,69386,57453,70147,73538,49562,46806,64550,36653,25718,68792,31113,7607,48037,71020,22666,65957,11141,39227,7990,19849,65972,74528,40888,55386,95918,92088,91125,53648,66122,138,79933,71058,34826,97725,69513,22915,18246,52244,91161,40861,40374,13239,56162,4703,95851,22824,41271,28202,62852,84238,46625,20031,8524,20077,65817,21174,29279,57712,22401,67500,30980,74485,26480,21343,30031,61921,35744,57308,71196,1865,49234,62616,54021,29008,83672,85839,96836,45077,80900,66906,63526,93824,71820,11033,20183,85704,4683,63512,39144,56880,64424,95979,17709,94849,31771,5737,84286,16757,46256,24478,73180,59978,8254,78963,95437,86351,33824,32540,18357,2668,99260,21284,81351,70961,10255,6911,47394,72408,23827,59865,96395,30665,43699,3593,29165,23388,26628,92402,16731,86740,29493,9069,78653,90094,42735,33682,95041,89887,92369,57949,81585,50593,14698,4737,72551,57271,59433,156,33966,58773,59108,49578,18100,59836,73221,21110,1650,11058,47770,66141,84576,58388,40915,94507,32209,17272,65674,95552,25685,5345,36995,36302,7971,67001,62062,75939,36005,26739,56484,46885,66348,87666,78055,44485,82955,85936,9219,1847,92687,72579,45457,78252,98239,4e4,75563,92408,17175,78845,32638,26959,35406,59553,57852,7506,9,93172,77713,93880,40981,27924,9678,24538,52426,84852,83781,23712,82490,77890,22482,66668,55850,25644,44972,62275,78089,28894,98685,32998,98766,89119,34355,75127,69797,71419,62067,57990,96514,50603,79807,26135,29207,43632,32905,38513,18924,88872,20758,70232,60425,1116,24077,21369,93541,75329,78656,44251,42014,98154,42552,14575,30765,348,1134,71581,68420,78141,21105,63305,9718,54851,65867,8595,47390,39182,51174,41478,64433,59628,31945,87322,78667,95282,5622,26224,19972,97269,98376,14779,51138,49658,45345,4972,52794,15737,496,48939,63485,42780,16061,59631,37171,13483,56058,51093,62290,88227,17400,88433,67363,89507,26482,85964,71336,67799,28342,37747,61722,27180,78755,18603,42953,6606,23875,56766,1932,36113,62807,84012,21103,9685,69662,76755,13701,95168,13169,44726,15284,16702,89617,54397,52052,12835,37741,86434,22400,37947,95763,86337,35189,22756,47473,16618,42479,47405,14055,64262,66670,89692,54032,94591,44149,29854,76691,33263,62048,25116,88598,16119,62116,54517,31883,86707,18895,81790,71294,2684,15292,48107,14341,91416,75609,92564,39987,2283,89970,95855,80970,5432,89860,90293,99851,94648,5598,32171,28793,92305,64244,8277,93391,96717,34464,29838,10664,28050,60122,77934,10758,84922,92220,45071,97697,36368,17792,84792,76594,67319,51886,5665,45201,11348,9254,7510,51039,91683,84500,85338,5555,19633,3870,39576,41486,58524,54508,20707,58504,39642,22454,80069,83455,31043,90794,51934,3295,26582,16300,74990,22197,83310,69642,81113,58558,84833,17105,46659,25003,85749,44829,4103,67516,76458,52392,53546,70291,98846,67315,30686,18555,29755,5923,22732,19501,56181,85351,5023,4808,56911,16793,75336,49712,27723,96974,34321,5454,12862,71924,45928,95697,68664,58183,78104,42483,71204,99628,40642,56410,17350,13396,76724,87509,9158,83708,27298,92651,95086,38851,63558,89810,1580,32518,35795,26514,56322,78635,63731,91428,7247,66460,38671,26799,22549,47991,46064,80467,40083,17141,39152,99872,27561,75389,74778,94893,82935,99076,93419,10474,84436,47536,16719,60136,80566,28404,74525,74212,3704,65516,98197,34210,64140,22238,49939,99542,27481,21992,78181,90060,71365,66935,29578,14961,8569,9454,43308,66753,45972,93572,16382,87320,37183,25478,38164,31997,69856,60898,63968,62264,4799,17591,89937,73905,55890,88285,2448,40398,54180,65869,45155,43407,39105,339,51619,20203,21189,68245,76912,1222,76411,82679,7,66047,32043,42627,16638,27019,15248,66444,8249,18790,82150,54084,84469,3426,50226,99868,88894,43769,66384,8593,41414,2976,60053,51866,87904,74135,53842,59520,67482,16995,32328,29555,49067,2799,68851,41049,97190,53984,99755,46412,45885,64e3,21962,36438,71742,57223,66599,86071,31436,32667,98099,38399,47377,5171,2742,48803,17823,22093,9866,691,5514,25546,2114,5919,56181,96052,67211,61712,25995,3188,23833,38549,44775,55355,61548,55988,47309,23749,30667,70732,33299,16127,30842,78961,41072,9876,18903,30292,25275,61881,15939,72573,84502,92654,97226,53434,77025,63892,12421,33644,39445,30933,84218,13757,37719,84450,2697,60309,22402,80310,92771,45205,72792,95776,85945,74651,216,50842,47854,21916,61588,75405,10495,83083,60427,78495,99809,47890,22993,21508,9459,26845,98130,1184,46438,27698,40652,65654,98517,1096,6998,49133,57041,77983,58708,42176,67356,324,70063,10597,65205,25622,34336,16640,27896,26907,86760,48244,89650,44997,51609,28934,9171,97859,97213,19859,41037,64081,94781,27683,41521,52871,86935,26486,38744,25943,60617,6414,42292,46204,53262,30201,38776,88831,97253,67282,72860,18452,60927,81504,57713,30296,10896,39900,67135,42772,4631,55283,39253,25264,1809,12874,88035,88421,90491,83290,6884,15444,90113,20406,20796,40239,34431,15018,45600,17241,26611,9551,89126,65673,31708,91252,39647,63011,24193,58932,89326,33491,53217,27976,70151,37531,53576,23931,11789,73073,52171,89301,51718,15385,79487,66436,35771,34163,86540,42665,80748,77622,14679,40185,25030,42622,13162,17048,24243,59985,59807,60562,3595,10135,29199,69784,59796,38194,58432,50943,40422,63035,3208,81440,90749,88046,32218,88092,22224,2627,91576,16781,43948,57795,71073,27817,87077,82717,24473,42096,76920,88864,90537,14715,42551,45066,24316,37361,38582,21871,14672,93362,21727,57021,94313,39562,64985,94028,46094,43845,91838,79574,7597,3153,56783,18817,74711,6883,91061,31674,73729,99315,66183,57647,74484,68077,33224,397,56753,53158,71872,68153,9298,20961,49656,33407,95683,14328,44708,72952,27048,67887,28741,46752,88177,95894,40086,88534,87112,68614,83073,88794,96799,67588,75049,84603,83140,97347,87316,73087,77135,71883,98643,3808,8848,14133,60447,1366,72976,1868,51667,63279,60040,88264,79152,3474,61366,20523,21584,93712,83654,89761,90154,96345,37539,32556,74254,70603,97122,44978,78028,8943,13778,11080,34271,68276,85372,48410,94516,15427,75323,71685,70774,50342,33771,3678,42321,69788,41758,55004,30992,17402,63523,42328,87171,24751,15084,33884,83655,88345,69602,52606,57886,18034,3381,75796,35901,77480,28683,68324,66035,7223,14926,16128,13645,90370,31949,11057,98849,29499,21565,30786,83292,92392,37104,36899,49906,79368,43710,80365,88735,75275,21664,57965,19002,301,12658,94385,1717,96191,50404,80166,93965,24688,27839,10812,31715,92127,42588,93307,80834,11317,26583,25769,98227,14884,58462,29148,68662,26872,72927,79021,51622,29521,33355,45701,45996,33782,93424,16530,96086,17329,74020,11501,46660,5583,22277,77653,55430,84644,448,86828,58855,67451,95264,67386,82424,52611,60012,88620,72894,94716,22262,99813,69592,63464,33163,91857,47904,22209,78590,68615,52952,31441,41313,18550,72685,68825,4795,53971,14592,39634,23682,76630,2731,81481,86542,23727,54291,56045,61635,32186,9355,73416,63532,24340,18886,84832,30654,48543,18339,65024,91197,64624,74648,9660,27897,49771,11123,8732,49393,12911,72416,17834,18878,62754,85072,23727,56577,51257,83291,12329,16203,91681,68137,79959,43609,58987,2026,42969,59144,84349,75214,76972,22633,64104,53799,16851,99197,70476,77113,46320,88693,37711,96536,68156,7119,2104,49435,77706,18924,24957,92406,87148,70482,36491,42605,54440,7893,31618,35707,65130,30007,75706,77266,37100,9601,87681,42543,69847,81848,32034,49429,99434,62209,17125,55227,61634,52574,83649,28725,70119,62467,80676,21192,99584,21310,25292,72781,17186,10393,98390,19789,92931,36234,62627,23437,3885,58822,82941,43806,8172,23790,72295,36196,98200,2889,87619,13846,56197,27151,21238,48794,81100,62643,40001,39243,33213,78416,194,91369,79342,36404,52308,13741,24442,88610,12659,11574,70052,93039,79367,41792,61816,35435,47192,97596,28330,41145,16918,62865,9576,45857,68737,90124,16703,7071,48433,57222,34435,800,72496,16449,68187,28739,97672,86818,50768,40807,88681,64340,2224,19703,59245,90905,31239,84216,93942,97371,16842,92168,52692,16064,84686,89444,27938,98406,41365,4515,20494,18813,16242,10634,61566,18592,78057,8720,33739,78345,87693,30242,70545,55521,23687,9160,8655,38811,61768,7228,5567,5561,82071,85,50145,23113,97761,88441,14891,72188,85166,37189,75671,81377,92470,73645,93258,6610,12185,43065,26704,47922,56650,7527,18006,56948,51675,16658,66402,1047,81624,77395,62310,73262,66050,57275,32936,87641,51528,58183,21952,84098,28913,28622,18140,89796,41317,93954,67690,64667,57092,21315,4731,76115,77291,11204,8634,93034,27411,27149,13843,9817,9407,84492,28444,59901,14592,89654,66207,66232,80293,74502,36925,55515,10121,16768,4720,71502,40500,21406,571,87320,81683,42788,86367,44686,22159,67015,35892,49668,83991,72088,30210,74009,86370,97956,2132,93512,54819,26094,51409,21485,94764,85806,13393,48543,7042,76538,64224,47909,9994,23750,17351,52141,30486,60380,86546,66606,36913,58173,45709,83679,82617,23381,9603,61107,566,6572,64745,10614,86371,43244,97154,10397,50975,68006,20045,16942,25536,74031,31807,70133,78790,40341,68730,39635,39013,66841,44043,96215,21270,59427,25034,40645,84741,52083,54503,36861,27659,95463,53847,40921,70116,61536,56756,8967,31079,20097,76014,99818,16606,19713,66904,27106,24874,96701,73287,76772,6073,57343,51428,91171,28299,17520,64903,4177,36071,94952,59008,28543,11576,74547,13260,20688,41261,2780,6633,37536,8844,95774,49323,30448,14154,83379,71259,23302,68402,43750,88505,15575,44927,6584,29867,21541,65763,12154,86616,79877,73259,68626,98962,68548,86576,48046,51755,64995,3661,64585,81550,46798,49319,50206,22024,5175,12923,23427,55915,91723,55831,83784,81034,86779,34622,84570,18960,48798,42970,95789,39465,82353,68905,44234,18244,54345,5592,89361,14644,67924,66415,89349,88530,72096,44459,5258,48317,48866,56886,90458,75889,4514,37227,11302,4667,2129,80414,86289,15887,87380,50749,83220,50529,20619,11606,36531,23409,78122,19566,76564,33045,66703,30017,35347,35038,12952,13971,3922,98702,11786,38388,69556,76728,60535,59961,23634,42211,98387,34880,27755,93182,99040,96390,65989,38375,3652,59657,57431,24666,11061,64713,85185,72849,58611,31220,26657,77056,24553,24993,5210,89024,32054,46997,92652,28363,98992,22593,97710,47766,37646,93573,95502,33790,92973,27766,62671,89698,10877,73893,41004,96035,18795,48080,59666,30241,35233,87353,43647,13404,41982,19264,29229,61369,8309,39383,42305,25944,13577,51545,68990,69801,37145,79189,55897,57793,66816,21930,56771,79296,73793,21632,42301,23696,72641,56310,85576,3004,25669,69221,32996,23040,65782,23712,13414,10758,15590,97298,74246,51511,46900,36795,38292,3852,6384,84421,3446,91670,45312,27609,87034,6683,83891,88991,16533,9197,34427,60384,48525,90978,46107,21693,12956,21804,46558,37682,81207,85840,53238,35026,4835,53264,41376,17783,64756,39278,25403,33042,20954,31193,24247,45911,92453,25370,86602,48574,57865,26436,16122,76614,17028,21262,59718,77821,14036,31033,90563,45410,15158,90209,84089,38053,60780,54166,14255,33120,27171,71798,91214,80040,56699,12475,40193,59415,4769,75920,1036,2692,75862,16612,73670,61182,3305,90334,187,91659,28063,75684,50017,82643,9282,77376,85469,8164,5584,36623,82597,83859,3435,98460,70095,80257,4381,6501,8924,35514,14297,54373,71369,5172,15955,82441,4636,48215,6821,3385,17663,40107,55679,30366,42390,95895,16083,58499,17176,55993,51034,49296,4010,78974,35930,2019,96226,27167,68245,53109,59037,37843,79243,10262,58797,61490,82590,52411,54783,29447,94551,30026,97959,93939,73217,82573,62154,78291,33728,39102,11484,86210,43794,73553,87435,1110,77108,56521,78610,8254,1842,43068,70415,79195,26136,49786,47279,38471,20379,54704,86614,91138,51595,50818,80186,73087,17262,94735,4952,27935,4928,74862,51392,62388,9570,38485,30594,56278,47395,72762,94597,72279,16010,34697,54475,67874,78014,88381,4045,41494,55178,46054,24373,1824,55333,7525,97908,61178,84635,2199,35361,4803,21907,79414,66083,54782,58692,28332,41851,28198,55819,37313,67046,16147,90478,71230,34141,85002,44332,35906,429,39744,773,22909,19536,98986,90945,45209,85439,92265,25291,22775,60611,49159,95701,36113,53923,60824,84935,29656,50007,86624,61691,76150,32187,42765,60660,13859,10792,88210,29374,29563,45188,28811,19739,67649,73775,99247,48414,91067,68253,9452,90116,91737,73979,62370,69112,58791,20349,71480,56852,36919,87977,77609,68738,85159,4918,70076,46473,4122,57713,1426,50987,77910,66211,62546,77749,96462,34304,77441,12104,91805,10287,60943,49632,83116,25716,23113,22707,77770,31176,6759,46130,4739,55554,3843,31653,70834,72877,41561,36903,23010,6663,2266,16360,70118,91936,17098,77278,4880,23484,94970,41826,46733,93484,68350,38861,18134,32936,241,24803,13876,93278,5039,35873,44418,5305,28510,36115,46717,15238,78607,23464,68635,55712,55007,92411,65739,4858,67537,37041,67453,89801,45963,14800,14225,65655,80463,9716,77255,65136,11230,76323,81433,36445,86523,61058,59560,19380,40791,48073,29626,36661,87907,57369,41623,13705,3880,45088,55444,41003,27754,1450,75312,71801,99600,60719,54182,29245,63315,73758,42973,32702,10855,56363,14638,84424,27178,78195,3133,70865,48019,26117,7151,52107,85562,41347,50486,69457,86961,95482,11857,93587,45680,42145,13029,10043,5142,49213,54525,85761,42707,70754,33768,87671,85038,58900,88438,20004,63390,14815,38875,73417,82875,89481,55517,944,15773,61814,32915,27868,5510,21916,28426,89881,16680,88850,11056,51991,4230,39107,49216,40065,4523,75848,95349,56034,10724,9885,88232,42478,65702,95696,39746,66032,88082,86905,30007,75068,66629,7358,26706,90511,72843,67857,20061,98581,69682,38e3,14186,70,2290,17269,30909,69449,19997,13275,2444,84985,51290,97641,15092,69650,21920,19617,7418,49725,91090,20805,28627,80665,67192,34697,57667,99323,50101,40587,35081,14037,34414,19898,60779,83267,87499,29596,41852,15813,32419,72232,8322,39184,46525,13833,65743,94595,37363,4711,35386,96413,10627,62625,56555,12919,93218,25191,98380,51923,66181,5788,73491,1452,487,12277,45415,11884,61300,94528,9181,26616,11455,31514,63290,45035,42759,33804,85721,80979,46010,50975,72482,31231,3086,58941,46102,25773,89742,29788,96741,88523,14922,88262,76305,57676,93259,2396,69145,26074,30056,3853,75317,56639,66203,38923,48939,22813,91864,10934,6714,84099,25631,73223,95630,97552,45950,22197,42886,33764,1263,41856,82057,62349,94091,78028,62651,18911,5693,92561,97821,41994,92343,76785,22216,4203,5038,86151,23596,24338,77181,51761,97693,10955,98159,37568,58932,72128,27303,99608,31688,57557,91022,43036,93927,32869,53653,55205,33139,47271,31224,51650,36422,86857,73799,22068,43376,84760,44898,65776,42451,71480,38509,41673,44141,75918,95652,68981,83001,48815,98086,67950,27986,33175,43624,55274,71051,61124,51550,64967,31570,15748,19159,38174,51078,79811,39183,57527,96550,85168,28824,47466,56993,13151,96664,29735,70251,1079,4314,77714,11507,1440,48415,31984,99915,20282,26524,18057,4992,40521,98108,84045,91961,79256,72244,25788,5487,23595,73302,14205,8925,27625,64343,28821,37992,67156,83320,31106,10884,30735,15067,51091,15668,48777,50770,19169,76504,41165,29749,92812,8065,66782,26841,1411,95461,61134,18699,52261,60469,81373,44825,11448,73320,30151,56991,31372,6655,36472,86292,30247,30931,21029,53410,9859,37267,47514,3492,49008,94727,25234,40546,53417,36492,25723,76227,58456,15979,34876,9574,34392,3751,36933,83921,65108,63135,67572,40184,21098],p=new Array(16),d=[16,8,16,24],s=4,g=0;for(var r=0;r<16;r++){p[r]=new Array(256);for(var i=0;i<256;i++)p[r][i]=i|i<<8|i<<16|i<<24|0;for(var m=3;0<=m;m--)for(var v=0;v<255;v++){var y=255<<(m<<3),b=0|p[r][v],w=function(t,e){var n=e-t+1,r=0,i=1;do{for(r=0,i=1;i<n;i*=10)r=10*r+(s<0&&(s=4,g++),o[g]%Math.pow(10,s+1)/Math.pow(10,s--)|0)}while((i/n|0)*n<=r);return t+r%n|0}(v,255);p[r][v]=p[r][v]&~y|p[r][w]&y|0,p[r][w]=p[r][w]&~y|b&y|0}}n=function(){function e(t){if(this instanceof e)return(t=t||{}).length=t.length||128,t.rounds=t.rounds||8,(t=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,h(e).call(this,t))).blockSize=16-t.state.hash.length,t.blockSizeInBytes=t.blockSize*t.unitSize,t.W=new Array(16),t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&f(t,n);for(var t=[{key:"reset",value:function(){l(h(e.prototype),"reset",this).call(this),this.state.hash=new Array(this.options.length/32|0);for(var t=0;t<this.state.hash.length;t++)this.state.hash[t]=0}},{key:"processBlock",value:function(t){for(var e=0;e<this.state.hash.length;e++)this.W[e]=0|this.state.hash[e];for(var n=this.state.hash.length;n<16;n++)this.W[n]=0|t[n-this.state.hash.length];for(var r=0;r<this.options.rounds<<1;r+=2)for(var i=0;i<4;i++){for(var o=0;o<16;o++){var s=0|p[r+(o/2|0)%2][255&this.W[o]];this.W[o-1>>>0&15]^=s,this.W[o+1&15]^=s}for(var a=0;a<16;a++)this.W[a]=Object(c.b)(this.W[a],d[i])}for(var u=0;u<this.state.hash.length;u++)this.state.hash[u]=this.state.hash[u]^this.W[15-u]|0}},{key:"finalize",value:function(){return 0<this.state.message.length&&this.addPaddingZero(this.blockSizeInBytes-this.state.message.length|0),this.addPaddingZero(this.blockSizeInBytes-8|0),this.addLengthBits(),this.process(),this.getStateHash()}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";var r=n(5),u=n(0);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function a(t,e,n){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=c(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,e){return(l=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}for(var h=[[7,12,17,22],[5,9,14,20],[4,11,16,23],[6,10,15,21]],f=new Array(64),o=0;o<64;o++)f[o]=4294967296*Math.abs(Math.sin(o+1))|0;n=function(){function o(){var t=this;if(!(t instanceof o))throw new TypeError("Cannot call a class as a function");var t=this,e=c(o).apply(this,arguments);if(!e||"object"!==i(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}var t=o,e=r.a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&l(t,e),t=[{key:"FF",value:function(t,e,n){return t&e|~t&n}},{key:"GG",value:function(t,e,n){return t&n|e&~n}},{key:"HH",value:function(t,e,n){return t^e^n}},{key:"II",value:function(t,e,n){return e^(t|~n)}},{key:"CC",value:function(t,e,n,r,i,o,s,a){return Object(u.a)(n+t(r,i,o)+s+e,a)+r|0}}],s((e=o).prototype,[{key:"reset",value:function(){a(c(o.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878]}},{key:"processBlock",value:function(t){var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],e=o.CC(o.FF,f[0],e,n,r,i,t[0],h[0][0]),i=o.CC(o.FF,f[1],i,e,n,r,t[1],h[0][1]),r=o.CC(o.FF,f[2],r,i,e,n,t[2],h[0][2]),n=o.CC(o.FF,f[3],n,r,i,e,t[3],h[0][3]);e=o.CC(o.FF,f[4],e,n,r,i,t[4],h[0][0]),i=o.CC(o.FF,f[5],i,e,n,r,t[5],h[0][1]),r=o.CC(o.FF,f[6],r,i,e,n,t[6],h[0][2]),n=o.CC(o.FF,f[7],n,r,i,e,t[7],h[0][3]),e=o.CC(o.FF,f[8],e,n,r,i,t[8],h[0][0]),i=o.CC(o.FF,f[9],i,e,n,r,t[9],h[0][1]),r=o.CC(o.FF,f[10],r,i,e,n,t[10],h[0][2]),n=o.CC(o.FF,f[11],n,r,i,e,t[11],h[0][3]),e=o.CC(o.FF,f[12],e,n,r,i,t[12],h[0][0]),i=o.CC(o.FF,f[13],i,e,n,r,t[13],h[0][1]),r=o.CC(o.FF,f[14],r,i,e,n,t[14],h[0][2]),n=o.CC(o.FF,f[15],n,r,i,e,t[15],h[0][3]),e=o.CC(o.GG,f[16],e,n,r,i,t[1],h[1][0]),i=o.CC(o.GG,f[17],i,e,n,r,t[6],h[1][1]),r=o.CC(o.GG,f[18],r,i,e,n,t[11],h[1][2]),n=o.CC(o.GG,f[19],n,r,i,e,t[0],h[1][3]),e=o.CC(o.GG,f[20],e,n,r,i,t[5],h[1][0]),i=o.CC(o.GG,f[21],i,e,n,r,t[10],h[1][1]),r=o.CC(o.GG,f[22],r,i,e,n,t[15],h[1][2]),n=o.CC(o.GG,f[23],n,r,i,e,t[4],h[1][3]),e=o.CC(o.GG,f[24],e,n,r,i,t[9],h[1][0]),i=o.CC(o.GG,f[25],i,e,n,r,t[14],h[1][1]),r=o.CC(o.GG,f[26],r,i,e,n,t[3],h[1][2]),n=o.CC(o.GG,f[27],n,r,i,e,t[8],h[1][3]),e=o.CC(o.GG,f[28],e,n,r,i,t[13],h[1][0]),i=o.CC(o.GG,f[29],i,e,n,r,t[2],h[1][1]),r=o.CC(o.GG,f[30],r,i,e,n,t[7],h[1][2]),n=o.CC(o.GG,f[31],n,r,i,e,t[12],h[1][3]),e=o.CC(o.HH,f[32],e,n,r,i,t[5],h[2][0]),i=o.CC(o.HH,f[33],i,e,n,r,t[8],h[2][1]),r=o.CC(o.HH,f[34],r,i,e,n,t[11],h[2][2]),n=o.CC(o.HH,f[35],n,r,i,e,t[14],h[2][3]),e=o.CC(o.HH,f[36],e,n,r,i,t[1],h[2][0]),i=o.CC(o.HH,f[37],i,e,n,r,t[4],h[2][1]),r=o.CC(o.HH,f[38],r,i,e,n,t[7],h[2][2]),n=o.CC(o.HH,f[39],n,r,i,e,t[10],h[2][3]),e=o.CC(o.HH,f[40],e,n,r,i,t[13],h[2][0]),i=o.CC(o.HH,f[41],i,e,n,r,t[0],h[2][1]),r=o.CC(o.HH,f[42],r,i,e,n,t[3],h[2][2]),n=o.CC(o.HH,f[43],n,r,i,e,t[6],h[2][3]),e=o.CC(o.HH,f[44],e,n,r,i,t[9],h[2][0]),i=o.CC(o.HH,f[45],i,e,n,r,t[12],h[2][1]),r=o.CC(o.HH,f[46],r,i,e,n,t[15],h[2][2]),n=o.CC(o.HH,f[47],n,r,i,e,t[2],h[2][3]),e=o.CC(o.II,f[48],e,n,r,i,t[0],h[3][0]),i=o.CC(o.II,f[49],i,e,n,r,t[7],h[3][1]),r=o.CC(o.II,f[50],r,i,e,n,t[14],h[3][2]),n=o.CC(o.II,f[51],n,r,i,e,t[5],h[3][3]),e=o.CC(o.II,f[52],e,n,r,i,t[12],h[3][0]),i=o.CC(o.II,f[53],i,e,n,r,t[3],h[3][1]),r=o.CC(o.II,f[54],r,i,e,n,t[10],h[3][2]),n=o.CC(o.II,f[55],n,r,i,e,t[1],h[3][3]),e=o.CC(o.II,f[56],e,n,r,i,t[8],h[3][0]),i=o.CC(o.II,f[57],i,e,n,r,t[15],h[3][1]),r=o.CC(o.II,f[58],r,i,e,n,t[6],h[3][2]),n=o.CC(o.II,f[59],n,r,i,e,t[13],h[3][3]),e=o.CC(o.II,f[60],e,n,r,i,t[4],h[3][0]),i=o.CC(o.II,f[61],i,e,n,r,t[11],h[3][1]),r=o.CC(o.II,f[62],r,i,e,n,t[2],h[3][2]),n=o.CC(o.II,f[63],n,r,i,e,t[9],h[3][3]),this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+i|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}]),s(e,t),o}();e.a=n},function(t,e,n){"use strict";var a=n(3),u=n(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e,n){return(l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=h(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var p=[1518500249,1859775393,2400959708,3395469782],n=function(){function e(t){if(this instanceof e)return(t=function(t,e){if(!e||"object"!==c(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,h(e).call(this,t))).options.rounds=t.options.rounds||80,t.W=new Array(80),t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&f(t,n);for(var t=[{key:"reset",value:function(){l(h(e.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878,-1009589776]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],o=0|this.state.hash[4],s=0;s<this.options.rounds;s++){this.W[s]=s<16?0|t[s]:0|Object(u.a)(this.W[s-3]^this.W[s-8]^this.W[s-14]^this.W[s-16],1);var a=Object(u.a)(e,5)+o+this.W[s]+p[s/20>>0]|0,a=s<20?a+(n&r|~n&i)|0:!(s<40)&&s<60?a+(n&r|n&i|r&i)|0:a+(n^r^i)|0,o=i,i=r,r=0|Object(u.a)(n,30),n=e,e=a}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+i|0,this.state.hash[4]=this.state.hash[4]+o|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";var c=n(0),a=n(3);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e,n){return(l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=h(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var r=new Array(256),n=[104,208,235,43,72,157,106,228,227,163,86,129,125,241,133,158,44,142,120,202,23,169,97,213,93,11,140,60,119,81,34,66,63,84,65,128,204,134,179,24,46,87,6,98,244,54,209,107,27,101,117,16,218,73,38,249,203,102,231,186,174,80,82,171,5,240,13,115,59,4,32,254,221,245,180,95,10,181,192,160,113,165,45,96,114,147,57,8,131,33,92,135,177,224,0,195,18,145,138,2,28,230,69,194,196,253,191,68,161,76,51,197,132,35,124,176,37,21,53,105,255,148,77,112,162,175,205,214,108,183,248,9,243,103,164,234,236,182,212,210,20,30,225,36,56,198,219,75,122,58,222,94,223,149,252,170,215,206,7,15,61,88,154,152,156,242,167,17,126,139,67,3,226,220,229,178,78,199,109,233,39,64,216,55,146,143,1,29,83,62,89,193,79,50,22,250,116,251,99,159,52,26,42,90,141,201,207,246,144,40,136,155,49,14,189,74,232,150,166,12,200,121,188,190,239,110,70,151,91,237,25,217,172,153,168,41,100,31,173,85,19,187,247,111,185,71,47,238,184,123,137,48,211,127,118,130],i=[1,11,9,12,13,6,15,3,14,8,7,4,10,2,5,0],o=[7,12,11,13,14,4,9,15,6,3,8,10,2,5,1,0],s=new Array(16),p=[1,1,4,1,8,5,2,9],d=[1,1,3,1,5,8,9,5],g=new Array(512),m=new Array(22),v=new Array(512),y=new Array(22),b=new Array(512),w=new Array(22);function _(t,e){for(var n=new Array(512),r=new Array(22),i=0;i<8;i++)n[i]=[];for(var o=0;o<256;o++){var s=new Array(10);s[1]=t[o],s[2]=s[1]<<1,256<=s[2]&&(s[2]^=285),s[3]=s[2]^s[1],s[4]=s[2]<<1,256<=s[4]&&(s[4]^=285),s[5]=s[4]^s[1],s[8]=s[4]<<1,256<=s[8]&&(s[8]^=285),s[9]=s[8]^s[1],n[0][2*o]=s[e[0]]<<24|s[e[1]]<<16|s[e[2]]<<8|s[e[3]],n[0][2*o+1]=s[e[4]]<<24|s[e[5]]<<16|s[e[6]]<<8|s[e[7]];for(var a=1;a<8;a++)n[a][2*o]=Object(c.d)(n[0][2*o+1],n[0][2*o],a<<3),n[a][2*o+1]=Object(c.c)(n[0][2*o+1],n[0][2*o],a<<3)}r[0]=0,r[1]=0;for(var u=1;u<=10;u++)r[2*u]=4278190080&n[0][16*u-16]^16711680&n[1][16*u-14]^65280&n[2][16*u-12]^255&n[3][16*u-10],r[2*u+1]=4278190080&n[4][16*u-7]^16711680&n[5][16*u-5]^65280&n[6][16*u-3]^255&n[7][16*u-1];return[n,r]}for(var x=0;x<16;x++)s[i[x]]=0|x;for(var C=0;C<256;C++){var k=i[C>>4],S=s[15&C],O=o[k^S];r[C]=i[k^O]<<4|s[S^O]}n=_(n,d),v=n[0],y=n[1],n=_(r,d),b=n[0],w=n[1],n=_(r,p),g=n[0],m=n[1],d=function(){function n(t){var e;switch(function(t){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this),(t=t||{}).type=t.type||"",t.rounds=t.rounds||10,(e=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,h(n).call(this,t))).options.type){case"0":case 0:e.C=v,e.RC=y;break;case"t":e.C=b,e.RC=w;break;default:e.C=g,e.RC=m}return e}var t=n,e=a.a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&f(t,e);for(var t=[{key:"reset",value:function(){l(h(n.prototype),"reset",this).call(this),this.state.hash=new Array(16);for(var t=0;t<16;t++)this.state.hash[t]=0}},{key:"processBlock",value:function(t){for(var e=new Array(16),n=[],r=0;r<16;r++)n[r]=t[r]^(e[r]=this.state.hash[r])|0;for(var i=[],o=1;o<=this.options.rounds;o++){for(var s=0;s<8;s++){i[2*s]=0;for(var a=i[2*s+1]=0,u=56,c=0;a<8;a++,c=(u-=8)<32?1:0)i[2*s]^=this.C[a][2*(e[2*(s-a&7)+c]>>>u%32&255)],i[2*s+1]^=this.C[a][2*(e[2*(s-a&7)+c]>>>u%32&255)+1]}for(var l=0;l<16;l++)e[l]=i[l];e[0]^=this.RC[2*o],e[1]^=this.RC[2*o+1];for(var h=0;h<8;h++){i[2*h]=e[2*h],i[2*h+1]=e[2*h+1];for(var f=0,p=56,d=0;f<8;f++,d=(p-=8)<32?1:0)i[2*h]^=this.C[f][2*(n[2*(h-f&7)+d]>>>p%32&255)],i[2*h+1]^=this.C[f][2*(n[2*(h-f&7)+d]>>>p%32&255)+1]}for(var g=0;g<16;g++)n[g]=i[g]}for(var m=0;m<16;m++)this.state.hash[m]^=n[m]^t[m]}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<32?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}],r=n.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return n}();e.a=d},function(t,e,n){"use strict";var a=n(4);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,e){return(l=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var h=function(){function e(t){if(this instanceof e)return(t=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,c(e).call(this,t))).unitSize=1,t.blockSizeInBytes=t.blockSize*t.unitSize,t.blockUnits=[],t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&l(t,n);for(var t=[{key:"process",value:function(){for(;this.state.message.length>=this.blockSizeInBytes;){this.blockUnits=new Array(this.blockSizeInBytes);for(var t=0;t<this.blockSizeInBytes;t++)this.blockUnits[t]=0|this.state.message.charCodeAt(t);this.state.message=this.state.message.substr(this.blockSizeInBytes),this.processBlock(this.blockUnits)}}},{key:"processBlock",value:function(t){}},{key:"getStateHash",value:function(t){t=t||this.state.hash.length;for(var e="",n=0;n<t;n++)e+=String.fromCharCode(255&this.state.hash[n]);return e}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e,n){return(p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=d(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function d(t){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function g(t,e){return(g=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var m=[41,46,67,201,162,216,124,1,61,54,84,161,236,240,6,19,98,167,5,243,192,199,115,140,152,147,43,217,188,76,130,202,30,155,87,60,253,212,224,22,103,66,111,24,138,23,229,18,190,78,196,214,218,158,222,73,160,251,245,142,187,47,238,122,169,104,121,145,21,178,7,63,148,194,16,137,11,34,95,33,128,127,93,154,90,144,50,39,53,62,204,231,191,247,151,3,255,25,48,179,72,165,181,209,215,94,146,42,172,86,170,198,79,184,56,210,150,164,125,182,118,252,107,226,156,116,4,241,69,157,112,89,100,113,135,32,134,91,207,101,230,45,168,2,27,96,37,173,174,176,185,246,28,70,97,105,52,64,126,15,85,71,163,35,221,81,175,58,195,92,249,206,186,197,234,38,44,83,13,110,133,40,132,9,211,223,205,244,65,129,77,82,106,220,55,200,108,193,171,250,36,225,123,8,12,189,177,74,120,136,149,139,227,99,232,109,233,203,213,254,59,0,29,57,242,239,183,14,102,88,208,228,166,119,114,248,235,117,75,10,49,68,80,180,143,237,31,26,219,153,141,51,159,17,131,20],n=function(){function e(t){if(this instanceof e)return(t=function(t,e){if(!e||"object"!==f(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,d(e).call(this,t))).options.rounds=t.options.rounds||18,t;throw new TypeError("Cannot call a class as a function")}var t=e,n=h;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&g(t,n);for(var t=[{key:"reset",value:function(){p(d(e.prototype),"reset",this).call(this),this.state.hash=new Array(48),this.state.checksum=new Array(16)}},{key:"processBlock",value:function(t){for(var e=0;e<16;e++)this.state.hash[16+e]=0|t[e],this.state.hash[32+e]=t[e]^this.state.hash[e];for(var n=0,r=0;r<this.options.rounds;r++){for(var i=0;i<48;i++)n=this.state.hash[i]^=m[n];n=n+r&255}for(var n=255&this.state.checksum[15],o=0;o<16;o++)n=this.state.checksum[o]^=m[t[o]^n]}},{key:"finalize",value:function(){this.addPaddingPKCS7(16-(15&this.state.message.length)|0),this.process();for(var t=0;t<16;t++)this.state.message+=String.fromCharCode(this.state.checksum[t]);return this.process(),this.getStateHash(16)}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";var a=n(5),c=n(0);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e,n){return(l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=h(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var p=[0,1518500249,1859775393,2400959708],d=[5,11,7,15,6,13,8,14,7,12,9,11,8,15,6,12,9,14,5,13],g=[10,17,25,30],m=[18,0,1,2,3,19,4,5,6,7,16,8,9,10,11,17,12,13,14,15,22,3,6,9,12,23,15,2,5,8,20,11,14,1,4,21,7,10,13,0,26,12,5,14,7,27,0,9,2,11,24,4,13,6,15,25,8,1,10,3,30,7,2,13,8,31,3,14,9,4,28,15,10,5,0,29,11,6,1,12],n=function(){function e(t){if(this instanceof e)return(t=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,h(e).call(this,t))).options.rounds=t.options.rounds||80,t.W=new Array(32),t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&f(t,n);for(var t=[{key:"reset",value:function(){l(h(e.prototype),"reset",this).call(this),this.state.hash=[1732584193,4023233417,2562383102,271733878,3285377520]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],o=0|this.state.hash[4],s=0;s<16;s++)this.W[s]=0|t[s];this.W[16]=this.W[0]^this.W[1]^this.W[2]^this.W[3]|0,this.W[17]=this.W[4]^this.W[5]^this.W[6]^this.W[7]|0,this.W[18]=this.W[8]^this.W[9]^this.W[10]^this.W[11]|0,this.W[19]=this.W[12]^this.W[13]^this.W[14]^this.W[15]|0,this.W[20]=this.W[3]^this.W[6]^this.W[9]^this.W[12]|0,this.W[21]=this.W[2]^this.W[5]^this.W[8]^this.W[15]|0,this.W[22]=this.W[1]^this.W[4]^this.W[11]^this.W[14]|0,this.W[23]=this.W[0]^this.W[7]^this.W[10]^this.W[13]|0,this.W[24]=this.W[5]^this.W[7]^this.W[12]^this.W[14]|0,this.W[25]=this.W[0]^this.W[2]^this.W[9]^this.W[11]|0,this.W[26]=this.W[4]^this.W[6]^this.W[13]^this.W[15]|0,this.W[27]=this.W[1]^this.W[3]^this.W[8]^this.W[10]|0,this.W[28]=this.W[2]^this.W[7]^this.W[8]^this.W[13]|0,this.W[29]=this.W[3]^this.W[4]^this.W[9]^this.W[14]|0,this.W[30]=this.W[0]^this.W[5]^this.W[10]^this.W[15]|0,this.W[31]=this.W[1]^this.W[6]^this.W[11]^this.W[12]|0;for(var a=0;a<this.options.rounds;a++)var u=Object(c.a)(e,d[a%20])+o+this.W[m[a]]+p[a/20>>0]|0,u=a<20?u+(n&r|~n&i)|0:!(a<40)&&a<60?u+(r^(n|~i))|0:u+(n^r^i)|0,o=i,i=r,r=0|Object(c.a)(n,g[a/20>>0]),n=e,e=u;this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+i|0,this.state.hash[4]=this.state.hash[4]+o|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";n.d(e,"a",function(){return r});var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function r(t){for(var e="",n=t.length-t.length%3|0,r=0,i=0;i<n;i+=3)r=t.charCodeAt(i)<<16|t.charCodeAt(i+1)<<8|t.charCodeAt(i+2),e+=o.charAt(r>>18)+o.charAt(r>>12&63)+o.charAt(r>>6&63)+o.charAt(63&r);return t.length-n==2?(r=t.charCodeAt(n)<<16|t.charCodeAt(1+n)<<8,e+=o.charAt(r>>18)+o.charAt(r>>12&63)+o.charAt(r>>6&63)+"="):t.length-n==1&&(r=t.charCodeAt(n)<<16,e+=o.charAt(r>>18)+o.charAt(r>>12&63)+"=="),e}},function(t,e,n){"use strict";var r=n(5),u=n(0);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function a(t,e,n){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=c(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,e){return(l=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var h=[[3,7,11,19],[3,5,9,13],[3,9,11,15]],f=1518500249,p=1859775393,n=function(){function o(){var t=this;if(!(t instanceof o))throw new TypeError("Cannot call a class as a function");var t=this,e=c(o).apply(this,arguments);if(!e||"object"!==i(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}var t=o,e=r.a;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");return t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&l(t,e),t=[{key:"FF",value:function(t,e,n){return t&e|~t&n}},{key:"GG",value:function(t,e,n){return t&e|t&n|e&n}},{key:"HH",value:function(t,e,n){return t^e^n}},{key:"CC",value:function(t,e,n,r,i,o,s,a){return 0|Object(u.a)(n+t(r,i,o)+s+e,a)}}],s((e=o).prototype,[{key:"reset",value:function(){a(c(o.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878]}},{key:"processBlock",value:function(t){var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],e=o.CC(o.FF,0,e,n,r,i,t[0],h[0][0]),i=o.CC(o.FF,0,i,e,n,r,t[1],h[0][1]),r=o.CC(o.FF,0,r,i,e,n,t[2],h[0][2]),n=o.CC(o.FF,0,n,r,i,e,t[3],h[0][3]);e=o.CC(o.FF,0,e,n,r,i,t[4],h[0][0]),i=o.CC(o.FF,0,i,e,n,r,t[5],h[0][1]),r=o.CC(o.FF,0,r,i,e,n,t[6],h[0][2]),n=o.CC(o.FF,0,n,r,i,e,t[7],h[0][3]),e=o.CC(o.FF,0,e,n,r,i,t[8],h[0][0]),i=o.CC(o.FF,0,i,e,n,r,t[9],h[0][1]),r=o.CC(o.FF,0,r,i,e,n,t[10],h[0][2]),n=o.CC(o.FF,0,n,r,i,e,t[11],h[0][3]),e=o.CC(o.FF,0,e,n,r,i,t[12],h[0][0]),i=o.CC(o.FF,0,i,e,n,r,t[13],h[0][1]),r=o.CC(o.FF,0,r,i,e,n,t[14],h[0][2]),n=o.CC(o.FF,0,n,r,i,e,t[15],h[0][3]),e=o.CC(o.GG,f,e,n,r,i,t[0],h[1][0]),i=o.CC(o.GG,f,i,e,n,r,t[4],h[1][1]),r=o.CC(o.GG,f,r,i,e,n,t[8],h[1][2]),n=o.CC(o.GG,f,n,r,i,e,t[12],h[1][3]),e=o.CC(o.GG,f,e,n,r,i,t[1],h[1][0]),i=o.CC(o.GG,f,i,e,n,r,t[5],h[1][1]),r=o.CC(o.GG,f,r,i,e,n,t[9],h[1][2]),n=o.CC(o.GG,f,n,r,i,e,t[13],h[1][3]),e=o.CC(o.GG,f,e,n,r,i,t[2],h[1][0]),i=o.CC(o.GG,f,i,e,n,r,t[6],h[1][1]),r=o.CC(o.GG,f,r,i,e,n,t[10],h[1][2]),n=o.CC(o.GG,f,n,r,i,e,t[14],h[1][3]),e=o.CC(o.GG,f,e,n,r,i,t[3],h[1][0]),i=o.CC(o.GG,f,i,e,n,r,t[7],h[1][1]),r=o.CC(o.GG,f,r,i,e,n,t[11],h[1][2]),n=o.CC(o.GG,f,n,r,i,e,t[15],h[1][3]),e=o.CC(o.HH,p,e,n,r,i,t[0],h[2][0]),i=o.CC(o.HH,p,i,e,n,r,t[8],h[2][1]),r=o.CC(o.HH,p,r,i,e,n,t[4],h[2][2]),n=o.CC(o.HH,p,n,r,i,e,t[12],h[2][3]),e=o.CC(o.HH,p,e,n,r,i,t[2],h[2][0]),i=o.CC(o.HH,p,i,e,n,r,t[10],h[2][1]),r=o.CC(o.HH,p,r,i,e,n,t[6],h[2][2]),n=o.CC(o.HH,p,n,r,i,e,t[14],h[2][3]),e=o.CC(o.HH,p,e,n,r,i,t[1],h[2][0]),i=o.CC(o.HH,p,i,e,n,r,t[9],h[2][1]),r=o.CC(o.HH,p,r,i,e,n,t[5],h[2][2]),n=o.CC(o.HH,p,n,r,i,e,t[13],h[2][3]),e=o.CC(o.HH,p,e,n,r,i,t[3],h[2][0]),i=o.CC(o.HH,p,i,e,n,r,t[11],h[2][1]),r=o.CC(o.HH,p,r,i,e,n,t[7],h[2][2]),n=o.CC(o.HH,p,n,r,i,e,t[15],h[2][3]),this.state.hash=[this.state.hash[0]+e|0,this.state.hash[1]+n|0,this.state.hash[2]+r|0,this.state.hash[3]+i|0]}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}]),s(e,t),o}();e.a=n},function(t,e,n){"use strict";var a=n(3),u=n(0);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e,n){return(l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=h(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var p=[1518500249,1859775393,2400959708,3395469782],n=function(){function e(t){if(this instanceof e)return(t=function(t,e){if(!e||"object"!==c(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,h(e).call(this,t))).options.rounds=t.options.rounds||80,t.W=new Array(80),t;throw new TypeError("Cannot call a class as a function")}var t=e,n=a.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&f(t,n);for(var t=[{key:"reset",value:function(){l(h(e.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878,-1009589776]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],i=0|this.state.hash[3],o=0|this.state.hash[4],s=0;s<this.options.rounds;s++){this.W[s]=s<16?0|t[s]:this.W[s-3]^this.W[s-8]^this.W[s-14]^this.W[s-16]|0;var a=Object(u.a)(e,5)+o+this.W[s]+p[s/20>>0]|0,a=s<20?a+(n&r|~n&i)|0:!(s<40)&&s<60?a+(n&r|n&i|r&i)|0:a+(n^r^i)|0,o=i,i=r,r=0|Object(u.a)(n,30),n=e,e=a}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+i|0,this.state.hash[4]=this.state.hash[4]+o|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}],r=e.prototype,i=t,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return e}();e.a=n},function(t,e,n){"use strict";function r(t){for(var e="",n=new Uint8Array(t),r=0;r<n.length;r++)e+=String.fromCharCode(n[r]);return e}n.d(e,"a",function(){return r})},,function(t,e,n){"use strict";var r=function(){function i(t,e){if(!(this instanceof i))throw new TypeError("Cannot call a class as a function");t.length>e.blockSizeInBytes&&(e.update(t),t=e.finalize(),e.reset());for(var n=t.length;n<e.blockSizeInBytes;n++)t+="\0";this.oPad="";for(var r=0;r<t.length;r++)e.update(String.fromCharCode(54^t.charCodeAt(r))),this.oPad+=String.fromCharCode(92^t.charCodeAt(r));this.hasher=e}for(var t=[{key:"update",value:function(t){this.hasher.update(t)}},{key:"finalize",value:function(){var t=this.hasher.finalize();return this.hasher.reset(),this.hasher.update(this.oPad),this.hasher.update(t),this.hasher.finalize()}}],e=i.prototype,n=t,r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}return i}();e.a=r},,,,,,,,,function(t,e,n){"use strict";n.r(e);var s=n(14),a=n(13),u=n(16),c=n(10),l=n(8),h=n(17),f=n(11),p=n(7),d=n(6),g=n(9),m=n(12),v=n(1),y=n(18),b=n(2),w=n(15),_=n(20);n=new(n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.encoder={},this.encoder.fromUtf=v.a,this.encoder.fromArrayBuffer=y.a,this.encoder.toHex=b.a,this.encoder.toBase64=w.a}for(var e=[{key:"getHasher",value:function(t,e){switch(e=e||{},t){case"has160":return new s.a(e);case"md2":return new a.a(e);case"md4":return new u.a(e);case"md5":return new c.a(e);case"ripemd128":return e=Object.assign({},{length:128},e),new l.a(e);case"ripemd":case"ripemd160":return e=Object.assign({},{length:160},e),new l.a(e);case"ripemd256":return e=Object.assign({},{length:256},e),new l.a(e);case"ripemd320":return e=Object.assign({},{length:320},e),new l.a(e);case"sha0":return new h.a(e);case"sha1":return new f.a(e);case"sha224":return e=Object.assign({},{length:224},e),new p.a(e);case"sha256":return e=Object.assign({},{length:256},e),new p.a(e);case"sha384":return e=Object.assign({},{length:384},e),new d.a(e);case"sha512":return e=Object.assign({},{length:512},e),new d.a(e);case"sha512/224":return e=Object.assign({},{length:224},e),new d.a(e);case"sha512/256":return e=Object.assign({},{length:256},e),new d.a(e);case"snefru":case"snefru128":case"snefru128/8":return e=Object.assign({},{length:128},e),new g.a(e);case"snefru256":case"snefru256/8":return e=Object.assign({},{length:256},e),new g.a(e);case"snefru128/2":return e=Object.assign({},{length:128,rounds:2},e),new g.a(e);case"snefru256/4":return e=Object.assign({},{length:256,rounds:4},e),new g.a(e);case"whirlpool":return new m.a(e);case"whirlpool-0":return e=Object.assign({},{type:"0"},e),new m.a(e);case"whirlpool-t":return e=Object.assign({},{type:"t"},e),new m.a(e)}}},{key:"hash",value:function(t,e,n){t=this.getHasher(t,n=n||{});return t.update(Object(v.a)(e)),Object(b.a)(t.finalize())}},{key:"getHmac",value:function(t,e){return new _.a(t,e)}},{key:"hmac",value:function(t,e,n){t=this.getHmac(Object(v.a)(t),n);return t.update(Object(v.a)(e)),Object(b.a)(t.finalize())}}],n=t.prototype,r=e,i=0;i<r.length;i++){var o=r[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(n,o.key,o)}return t}());e.default=n}]).default;!function(s){"use strict";var n={page:1,pageSize:200,total:0,showTotal:!1,totalTxt:"共{total}条",noData:!1,showSkip:!1,showPN:!0,prevPage:"上一页",nextPage:"下一页",fastForward:0,selectOption:[],backFun:function(t){}};function e(t,e){this.element=s(t),this.settings=s.extend({},n,e),this.pageNum=1,this.pageList=[],this.pageTatol=0,this.init()}s.extend(e.prototype,{init:function(){this.element.empty(),this.viewHtml(),this.clickBtn()},creatHtml:function(t){t==this.settings.page?this.pageList.push('<button class="active" data-page='+t+">"+t+"</button>"):this.pageList.push("<button data-page="+t+">"+t+"</button>")},viewHtml:function(){var t=this.settings,e=0,n=[];if(0<t.total)e=Math.ceil(t.total/t.pageSize);else{if(!t.noData)return;t.page=e=1,t.total=0}if(this.pageTatol=e,this.pageNum=t.page,t.showTotal&&n.push('<div class="spage-total">'+t.totalTxt.replace(/\{(\w+)\}/gi,t.total)+"</div>"),n.push('<div class="spage-number">'),this.pageList=[],t.showPN&&(1==t.page?this.pageList.push('<button class="button-disabled" data-page="prev"><i class="prevBtn"></i></button>'):this.pageList.push('<button data-page="prev"><i class="prevBtn"></i></button>')),e<=6)for(var r=1;r<e+1;r++)this.creatHtml(r);else if(t.page<3){for(r=1;r<=3;r++)this.creatHtml(r);this.pageList.push('<button data-page="after" class="spage-after">...</button><button data-page='+e+">"+e+"</button>")}else if(t.page>e-3){this.pageList.push('<button data-page="1">1</button><button data-page="before" class="spage-before">...</button>');for(r=e-3;r<=e;r++)this.creatHtml(r)}else{this.pageList.push('<button data-page="1">1</button>'),3<t.page&&this.pageList.push('<button data-page="before" class="spage-before">...</button>');for(r=t.page-1;r<=Number(t.page)+1;r++)this.creatHtml(r);t.page<=e-3&&this.pageList.push('<button data-page="after" class="spage-after">...</button>'),this.pageList.push("<button data-page="+e+">"+e+"</button>")}if(t.showPN&&(t.page==e?this.pageList.push('<button class="button-disabled" data-page="next"><i class="nextBtn"></i></button>'):this.pageList.push('<button data-page="next"><i class="nextBtn"></i></button>')),n.push(this.pageList.join("")),n.push("</div>"),0<t.selectOption.length){for(var i='<select class="selectNum" id="selectNum">',o=0;o<=t.selectOption.length-1;o++)i+="<option value="+t.selectOption[o]+" ",t.pageSize===t.selectOption[o]?i+="selected>"+t.selectOption[o]+"行/页</option>":i+=">"+t.selectOption[o]+"行/页</option>";n.push(i+="</select>")}t.showSkip&&n.push('<div class="spage-skip">跳至&nbsp;<input type="text" class="luckysheet-mousedown-cancel" value="'+t.page+'"/>&nbsp;页&nbsp;&nbsp;</div>'),this.element.html(n.join(""))},clickBtn:function(){var n=this,r=this.settings,i=this.element,o=this.pageTatol;this.element.on("change","select",function(t){var e=parseInt(document.getElementById("selectNum").value);r.pageSize=e,r.page=1,n.element.empty(),n.viewHtml(),r.backFun(r)}),this.element.off("click","button"),this.element.on("click","button",function(){var t=s(this).data("page");switch(t){case"prev":r.page=1<=r.page-1?r.page-1:1,t=r.page;break;case"next":r.page=Number(r.page)+1<=o?Number(r.page)+1:o,t=r.page;break;case"before":r.page=1<=r.page-r.fastForward?r.page-r.fastForward:1,t=r.page;break;case"after":r.page=Number(r.page)+Number(r.fastForward)<=o?Number(r.page)+Number(r.fastForward):o,t=r.page;break;case"go":var e=parseInt(i.find("input").val());if(!(/^[0-9]*$/.test(e)&&1<=e&&e<=o))return;t=r.page=e;break;default:r.page=t}t!=n.pageNum&&(n.pageNum=r.page,n.viewHtml(),r.backFun(r))}),this.element.off("keyup","input"),this.element.on("keyup","input",function(t){13==t.keyCode&&(t=parseInt(i.find("input").val()),/^[0-9]*$/.test(t)&&1<=t&&t<=o&&t!=n.pageNum&&(r.page=t,n.pageNum=t,n.viewHtml(),r.backFun(r)))}),0<r.fastForward&&(i.find(".spage-after").hover(function(){s(this).html("&raquo;")},function(){s(this).html("...")}),i.find(".spage-before").hover(function(){s(this).html("&laquo;")},function(){s(this).html("...")}))}}),s.fn.sPage=function(t){return this.each(function(){new e(this,t)})}}(jQuery,(window,document));