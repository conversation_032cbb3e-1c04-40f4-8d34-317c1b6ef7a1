# port 端口号
VITE_PORT = 8080

#浏览器自动打开
VITE_OPEN = true

# 本地环境
ENV = 'development'

# ADMIN 服务地址
VITE_ADMIN_PROXY_PATH = http://gateway.xyd-dev.zszc.jianshicha.cn/
VITE_ADMIN_PROXY_PATH_FILE = http://gateway.beer-brace.zszc.jianshicha.cn/file-cloud/
# VITE_ADMIN_PROXY_PATH = http://gateway.mf-dev.zszc.jianshicha.cn/
#VITE_ADMIN_PROXY_PATH = http://gateway.xz-dev.zszc.jianshicha.cn/
# dev
# VITE_ADMIN_PROXY_PATH = 'http://gateway.sd-dev.zszc.jianshicha.cn'
# VITE_ADMIN_PROXY_PATH = 'http://gateway.sd-test.zszc.jianshicha.cn'

# 本地代理映射
VITE_BASE_API = '/etbApi'

# 代码生成服务地址
VITE_GEN_PROXY_PATH = http://localhost:5003

# 启动验证码校验
VITE_VERIFY_IMAGE_ENABLE = true