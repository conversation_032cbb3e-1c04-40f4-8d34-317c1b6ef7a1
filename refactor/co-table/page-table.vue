<!--
  Page-Table 向后兼容入口文件
  保持与原有 page-table 组件的完全兼容
-->
<template>
  <co-table-main
    :form-model="formModel"
    :table-header="tableHeader"
    :table-config="tableConfig"
    :search-config="searchConfig"
    v-bind="$attrs"
    @operation="onOperation"
    @selection-change="onSelectionChange"
    @search="onSearch"
    v-on="$listeners"
  >
    <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps || {}" />
    </template>
  </co-table-main>
</template>

<script setup>
import CoTableMain from './index.vue';

// 组件名称
defineOptions({
  name: 'PageTable',
  inheritAttrs: false
});

// Props定义（与原page-table保持一致）
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  formModel: {
    type: Object,
    default: () => ({})
  },
  tableHeader: {
    type: Array,
    default: () => []
  },
  tableConfig: {
    type: Object,
    default: () => ({})
  },
  searchConfig: {
    type: Object,
    default: () => ({})
  }
});

// Emits定义
const emit = defineEmits(['operation', 'selection-change', 'search']);

// 事件处理
const onOperation = (data, refreshFn) => {
  emit('operation', data, refreshFn);
};

const onSelectionChange = (selection) => {
  emit('selection-change', selection);
};

const onSearch = (searchData) => {
  emit('search', searchData);
};
</script>
