/**
 * Co-Table 分页功能组合式函数
 * 提供分页数据管理和事件处理
 */

import { ref, reactive, computed, watch } from 'vue';
import { getGlobalConfig } from '../config/index.js';

/**
 * 分页功能组合式函数
 * @param {object} props - 组件props
 * @param {Function} emit - emit函数
 * @returns {object} 分页相关的响应式数据和方法
 */
export function useTablePagination(props, emit) {
  // 分页配置
  const paginationConfig = getGlobalConfig('pagination');
  
  // 响应式数据
  const paginationData = reactive({
    current: 1,
    size: 10,
    total: 0,
    pages: 1
  });
  
  // 计算属性
  const paginationAttrs = computed(() => {
    const defaultAttrs = paginationConfig.component || {};
    const userAttrs = props.paginationAttrs || {};
    
    return Object.assign({}, defaultAttrs, userAttrs, {
      currentPage: paginationData.current,
      pageSize: paginationData.size,
      total: paginationData.total,
      pageCount: paginationData.pages
    });
  });
  
  const requestMapping = computed(() => {
    return paginationConfig.request || {
      current: 'current',
      size: 'size'
    };
  });
  
  const responseMapping = computed(() => {
    return paginationConfig.response || {
      current: 'current',
      pages: 'pages',
      size: 'size',
      total: 'total',
      records: 'list'
    };
  });

  /**
   * 初始化分页数据
   * @param {object} data - 分页数据
   */
  const initPagination = (data = {}) => {
    const mapping = responseMapping.value;
    
    paginationData.current = data[mapping.current] || 1;
    paginationData.size = data[mapping.size] || 10;
    paginationData.total = data[mapping.total] || 0;
    paginationData.pages = data[mapping.pages] || 1;
  };

  /**
   * 获取请求参数
   * @param {object} extraParams - 额外参数
   * @returns {object} 请求参数
   */
  const getRequestParams = (extraParams = {}) => {
    const mapping = requestMapping.value;
    
    return {
      ...extraParams,
      [mapping.current]: paginationData.current,
      [mapping.size]: paginationData.size
    };
  };

  /**
   * 处理页码变化
   * @param {number} page - 新页码
   */
  const handleCurrentChange = (page) => {
    paginationData.current = page;
    
    emit('pagination-change', {
      type: 'current',
      current: page,
      size: paginationData.size,
      pagination: { ...paginationData }
    });
  };

  /**
   * 处理页大小变化
   * @param {number} size - 新页大小
   */
  const handleSizeChange = (size) => {
    paginationData.size = size;
    paginationData.current = 1; // 重置到第一页
    
    emit('pagination-change', {
      type: 'size',
      current: paginationData.current,
      size: size,
      pagination: { ...paginationData }
    });
  };

  /**
   * 跳转到指定页
   * @param {number} page - 目标页码
   */
  const goToPage = (page) => {
    if (page < 1 || page > paginationData.pages) {
      console.warn(`页码 ${page} 超出范围 [1, ${paginationData.pages}]`);
      return;
    }
    
    handleCurrentChange(page);
  };

  /**
   * 跳转到第一页
   */
  const goToFirstPage = () => {
    goToPage(1);
  };

  /**
   * 跳转到最后一页
   */
  const goToLastPage = () => {
    goToPage(paginationData.pages);
  };

  /**
   * 上一页
   */
  const prevPage = () => {
    if (paginationData.current > 1) {
      goToPage(paginationData.current - 1);
    }
  };

  /**
   * 下一页
   */
  const nextPage = () => {
    if (paginationData.current < paginationData.pages) {
      goToPage(paginationData.current + 1);
    }
  };

  /**
   * 重置分页
   */
  const resetPagination = () => {
    paginationData.current = 1;
    paginationData.size = 10;
    paginationData.total = 0;
    paginationData.pages = 1;
  };

  /**
   * 设置分页数据
   * @param {object} data - 分页数据
   */
  const setPagination = (data) => {
    Object.assign(paginationData, data);
  };

  /**
   * 获取分页信息
   * @returns {object} 分页信息
   */
  const getPaginationInfo = () => {
    return {
      ...paginationData,
      hasNext: paginationData.current < paginationData.pages,
      hasPrev: paginationData.current > 1,
      startIndex: (paginationData.current - 1) * paginationData.size + 1,
      endIndex: Math.min(paginationData.current * paginationData.size, paginationData.total)
    };
  };

  /**
   * 计算总页数
   * @param {number} total - 总记录数
   * @param {number} size - 页大小
   * @returns {number} 总页数
   */
  const calculatePages = (total, size) => {
    return Math.ceil(total / size) || 1;
  };

  /**
   * 更新总记录数
   * @param {number} total - 总记录数
   */
  const updateTotal = (total) => {
    paginationData.total = total;
    paginationData.pages = calculatePages(total, paginationData.size);
    
    // 如果当前页超出范围，跳转到最后一页
    if (paginationData.current > paginationData.pages) {
      paginationData.current = paginationData.pages;
    }
  };

  /**
   * 从响应数据中提取记录
   * @param {object} response - 响应数据
   * @returns {Array} 记录列表
   */
  const extractRecords = (response) => {
    const mapping = responseMapping.value;
    return response[mapping.records] || [];
  };

  // 监听分页数据变化
  watch(
    () => paginationData.total,
    (newTotal) => {
      paginationData.pages = calculatePages(newTotal, paginationData.size);
    }
  );

  watch(
    () => paginationData.size,
    (newSize) => {
      paginationData.pages = calculatePages(paginationData.total, newSize);
    }
  );

  return {
    // 响应式数据
    paginationData,
    paginationAttrs,
    requestMapping,
    responseMapping,
    
    // 方法
    initPagination,
    getRequestParams,
    handleCurrentChange,
    handleSizeChange,
    goToPage,
    goToFirstPage,
    goToLastPage,
    prevPage,
    nextPage,
    resetPagination,
    setPagination,
    getPaginationInfo,
    calculatePages,
    updateTotal,
    extractRecords
  };
}
