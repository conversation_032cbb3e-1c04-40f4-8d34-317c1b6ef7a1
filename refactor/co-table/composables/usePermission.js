/**
 * Co-Table 权限管理组合式函数
 * 提供权限检查、按钮权限控制、路由权限等功能
 */

import { ref, reactive, computed, inject } from 'vue';
import { getType, deepClone } from '../utils/index.js';
import { getGlobalConfig } from '../config/index.js';
import { 
  hasPermission, 
  hasAnyPermission, 
  parseButtonRule, 
  filterButtons,
  getRoutePermissions,
  mergePermissions,
  createPermissionChecker
} from '../utils/permission.js';

/**
 * 权限管理组合式函数
 * @param {object} props - 组件props
 * @param {object} route - 路由对象
 * @returns {object} 权限相关的响应式数据和方法
 */
export function usePermission(props, route = null) {
  // 权限配置
  const permissionConfig = getGlobalConfig('permission');
  const metaPermisKey = permissionConfig.metaPermisKey || 'perms';
  
  // 响应式数据
  const userPermissions = ref([]);
  const routePermissions = ref([]);
  const operationList = ref([]);
  const inTableRowBtn = reactive({});
  const showPermisBtn = reactive({});
  
  // 全局权限处理方法注册
  const globalPermissionHandlers = reactive({
    // 自定义权限检查方法
    customCheck: null,
    // 权限获取方法
    getPermissions: null,
    // 权限缓存方法
    cachePermissions: null,
    // 权限清除方法
    clearPermissions: null
  });

  /**
   * 注册全局权限处理方法
   * @param {string} type - 处理方法类型
   * @param {Function} handler - 处理方法
   */
  const registerPermissionHandler = (type, handler) => {
    if (typeof handler === 'function') {
      globalPermissionHandlers[type] = handler;
    } else {
      console.warn(`权限处理方法 ${type} 必须是函数类型`);
    }
  };

  /**
   * 获取用户权限
   * @returns {Array} 用户权限列表
   */
  const getUserPermissions = async () => {
    // 如果有自定义获取方法，使用自定义方法
    if (globalPermissionHandlers.getPermissions) {
      try {
        const permissions = await globalPermissionHandlers.getPermissions();
        userPermissions.value = Array.isArray(permissions) ? permissions : [];
        return userPermissions.value;
      } catch (error) {
        console.error('获取用户权限失败:', error);
      }
    }
    
    // 默认从props或全局状态获取
    if (props.userPermissions) {
      userPermissions.value = props.userPermissions;
    }
    
    return userPermissions.value;
  };

  /**
   * 获取路由权限
   * @param {object} routeObj - 路由对象
   * @returns {Array} 路由权限列表
   */
  const getRoutePermissionsData = (routeObj = route) => {
    if (!routeObj) return [];
    
    const permissions = getRoutePermissions(routeObj, metaPermisKey);
    routePermissions.value = permissions;
    return permissions;
  };

  /**
   * 检查权限
   * @param {string|Array} permission - 权限标识
   * @param {object} options - 选项
   * @returns {boolean} 是否有权限
   */
  const checkPermission = (permission, options = {}) => {
    const { useCustomCheck = true, useRoutePermissions = true } = options;
    
    // 如果有自定义检查方法，优先使用
    if (useCustomCheck && globalPermissionHandlers.customCheck) {
      try {
        return globalPermissionHandlers.customCheck(permission, {
          userPermissions: userPermissions.value,
          routePermissions: routePermissions.value,
          ...options
        });
      } catch (error) {
        console.error('自定义权限检查失败:', error);
      }
    }
    
    // 合并用户权限和路由权限
    let allPermissions = [...userPermissions.value];
    if (useRoutePermissions) {
      allPermissions = mergePermissions(routePermissions.value, userPermissions.value, 'push');
    }
    
    return hasPermission(permission, allPermissions);
  };

  /**
   * 解析操作按钮配置
   * @param {object} config - 表格配置
   * @param {object} attrs - 组件属性
   */
  const renderOperation = (config, attrs = {}) => {
    const operList = config?.operation?.list || [];
    const operMerge = config?.operation?.merge;
    const metaPermis = routePermissions.value;
    
    let btnList = reactive([...operList]);
    
    // 合并路由权限和操作按钮
    if (metaPermis.length) {
      if (operMerge) {
        btnList = operMerge === "push" 
          ? [...metaPermis, ...operList] 
          : [...operList, ...metaPermis];
      } else {
        btnList = metaPermis;
      }
    }
    
    if (btnList.length) {
      btnList.forEach(btn => {
        // 兼容旧系统字段
        if ('text' in btn) btn.name = btn.text;
        if ('showType' in btn) btn.type = btn.showType;
        if ('tableName' in btn) btn.tableKey = btn.tableName;
        if ('class' in btn) delete btn.class;
        
        delete btn.text;
        btn.size = btn.size || attrs.size || '';
        btn.loading = false;
        
        // 处理 element-plus 属性
        const btnAttrs = btn.attributes;
        if (btnAttrs && typeof btnAttrs === 'string') {
          const attrArr = btnAttrs.split(',');
          for (const key of attrArr) {
            btn[key] = true;
          }
          delete btn.attributes;
        }
      });
      
      operationList.value = btnList;
    }
  };

  /**
   * 设置行按钮权限
   * @param {object} row - 行数据
   * @param {object} options - 选项
   */
  const setRowPermissions = (row, options = {}) => {
    const { 
      tableKey = '', 
      currentRowKey = 'id', 
      config = {},
      operationOpts = { showCount: -1 }
    } = options;
    
    // 过滤并分析按钮权限
    const analysisBtn = operationList.value
      .filter(btn => +btn.inTable !== 2)
      .filter(btn => {
        const rule = btn.rule;
        
        // 处理 storage 规则
        if (rule && rule.includes('storage')) {
          matchStorage(rule);
        }
        
        // 解析规则
        const ruleResult = parseButtonRule(
          rule || 'true', 
          row, 
          window.storage || {}, 
          null
        );
        
        // 检查表格键匹配
        const tableKeyMatch = !btn.tableKey || tableKey.includes(btn.tableKey);
        
        const hasPermission = ruleResult && tableKeyMatch;
        
        // 检查隐藏列表
        const operation = config.operation;
        if (operation?.hiddenList) {
          return hasPermission && !operation.hiddenList(row, tableKey);
        }
        
        return hasPermission;
      });
    
    // 设置每行要显示的按钮
    const rowKey = currentRowKey + row[currentRowKey];
    inTableRowBtn[rowKey] = deepClone(analysisBtn);
    
    // 设置按钮折叠
    const { showCount } = operationOpts;
    for (const key in inTableRowBtn) {
      showPermisBtn[key] = showCount < 0 
        ? inTableRowBtn[key] 
        : inTableRowBtn[key].slice(0, showCount);
    }
  };

  /**
   * 匹配 storage 规则
   * @param {string} rule - 规则字符串
   */
  const matchStorage = (rule) => {
    // 这里可以添加 storage 匹配逻辑
    // 暂时保持与原有逻辑一致
  };

  /**
   * 获取按钮显示列表
   * @param {string} rowKey - 行键
   * @returns {Array} 显示的按钮列表
   */
  const getShowButtons = (rowKey) => {
    return showPermisBtn[rowKey] || [];
  };

  /**
   * 获取按钮完整列表
   * @param {string} rowKey - 行键
   * @returns {Array} 完整的按钮列表
   */
  const getAllButtons = (rowKey) => {
    return inTableRowBtn[rowKey] || [];
  };

  /**
   * 清除权限缓存
   */
  const clearPermissionCache = () => {
    userPermissions.value = [];
    routePermissions.value = [];
    operationList.value = [];
    
    // 清空按钮权限
    Object.keys(inTableRowBtn).forEach(key => {
      delete inTableRowBtn[key];
    });
    Object.keys(showPermisBtn).forEach(key => {
      delete showPermisBtn[key];
    });
    
    // 调用自定义清除方法
    if (globalPermissionHandlers.clearPermissions) {
      globalPermissionHandlers.clearPermissions();
    }
  };

  /**
   * 创建权限检查器
   * @returns {object} 权限检查器
   */
  const createChecker = () => {
    return createPermissionChecker(userPermissions.value);
  };

  /**
   * 批量检查权限
   * @param {Array} permissions - 权限列表
   * @returns {object} 权限检查结果
   */
  const batchCheckPermissions = (permissions) => {
    const results = {};
    
    permissions.forEach(permission => {
      results[permission] = checkPermission(permission);
    });
    
    return results;
  };

  return {
    // 响应式数据
    userPermissions,
    routePermissions,
    operationList,
    inTableRowBtn,
    showPermisBtn,
    globalPermissionHandlers,
    
    // 方法
    registerPermissionHandler,
    getUserPermissions,
    getRoutePermissionsData,
    checkPermission,
    renderOperation,
    setRowPermissions,
    getShowButtons,
    getAllButtons,
    clearPermissionCache,
    createChecker,
    batchCheckPermissions,
    
    // 工具方法
    hasPermission: (permission) => checkPermission(permission),
    hasAnyPermission: (permissions) => hasAnyPermission(permissions, userPermissions.value)
  };
}
