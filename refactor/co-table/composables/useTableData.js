/**
 * Co-Table 数据管理功能组合式函数
 * 提供数据加载、字典处理、搜索等功能
 */

import { ref, reactive, computed, watch, nextTick } from 'vue';
import { getType, deepClone } from '../utils/index.js';
import { getGlobalConfig } from '../config/index.js';

/**
 * 表格数据管理组合式函数
 * @param {object} props - 组件props
 * @param {Function} emit - emit函数
 * @param {object} tableState - 表格状态
 * @param {object} paginationState - 分页状态
 * @returns {object} 数据管理相关的响应式数据和方法
 */
export function useTableData(props, emit, tableState, paginationState) {
  // 响应式数据
  const searchFormData = reactive({});
  const dicEnumData = reactive({});
  const dicLoaded = ref(false);
  const dataLoading = ref(false);
  
  // 字典配置
  const dicConfig = getGlobalConfig('dictionary');
  
  // 计算属性
  const dicKeyArr = computed(() => {
    if (!props.dic) return [];
    return Object.keys(props.dic);
  });
  
  const dicEnum = computed(() => {
    const result = {};
    
    dicKeyArr.value.forEach(key => {
      const dicData = dicEnumData[key];
      if (dicData) {
        result[key] = {
          data: dicData.reduce((acc, item) => {
            const labelKey = dicConfig.keys.label;
            const valueKey = dicConfig.keys.value;
            acc[item[valueKey]] = item[labelKey];
            return acc;
          }, {}),
          color: dicData.reduce((acc, item) => {
            if (item.color) {
              acc[item[dicConfig.keys.value]] = item.color;
            }
            return acc;
          }, {})
        };
      }
    });
    
    return result;
  });

  /**
   * 初始化搜索表单数据
   * @param {object} searchConfig - 搜索配置
   */
  const initSearchForm = (searchConfig) => {
    if (!searchConfig || !searchConfig.items) return;
    
    // 清空现有数据
    Object.keys(searchFormData).forEach(key => {
      delete searchFormData[key];
    });
    
    // 初始化搜索字段
    searchConfig.items.forEach(item => {
      if (item.prop) {
        searchFormData[item.prop] = item.defaultValue || '';
      }
    });
  };

  /**
   * 加载字典数据
   * @param {object} dicConfig - 字典配置
   */
  const loadDictionary = async (dicConfig) => {
    if (!dicConfig) {
      dicLoaded.value = true;
      return;
    }
    
    const getDic = getGlobalConfig('dictionary.getDic');
    if (!getDic || typeof getDic !== 'function') {
      console.warn('字典获取方法未配置');
      dicLoaded.value = true;
      return;
    }
    
    try {
      const promises = Object.entries(dicConfig).map(async ([key, value]) => {
        let dicKey = value;
        
        // 处理 .json 后缀
        if (typeof value === 'string' && value.endsWith('.json')) {
          dicKey = value.replace(/\.json$/, '');
        }
        
        const data = await getDic(dicKey);
        dicEnumData[key] = Array.isArray(data) ? data : [];
      });
      
      await Promise.all(promises);
      dicLoaded.value = true;
    } catch (error) {
      console.error('字典数据加载失败:', error);
      dicLoaded.value = true;
    }
  };

  /**
   * 处理搜索事件
   * @param {object} searchResult - 搜索结果
   * @param {string} type - 搜索类型
   */
  const onSearch = (searchResult, type) => {
    if (type === 'reset') {
      // 重置分页
      paginationState.resetPagination();
    } else {
      // 搜索时重置到第一页
      paginationState.paginationData.current = 1;
    }
    
    // 发送搜索事件
    emit('search', {
      searchData: searchResult,
      type,
      pagination: { ...paginationState.paginationData }
    });
  };

  /**
   * 加载表格数据
   * @param {object} params - 请求参数
   */
  const loadTableData = async (params = {}) => {
    if (!props.api || typeof props.api !== 'function') {
      console.warn('表格数据加载API未配置');
      return;
    }
    
    dataLoading.value = true;
    tableState.loading.value = true;
    
    try {
      // 合并搜索参数和分页参数
      const requestParams = {
        ...searchFormData,
        ...paginationState.getRequestParams(),
        ...params
      };
      
      // 过滤空参数
      if (props.filterNullParams !== false) {
        tableState.filterNullParams(
          requestParams, 
          props.safeNullParams, 
          getType(props.safeNullParams)
        );
      }
      
      // 调用API
      const response = await props.api(requestParams);
      
      // 处理响应数据
      if (response) {
        // 更新分页信息
        paginationState.initPagination(response);
        
        // 提取表格数据
        const records = paginationState.extractRecords(response);
        tableState.initTableData(records);
        
        // 发送数据加载完成事件
        emit('data-loaded', {
          data: records,
          response,
          pagination: { ...paginationState.paginationData }
        });
      }
    } catch (error) {
      console.error('表格数据加载失败:', error);
      emit('data-error', error);
    } finally {
      dataLoading.value = false;
      tableState.loading.value = false;
    }
  };

  /**
   * 刷新表格数据
   * @param {boolean} resetPagination - 是否重置分页
   */
  const refreshData = (resetPagination = false) => {
    if (resetPagination) {
      paginationState.resetPagination();
    }
    
    loadTableData();
  };

  /**
   * 设置表格数据
   * @param {Array} data - 表格数据
   * @param {object} pagination - 分页信息
   */
  const setTableData = (data, pagination = {}) => {
    tableState.initTableData(data);
    
    if (Object.keys(pagination).length > 0) {
      paginationState.initPagination(pagination);
    }
  };

  /**
   * 添加表格行
   * @param {object} row - 行数据
   * @param {number} index - 插入位置，默认末尾
   */
  const addTableRow = (row, index = -1) => {
    const newRow = {
      ...row,
      _uuid: row._uuid || `new_${Date.now()}`,
      _selected: false
    };
    
    if (index === -1) {
      tableState.tableData.value.push(newRow);
    } else {
      tableState.tableData.value.splice(index, 0, newRow);
    }
    
    // 重新设置索引
    tableState.tableData.value.forEach((item, idx) => {
      item._index = idx;
      item._propPath = `data[${idx}]`;
    });
    
    emit('row-added', { row: newRow, index });
  };

  /**
   * 删除表格行
   * @param {number|object} target - 行索引或行数据
   */
  const removeTableRow = (target) => {
    let index = -1;
    let row = null;
    
    if (typeof target === 'number') {
      index = target;
      row = tableState.tableData.value[index];
    } else {
      const rowKey = tableState.currentRowKey.value;
      index = tableState.tableData.value.findIndex(item => item[rowKey] === target[rowKey]);
      row = target;
    }
    
    if (index > -1) {
      tableState.tableData.value.splice(index, 1);
      
      // 重新设置索引
      tableState.tableData.value.forEach((item, idx) => {
        item._index = idx;
        item._propPath = `data[${idx}]`;
      });
      
      emit('row-removed', { row, index });
    }
  };

  /**
   * 更新表格行
   * @param {number|object} target - 行索引或行数据
   * @param {object} newData - 新数据
   */
  const updateTableRow = (target, newData) => {
    let index = -1;
    let row = null;
    
    if (typeof target === 'number') {
      index = target;
      row = tableState.tableData.value[index];
    } else {
      const rowKey = tableState.currentRowKey.value;
      index = tableState.tableData.value.findIndex(item => item[rowKey] === target[rowKey]);
      row = tableState.tableData.value[index];
    }
    
    if (index > -1 && row) {
      Object.assign(row, newData);
      emit('row-updated', { row, index, newData });
    }
  };

  /**
   * 获取表格数据
   * @returns {Array} 表格数据
   */
  const getTableData = () => {
    return tableState.tableData.value;
  };

  // 监听字典配置变化
  watch(
    () => props.dic,
    (newDic) => {
      if (newDic) {
        loadDictionary(newDic);
      }
    },
    { immediate: true }
  );

  // 监听搜索配置变化
  watch(
    () => props.search,
    (newSearch) => {
      if (newSearch) {
        initSearchForm(newSearch);
      }
    },
    { immediate: true }
  );

  return {
    // 响应式数据
    searchFormData,
    dicEnumData,
    dicLoaded,
    dataLoading,
    dicKeyArr,
    dicEnum,
    
    // 方法
    initSearchForm,
    loadDictionary,
    onSearch,
    loadTableData,
    refreshData,
    setTableData,
    addTableRow,
    removeTableRow,
    updateTableRow,
    getTableData
  };
}
