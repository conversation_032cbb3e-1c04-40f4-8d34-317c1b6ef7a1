/**
 * Co-Table 表格操作功能组合式函数
 * 提供按钮操作、权限控制、事件分发等功能
 */

import { ref, reactive, computed } from 'vue';
import { getType } from '../utils/index.js';
import { getGlobalConfig } from '../config/index.js';
import { filterButtons, parseButtonRule } from '../utils/permission.js';

/**
 * 表格操作功能组合式函数
 * @param {object} props - 组件props
 * @param {Function} emit - emit函数
 * @param {object} tableState - 表格状态
 * @returns {object} 操作相关的响应式数据和方法
 */
export function useTableOperation(props, emit, tableState) {
  // 响应式数据
  const buttonLoadingStates = reactive({});
  const operationConfig = getGlobalConfig('operation');
  
  // 计算属性
  const operationOpts = computed(() => {
    return Object.assign({}, operationConfig.default, props.operationOpts || {});
  });
  
  const moreConfig = computed(() => {
    return Object.assign({}, operationConfig.more, operationOpts.value.more || {});
  });

  /**
   * 设置按钮加载状态
   * @param {string} buttonKey - 按钮标识
   * @param {boolean} loading - 加载状态
   */
  const setButtonLoading = (buttonKey, loading) => {
    buttonLoadingStates[buttonKey] = loading;
  };

  /**
   * 获取按钮加载状态
   * @param {string} buttonKey - 按钮标识
   * @returns {boolean} 加载状态
   */
  const getButtonLoading = (buttonKey) => {
    return buttonLoadingStates[buttonKey] || false;
  };

  /**
   * 过滤按钮权限
   * @param {Array} buttons - 按钮列表
   * @param {object} row - 行数据
   * @param {Array} userPermissions - 用户权限
   * @returns {Array} 过滤后的按钮列表
   */
  const filterButtonPermissions = (buttons, row = {}, userPermissions = []) => {
    if (!Array.isArray(buttons)) return [];
    
    return filterButtons(buttons, {
      userPermissions,
      row,
      storage: props.storage || {},
      store: props.store || null,
      tableKey: tableState.tableKey.value
    });
  };

  /**
   * 处理按钮点击事件
   * @param {object} eventData - 事件数据
   */
  const dispatchHandle = (eventData) => {
    const { field, btn, row, index, id, type = 'button' } = eventData;
    
    // 设置按钮加载状态
    if (btn && btn.loading !== false) {
      const buttonKey = `${field}_${row ? row[tableState.currentRowKey.value] : 'global'}`;
      setButtonLoading(buttonKey, true);
      
      // 自动清除加载状态（防止忘记清除）
      setTimeout(() => {
        setButtonLoading(buttonKey, false);
      }, 30000);
    }
    
    // 构建事件参数
    const eventParams = {
      field,
      btn,
      row,
      index,
      id,
      type,
      tableId: tableState.tableKey.value,
      selectedData: tableState.selectedData.value,
      tableData: tableState.tableData.value
    };
    
    // 发送事件
    emit('handle', eventParams);
    
    // 如果有自定义处理函数，也调用它
    if (props.handle && typeof props.handle === 'function') {
      props.handle(eventParams);
    }
  };

  /**
   * 处理上传成功事件
   * @param {any} result - 上传结果
   * @param {object} row - 行数据
   * @param {object} item - 列配置
   * @param {number} index - 行索引
   */
  const uploadSuccess = (result, row, item, index) => {
    // 更新行数据
    if (item.prop && result) {
      row[item.prop] = result;
    }
    
    // 发送上传成功事件
    emit('upload-success', {
      result,
      row,
      item,
      index,
      tableId: tableState.tableKey.value
    });
  };

  /**
   * 处理下载事件
   * @param {object} eventData - 事件数据
   */
  const onDownLoad = (eventData) => {
    const { field, row, index } = eventData;
    
    // 发送下载事件
    emit('download', {
      field,
      row,
      index,
      tableId: tableState.tableKey.value
    });
    
    // 如果有全局下载方法，调用它
    const globalDownloadMethod = getGlobalConfig('download.method');
    if (globalDownloadMethod && typeof globalDownloadMethod === 'function') {
      globalDownloadMethod(eventData);
    }
  };

  /**
   * 获取显示的按钮列表
   * @param {Array} buttons - 原始按钮列表
   * @param {object} row - 行数据
   * @returns {object} 包含显示按钮和更多按钮的对象
   */
  const getDisplayButtons = (buttons, row = {}) => {
    if (!Array.isArray(buttons)) {
      return { showButtons: [], moreButtons: [] };
    }
    
    // 过滤权限
    const filteredButtons = filterButtonPermissions(buttons, row, props.userPermissions || []);
    
    // 根据显示数量分割按钮
    const showCount = operationOpts.value.showCount;
    
    if (showCount === -1 || filteredButtons.length <= showCount) {
      return { showButtons: filteredButtons, moreButtons: [] };
    }
    
    return {
      showButtons: filteredButtons.slice(0, showCount),
      moreButtons: filteredButtons.slice(showCount)
    };
  };

  /**
   * 获取按钮的完整属性
   * @param {object} button - 按钮配置
   * @param {object} row - 行数据
   * @returns {object} 按钮属性
   */
  const getButtonProps = (button, row = {}) => {
    const buttonKey = `${button.mark}_${row[tableState.currentRowKey.value] || 'global'}`;
    const loading = getButtonLoading(buttonKey);
    
    return {
      ...button,
      loading: loading || button.loading || false,
      disabled: button.disabled || loading
    };
  };

  /**
   * 处理表格内表单事件
   * @param {object} eventData - 事件数据
   */
  const handleTableFormEvent = (eventData) => {
    const { handle, type, prop, row, index, value, formRef } = eventData;
    
    // 发送表单事件
    emit('form-change', {
      handle,
      type,
      prop,
      row,
      index,
      value,
      formRef,
      tableId: tableState.tableKey.value
    });
    
    // 如果有自定义处理函数，也调用它
    if (props.formHandle && typeof props.formHandle === 'function') {
      props.formHandle(eventData);
    }
  };

  /**
   * 清除按钮加载状态
   * @param {string} buttonKey - 按钮标识，如果不提供则清除所有
   */
  const clearButtonLoading = (buttonKey) => {
    if (buttonKey) {
      delete buttonLoadingStates[buttonKey];
    } else {
      Object.keys(buttonLoadingStates).forEach(key => {
        delete buttonLoadingStates[key];
      });
    }
  };

  /**
   * 批量设置按钮状态
   * @param {object} states - 状态对象 { buttonKey: loading }
   */
  const setBatchButtonLoading = (states) => {
    Object.assign(buttonLoadingStates, states);
  };

  return {
    // 响应式数据
    buttonLoadingStates,
    operationOpts,
    moreConfig,
    
    // 方法
    setButtonLoading,
    getButtonLoading,
    filterButtonPermissions,
    dispatchHandle,
    uploadSuccess,
    onDownLoad,
    getDisplayButtons,
    getButtonProps,
    handleTableFormEvent,
    clearButtonLoading,
    setBatchButtonLoading
  };
}
