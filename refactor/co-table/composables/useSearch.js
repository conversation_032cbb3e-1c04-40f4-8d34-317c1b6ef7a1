/**
 * Co-Table 搜索功能组合式函数
 * 提供搜索表单的数据管理、事件处理和业务逻辑
 */

import { ref, reactive, computed, provide, onMounted } from 'vue';
import { getType, deepClone } from '../utils/index.js';
import { getGlobalConfig } from '../config/index.js';
import { DATE_TYPES } from '../config/enums.js';

/**
 * 搜索功能组合式函数
 * @param {object} props - 组件props
 * @param {Function} emit - emit函数
 * @returns {object} 搜索相关的响应式数据和方法
 */
export function useSearch(props, emit) {
  // 响应式数据
  const searchFormRef = ref(null);
  const widgetItem = reactive({});
  const oldModel = ref({});
  
  // 配置数据
  const searchConfig = getGlobalConfig('search');
  const searchProps = computed(() => searchConfig.searchButton);
  const resetProps = computed(() => searchConfig.resetButton);
  
  // 样式配置
  const styles = computed(() => {
    return props.config?.styles || searchConfig.style;
  });
  
  const styleName = computed(() => {
    return typeof styles.value === 'string' ? 'class' : 'style';
  });

  // 提供给子组件的数据
  provide('widgetItem', widgetItem);

  /**
   * 获取组件类型
   * @param {object} item - 表单项配置
   * @returns {string} 组件类型
   */
  const getComponentType = (item) => {
    if (DATE_TYPES.includes(item.type)) {
      return 'date';
    }
    return item.type;
  };

  /**
   * 处理表单项变化
   * @param {object} changeData - 变化数据 {prop, value}
   */
  const onItemChange = ({ prop, value }) => {
    props.model[prop] = value;
    
    // 如果是场景模式，发送 update:model 事件
    if (props.scene) {
      emit('update:model', props.model);
    }
    
    emit('change', { prop, value });
  };

  /**
   * 处理搜索和重置操作
   * @param {string} type - 操作类型：'search' | 'reset'
   */
  const onHandle = (type = 'search') => {
    const formModelKeys = Object.keys(props.model);
    const searchResult = {};

    for (const widget of formModelKeys) {
      if (type === 'reset') {
        // 重置操作
        const resetValue = oldModel.value ? oldModel.value[widget] : '';
        
        // 调用组件的重置方法
        if (widgetItem[widget]) {
          widgetItem[widget].resetField(resetValue);
        }
        
        props.model[widget] = resetValue;
      } else {
        // 搜索操作
        const hasPrepend = widget.includes('_prepend');
        
        if (hasPrepend) {
          // 处理带前置选择器的字段
          handlePrependField(widget, searchResult);
        } else if (!widgetItem[widget]?.item.prepend || 
                   !widgetItem[widget]?.item.prepend.prop.includes('_prepend')) {
          // 处理普通字段
          searchResult[widget] = props.model[widget];
        }
      }
    }

    // 发送搜索事件
    emit('search', searchResult, type);
  };

  /**
   * 处理带前置选择器的字段
   * @param {string} widget - 字段名
   * @param {object} searchResult - 搜索结果对象
   */
  const handlePrependField = (widget, searchResult) => {
    if (props.model[widget]) {
      const mainProp = widget.replace(/\_prepend$/, '');
      
      // 检查是否为日期类型组件
      if (widgetItem[mainProp]?.isDateType) {
        const splitProp = widgetItem[mainProp]?.item.splitProp;
        
        if (splitProp && getType(splitProp) === 'Array') {
          // 有分割字段的日期范围处理
          if (props.model[mainProp]?.length) {
            searchResult[`${props.model[widget]}${splitProp[0]}`] = props.model[mainProp][0] ?? '';
            searchResult[`${props.model[widget]}${splitProp[1]}`] = props.model[mainProp][1] ?? '';
          }
        } else {
          // 没有分割字段的日期处理
          const joinChar = splitProp || ',';
          searchResult[props.model[widget]] = props.model[mainProp].join(joinChar);
        }
      } else {
        // 非日期类型字段
        if (props.model[mainProp]) {
          searchResult[props.model[widget]] = props.model[mainProp];
        }
      }
    } else {
      // 清除缓存的键
      if (widgetItem[widget]) {
        delete searchResult[widgetItem[widget].cacheKey];
        widgetItem[widget].cacheKey = undefined;
      }
    }
  };

  /**
   * 设置表单数据
   * @param {object} data - 要设置的数据
   */
  const setData = (data) => {
    const formModelKeys = Object.keys(props.model);
    
    for (const [key, value] of Object.entries(data)) {
      // 调用组件的重置方法
      if (widgetItem[key]) {
        widgetItem[key].resetField(value);
      }
      
      // 设置表单数据
      if (formModelKeys.includes(key)) {
        props.model[key] = value || '';
      } else {
        props.model[key] = value || '';
      }
    }
  };

  /**
   * 验证表单
   * @param {Function} callback - 回调函数
   * @returns {Promise} 验证结果
   */
  const validate = (callback) => {
    if (!searchFormRef.value) {
      console.warn('搜索表单引用不存在');
      return Promise.resolve(false);
    }
    
    if (callback && typeof callback === 'function') {
      return searchFormRef.value.validate(callback);
    }
    
    return searchFormRef.value.validate();
  };

  /**
   * 重置表单
   */
  const resetFields = () => {
    if (!searchFormRef.value) {
      console.warn('搜索表单引用不存在');
      return;
    }
    
    searchFormRef.value.resetFields();
  };

  /**
   * 清除验证
   * @param {string|Array} props - 要清除验证的字段
   */
  const clearValidate = (props) => {
    if (!searchFormRef.value) {
      console.warn('搜索表单引用不存在');
      return;
    }
    
    searchFormRef.value.clearValidate(props);
  };

  // 初始化
  onMounted(() => {
    // 保存初始模型数据
    oldModel.value = deepClone(props.model);
  });

  return {
    // 响应式数据
    searchFormRef,
    widgetItem,
    searchProps,
    resetProps,
    styles,
    styleName,
    
    // 方法
    getComponentType,
    onItemChange,
    onHandle,
    setData,
    validate,
    resetFields,
    clearValidate
  };
}
