/**
 * Co-Table 核心表格功能组合式函数
 * 提供表格数据管理、选择、分页、操作等核心功能
 */

import { ref, reactive, computed, nextTick, onMounted } from 'vue';
import { getType, generateUUID } from '../utils/index.js';
import { getGlobalConfig } from '../config/index.js';
import { filterButtons } from '../utils/permission.js';

/**
 * 表格核心功能组合式函数
 * @param {object} props - 组件props
 * @param {Function} emit - emit函数
 * @returns {object} 表格相关的响应式数据和方法
 */
export function useTable(props, emit) {
  // 响应式数据
  const tableRef = ref(null);
  const tableFormRef = ref(null);
  const selectedData = ref([]);
  const singleData = ref('');
  const tableData = ref([]);
  const loading = ref(false);
  
  // 表格配置
  const tableConfig = getGlobalConfig('table');
  const paginationConfig = getGlobalConfig('pagination');
  
  // 计算属性
  const tableKey = computed(() => props.tableKey || generateUUID());
  const currentRowKey = computed(() => props.rowKey || 'id');
  const childrenKey = computed(() => props.childrenKey || 'children');
  const singleMode = computed(() => props.singleMode || false);
  
  // 合并表格属性
  const mergeAttrs = computed(() => {
    const defaultAttrs = tableConfig.attrs || {};
    const userAttrs = props.tableAttrs || {};
    return Object.assign({}, defaultAttrs, userAttrs);
  });

  /**
   * 初始化表格数据
   * @param {Array} data - 原始数据
   */
  const initTableData = (data = []) => {
    if (!Array.isArray(data)) {
      console.warn('表格数据必须是数组格式');
      return;
    }
    
    tableData.value = data.map((item, index) => ({
      ...item,
      _uuid: item._uuid || generateUUID(),
      _index: index,
      _selected: false,
      _propPath: `data[${index}]`
    }));
  };

  /**
   * 设置行选中状态
   * @param {object} row - 目标行数据
   * @param {boolean} selected - 选中状态
   */
  const toggleRowSelection = (row, selected = true) => {
    if (!tableRef.value) return;
    
    const cRowKey = currentRowKey.value;
    const childKey = childrenKey.value;
    
    if (singleMode.value) {
      singleData.value = row[cRowKey];
      selectedData.value = [row];
    } else {
      const setToggleRow = (row, selected) => {
        row._selected = selected;
        
        if (selected) {
          if (!row[childKey]) {
            selectedData.value.push(row);
          }
        } else {
          const index = selectedData.value.findIndex(item => item[cRowKey] === row[cRowKey]);
          if (index > -1) {
            selectedData.value.splice(index, 1);
          }
        }
        
        // 处理子级数据
        if (row[childKey]) {
          for (let child of row[childKey]) {
            setToggleRow(child, selected);
          }
        }
      };
      
      setToggleRow(row, selected);
    }
    
    // 调用Element Plus表格的方法
    tableRef.value.toggleRowSelection(row, selected);
  };

  /**
   * 获取行索引
   * @param {object} row - 行数据
   * @returns {number} 行索引
   */
  const getRowIndex = (row) => {
    const crowKey = currentRowKey.value;
    const childKey = childrenKey.value;
    
    if (row[childKey] && row[childKey].length) {
      return row._index;
    }
    
    return tableData.value.findIndex(item => item[crowKey] === row[crowKey]);
  };

  /**
   * 初始化表格事件监听器
   * @param {object} listeners - 事件监听器对象
   * @returns {object} 处理后的监听器对象
   */
  const initListeners = (listeners = {}) => {
    const tableKeyValue = tableKey.value;
    
    return Object.assign(listeners, {
      'select-all': (selection) => {
        tableData.value.forEach(row => {
          row._selected = !!selection.length ? !row._selected : false;
        });
        selectedData.value = selection;
        emit('select-all', { selection, tableId: tableKeyValue });
      },
      
      select: (selection, row) => {
        row._selected = !row._selected;
        selectedData.value = selection;
        emit('select', { 
          selection, 
          row, 
          index: getRowIndex(row), 
          tableId: tableKeyValue 
        });
      },
      
      'row-click': (row, column, event) => {
        if (column.type === 'selection') return;
        event.stopPropagation();
        
        row._selected = !row._selected;
        const index = getRowIndex(row);
        
        if (props.currentRow) {
          if (singleMode.value) {
            handleSingleClick('', row, index, true);
          } else {
            tableRef.value.toggleRowSelection(row, row._selected);
          }
        }
        
        emit('row-click', { 
          row, 
          column, 
          event, 
          index, 
          tableId: tableKeyValue 
        });
      },
      
      'selection-change': (selection) => {
        selectedData.value = selection;
        emit('selection-change', selection);
      }
    });
  };

  /**
   * 处理单选点击
   * @param {string} type - 类型
   * @param {object} row - 行数据
   * @param {number} index - 索引
   * @param {boolean} isRowClick - 是否为行点击
   */
  const handleSingleClick = (type, row, index, isRowClick = false) => {
    if (singleMode.value) {
      singleData.value = row[currentRowKey.value];
      selectedData.value = [row];
    }
    
    emit('single-click', { type, row, index, isRowClick });
  };

  /**
   * 过滤空参数
   * @param {object} queryData - 查询数据
   * @param {boolean|Array} safeNullParams - 安全空参数配置
   * @param {string} paramsType - 参数类型
   */
  const filterNullParams = (queryData, safeNullParams, paramsType) => {
    if (paramsType === 'Boolean' && !safeNullParams) {
      for (let [key, value] of Object.entries(queryData)) {
        if (value === '' || value === undefined || value === null) {
          delete queryData[key];
        }
      }
      return;
    }
    
    if (paramsType === 'Array') {
      const paramsArr = Object.keys(queryData);
      const removeParams = paramsArr.filter(item => !safeNullParams.includes(item));
      
      for (let key of removeParams) {
        if (queryData[key] === undefined || queryData[key] === null || queryData[key] === '') {
          delete queryData[key];
        }
      }
    }
  };

  /**
   * 根据属性名获取结果
   * @param {string} propsName - 属性名
   * @param {any} data - 数据
   * @returns {any} 结果
   */
  const getResultByProps = (propsName, data) => {
    const attrsProp = props[propsName];
    
    if (!attrsProp) return data;
    
    const propType = getType(attrsProp);
    
    if (propType === 'String') {
      return data[attrsProp];
    }
    
    if (propType === 'Function') {
      return attrsProp(data);
    }
    
    return data;
  };

  /**
   * 设置属性
   * @param {object} target - 目标对象
   * @param {object} data - 数据
   */
  const setProp = (target, data) => {
    const entriesData = Object.entries(data);
    for (const [key, value] of entriesData) {
      target[key] = value;
    }
  };

  /**
   * 刷新表格数据
   * @param {Array} newData - 新数据
   */
  const refresh = (newData) => {
    if (newData) {
      initTableData(newData);
    }
    
    // 清空选中状态
    selectedData.value = [];
    singleData.value = '';
    
    // 重置表格状态
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value.clearSelection();
      }
    });
    
    emit('refresh');
  };

  /**
   * 获取选中的数据
   * @returns {Array} 选中的数据
   */
  const getSelectedData = () => {
    return selectedData.value;
  };

  /**
   * 清空选中状态
   */
  const clearSelection = () => {
    selectedData.value = [];
    singleData.value = '';
    
    if (tableRef.value) {
      tableRef.value.clearSelection();
    }
  };

  return {
    // 响应式数据
    tableRef,
    tableFormRef,
    selectedData,
    singleData,
    tableData,
    loading,
    tableKey,
    currentRowKey,
    childrenKey,
    singleMode,
    mergeAttrs,
    
    // 方法
    initTableData,
    toggleRowSelection,
    getRowIndex,
    initListeners,
    handleSingleClick,
    filterNullParams,
    getResultByProps,
    setProp,
    refresh,
    getSelectedData,
    clearSelection
  };
}
