{"name": "co-table-refactor", "version": "2.0.0", "description": "Co-Table 组件重构版本 - 从 Options API 迁移到 Composition API", "main": "index.vue", "type": "module", "keywords": ["vue3", "composition-api", "table", "element-plus", "co-table"], "author": "Claude 4.0 sonnet", "license": "MIT", "peerDependencies": {"vue": "^3.0.0", "element-plus": "^2.0.0"}, "devDependencies": {"sass": "^1.0.0"}, "files": ["index.vue", "components/", "composables/", "utils/", "config/", "styles/", "docs/"], "exports": {".": "./index.vue", "./components/*": "./components/*", "./composables/*": "./composables/*", "./utils/*": "./utils/*", "./config/*": "./config/*", "./styles/*": "./styles/*"}, "scripts": {"dev": "echo 'Development mode'", "build": "echo 'Build process'", "test": "echo 'Test suite'"}, "repository": {"type": "git", "url": "D:/project/work/CRUD"}, "bugs": {"url": "D:/project/work/CRUD/issues"}, "homepage": "D:/project/work/CRUD#readme"}