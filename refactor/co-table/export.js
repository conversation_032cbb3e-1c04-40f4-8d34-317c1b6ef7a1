/**
 * Co-Table 组件导出文件
 * 提供统一的组件导出和安装方法
 */

// 导入主组件
import CoTableMain from './index.vue';
import CoTable from './co-table.vue';
import PageTable from './page-table.vue';

// 导入子组件
import CoTableSearch from './components/co-table-search.vue';
import CoTableButton from './components/co-table-button.vue';
import CoTableContainer from './components/co-table-container.vue';
import CoTableUpload from './components/co-table-upload.vue';
import CoTableStatic from './components/co-table-static.vue';

// 导入表单组件
import {
  CoTableInput,
  CoTableSelect,
  CoTableDate,
  CoTableSwitch
} from './components/form-items/index.js';

// 导入组合式函数
import { useTable } from './composables/useTable.js';
import { useTableData } from './composables/useTableData.js';
import { useTableOperation } from './composables/useTableOperation.js';
import { useTablePagination } from './composables/useTablePagination.js';
import { usePermission } from './composables/usePermission.js';
import { useSearch } from './composables/useSearch.js';

// 导入工具函数
import * as utils from './utils/index.js';
import * as formatter from './utils/formatter.js';
import * as permission from './utils/permission.js';
import { permissionManager } from './utils/permission-manager.js';

// 导入配置
import { 
  getGlobalConfig, 
  setGlobalConfig, 
  resetGlobalConfig,
  mergeConfig,
  createConfigManager
} from './config/index.js';

import * as enums from './config/enums.js';

// 组件列表
const components = {
  CoTableMain,
  CoTable,
  PageTable,
  CoTableSearch,
  CoTableButton,
  CoTableContainer,
  CoTableUpload,
  CoTableStatic,
  CoTableInput,
  CoTableSelect,
  CoTableDate,
  CoTableSwitch
};

// 组合式函数列表
const composables = {
  useTable,
  useTableData,
  useTableOperation,
  useTablePagination,
  usePermission,
  useSearch
};

// 工具函数列表
const utilities = {
  ...utils,
  formatter,
  permission,
  permissionManager
};

// 配置管理
const config = {
  getGlobalConfig,
  setGlobalConfig,
  resetGlobalConfig,
  mergeConfig,
  createConfigManager
};

/**
 * 安装插件
 * @param {object} app - Vue应用实例
 * @param {object} options - 安装选项
 */
const install = (app, options = {}) => {
  // 注册组件
  Object.keys(components).forEach(name => {
    app.component(name, components[name]);
  });
  
  // 设置全局配置
  if (options.config) {
    setGlobalConfig(options.config);
  }
  
  // 注册权限处理器
  if (options.permissionHandlers) {
    Object.entries(options.permissionHandlers).forEach(([type, handler]) => {
      permissionManager.registerHandler(type, handler);
    });
  }
  
  // 提供全局属性
  app.config.globalProperties.$coTable = {
    config,
    utils: utilities,
    permissionManager
  };
  
  console.log('Co-Table 组件安装完成');
};

// 默认导出
export default {
  install,
  ...components
};

// 具名导出
export {
  // 主组件
  CoTableMain,
  CoTable,
  PageTable,
  
  // 子组件
  CoTableSearch,
  CoTableButton,
  CoTableContainer,
  CoTableUpload,
  CoTableStatic,
  CoTableInput,
  CoTableSelect,
  CoTableDate,
  CoTableSwitch,
  
  // 组合式函数
  useTable,
  useTableData,
  useTableOperation,
  useTablePagination,
  usePermission,
  useSearch,
  
  // 工具函数
  utils,
  formatter,
  permission,
  permissionManager,
  
  // 配置管理
  config,
  getGlobalConfig,
  setGlobalConfig,
  resetGlobalConfig,
  mergeConfig,
  createConfigManager,
  
  // 枚举
  enums,
  
  // 安装函数
  install
};
