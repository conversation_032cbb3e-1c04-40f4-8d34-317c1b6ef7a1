<template>
  <el-upload
    class="co-table-upload"
    v-bind="uploadAttrs"
    action=""
    :multiple="false"
    list-type="text"
    :auto-upload="true"
    :limit="1"
    :drag="false"
    :show-file-list="false"
    :disabled="uploading || uploadAttrs.disabled"
    :before-upload="beforeUpload"
    :http-request="httpRequest"
  >
    <el-button 
      v-if="showButton"
      type="text" 
      :class="buttonClass"
      :style="buttonStyle"
      :loading="uploading"
    >
      {{ uploadAttrs.text || '上传文件' }}
    </el-button>
    <slot v-else />
  </el-upload>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getType } from '../utils/index.js';
import { getGlobalConfig } from '../config/index.js';

// Props
const props = defineProps({
  // 上传方法
  methodFn: {
    type: Function,
    default: null
  },
  // 文件类型限制
  accept: {
    type: String,
    default: '*'
  },
  // 文件大小限制（MB）
  size: {
    type: Number,
    default: 10
  },
  // 按钮文本
  text: {
    type: String,
    default: '上传文件'
  },
  // 按钮样式
  styles: {
    type: [String, Object],
    default: ''
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 链接属性
  linkProps: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['onSuccess', 'onError', 'onProgress']);

// Reactive data
const uploading = ref(false);

// 计算上传属性
const uploadAttrs = computed(() => {
  const attrs = { ...props };
  // 移除不需要传递给 el-upload 的属性
  delete attrs.methodFn;
  delete attrs.linkProps;
  return attrs;
});

// 计算是否显示按钮
const showButton = computed(() => {
  return typeof props.styles === 'string';
});

// 计算按钮样式类
const buttonClass = computed(() => {
  return typeof props.styles === 'string' ? props.styles : '';
});

// 计算按钮内联样式
const buttonStyle = computed(() => {
  return typeof props.styles === 'object' ? props.styles : {};
});

// 文件上传前的检查
const beforeUpload = (file) => {
  // 检查文件类型
  if (props.accept && props.accept !== '*') {
    const extFileName = file.name.substring(file.name.lastIndexOf('.'));
    const uploadFileTypes = props.accept.split(',').map(type => type.trim());
    
    if (uploadFileTypes.length > 0 && !uploadFileTypes.includes(extFileName)) {
      ElMessage.error('不支持的文件类型');
      return false;
    }
  }
  
  // 检查文件大小
  const maxSize = props.size || 10;
  const fileSizeCheckResult = file.size / 1024 / 1024 <= maxSize;
  
  if (!fileSizeCheckResult) {
    ElMessage.error(`已超出文件大小，不能大于(${maxSize}MB)`);
    return false;
  }
  
  uploading.value = true;
  return true;
};

// HTTP 请求处理
const httpRequest = async (uploadData) => {
  try {
    // 获取上传方法
    const globalUploadMethod = getGlobalConfig('upload.method');
    const uploadMethod = props.methodFn || globalUploadMethod;
    
    if (!uploadMethod) {
      throw new Error('upload: global upload and the custom upload at least one');
    }
    
    if (getType(uploadMethod) !== 'Function') {
      throw new Error('upload: parameter is wrong, should be function');
    }
    
    // 执行上传
    const result = await uploadMethod(uploadData);
    
    // 发送成功事件
    emit('onSuccess', result);
    
    return result;
  } catch (error) {
    console.error('文件上传失败:', error);
    ElMessage.error('文件上传失败');
    
    // 发送错误事件
    emit('onError', error);
    
    throw error;
  } finally {
    uploading.value = false;
  }
};

// 手动触发上传
const submit = () => {
  // 这里可以添加手动上传的逻辑
};

// 清空文件列表
const clearFiles = () => {
  // 这里可以添加清空文件的逻辑
};

// 暴露方法给父组件
defineExpose({
  submit,
  clearFiles,
  uploading: uploading
});
</script>

<style scoped>
/* 上传组件样式 */
.co-table-upload {
  display: inline-block;
}

.co-table-upload :deep(.el-upload) {
  display: inline-block;
}

/* 上传按钮样式 */
.co-table-upload .el-button {
  margin: 0;
  padding: 0 8px;
  height: auto;
  line-height: 1.5;
  border: none;
  background: none;
  color: var(--el-color-primary);
  font-size: inherit;
}

.co-table-upload .el-button:hover {
  color: var(--el-color-primary-light-3);
  background: none;
}

.co-table-upload .el-button:focus {
  color: var(--el-color-primary);
  background: none;
}

/* 加载状态样式 */
.co-table-upload .el-button.is-loading {
  color: var(--el-color-primary);
}

.co-table-upload .el-button.is-loading .el-icon {
  margin-right: 4px;
}

/* 禁用状态样式 */
.co-table-upload .el-button.is-disabled {
  color: var(--el-text-color-disabled);
  cursor: not-allowed;
}

/* 自定义样式类支持 */
.co-table-upload .upload-btn-primary {
  color: var(--el-color-primary);
}

.co-table-upload .upload-btn-success {
  color: var(--el-color-success);
}

.co-table-upload .upload-btn-warning {
  color: var(--el-color-warning);
}

.co-table-upload .upload-btn-danger {
  color: var(--el-color-danger);
}

.co-table-upload .upload-btn-info {
  color: var(--el-color-info);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .co-table-upload .el-button {
    padding: 4px 8px;
    font-size: 14px;
  }
}
</style>
