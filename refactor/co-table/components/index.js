/**
 * Co-Table 组件入口文件
 * 统一导出所有组件
 */

// 基础组件
import CoTableButton from './co-table-button.vue';
import CoTableContainer from './co-table-container.vue';
import CoTableUpload from './co-table-upload.vue';
import CoTableStatic from './co-table-static.vue';

// 表单组件
import {
  CoTableInput,
  CoTableSelect,
  CoTableDate,
  CoTableSwitch,
  formComponents,
  getFormComponent,
  isDateType
} from './form-items/index.js';

// 组件映射表
export const components = {
  // 基础组件
  'co-table-button': CoTableButton,
  'co-table-container': CoTableContainer,
  'co-table-upload': CoTableUpload,
  'co-table-static': CoTableStatic,
  
  // 表单组件
  'co-table-input': CoTableInput,
  'co-table-select': CoTableSelect,
  'co-table-date': CoTableDate,
  'co-table-switch': CoTableSwitch,
  
  // 向后兼容的别名
  'co-button': CoTableButton,
  'co-container': CoTableContainer,
  'co-upload': CoTableUpload,
  'static-component': CoTableStatic,
  
  // 表单组件别名
  ...formComponents
};

// 获取组件
export function getComponent(name) {
  return components[name] || null;
}

// 检查组件是否存在
export function hasComponent(name) {
  return name in components;
}

// 注册组件到 Vue 应用
export function installComponents(app) {
  Object.keys(components).forEach(name => {
    app.component(name, components[name]);
  });
}

// 导出单个组件
export {
  // 基础组件
  CoTableButton,
  CoTableContainer,
  CoTableUpload,
  CoTableStatic,
  
  // 表单组件
  CoTableInput,
  CoTableSelect,
  CoTableDate,
  CoTableSwitch,
  
  // 表单组件工具
  getFormComponent,
  isDateType
};

// 默认导出组件映射表
export default components;
