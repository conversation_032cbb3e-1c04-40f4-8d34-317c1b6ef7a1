<template>
  <el-select 
    v-model="formModel[item.prop]" 
    clearable 
    :placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" 
    v-bind="itemAttrs" 
    v-on="listeners"
  >
    <el-option 
      v-for="opt in optionList" 
      :key="opt[valueKey]" 
      :label="opt[labelKey]" 
      :value="opt[valueKey]" 
      :disabled="itemAttrs.disabled" 
    />
  </el-select>
</template>

<script setup>
import { reactive, inject, computed, watch, onMounted, ref } from 'vue';
import { getType } from '../../utils/index.js';
import { getGlobalConfig } from '../../config/index.js';
import { handleFormEvent, handleMultipleValue, initFormField } from '../../utils/form-helper.js';

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: null
  },
  row: {
    type: Object,
    default: null
  },
  scene: {
    type: String,
    default: ''
  },
  dic: {
    type: Object,
    default: () => null
  },
  slotName: {
    type: String,
    default: ''
  },
  mainProp: {
    type: String,
    default: ''
  },
  index: Number,
  handle: Function,
  formRef: Object,
  type: String
});

// Emits
const emit = defineEmits(['change']);

// Inject
const widgetItem = inject('widgetItem', {});

// Reactive data
const optionList = ref([]);
const listeners = reactive({});

// Computed
const formModel = computed(() => reactive(props.row || props.data || {}));
const isInTable = computed(() => props.scene === 'inTable');
const itemAttrs = computed(() => Object.assign(props.item.attrs || {}));

// 字典配置
const dicConfig = getGlobalConfig('dictionary');
const labelKey = ref(dicConfig.keys.label);
const valueKey = ref(dicConfig.keys.value);

// Setup computed item prop
const computedItem = computed(() => {
  const item = { ...props.item };
  if (props.mainProp) {
    item.prop = `${props.mainProp}_prepend`;
  }
  return item;
});

// Filter function
const filterOption = (options) => {
  const { filter } = props.item;
  return typeof filter === 'function' ? filter(options) : options;
};

// Initialize options
const initOption = async (item, optionKey, optionVal) => {
  const optionValueType = getType(optionVal);
  
  // 根据自定义 optionKey 参数设置 labelKey, valueKey
  if (optionKey) {
    [labelKey.value, valueKey.value] = optionKey;
  }
  
  try {
    switch (optionValueType) {
      case 'Array':
        optionList.value = filterOption(optionVal);
        break;
        
      case 'Function':
        const result = optionVal();
        if (getType(result) === 'Promise') {
          const data = await result;
          optionList.value = filterOption(data);
        } else {
          optionList.value = filterOption(result);
        }
        break;
        
      case 'AsyncFunction':
        const asyncData = await optionVal();
        optionList.value = filterOption(asyncData);
        break;
        
      case 'Promise':
        const promiseData = await optionVal;
        optionList.value = filterOption(promiseData);
        break;
        
      case 'String':
        // 处理 .json 字典值
        if (optionVal.endsWith('.json')) {
          const dicKey = optionVal.replace(/\.json$/, '');
          const getDic = getGlobalConfig('dictionary.getDic');
          if (getDic) {
            const dicData = await getDic(dicKey);
            optionList.value = filterOption(dicData);
          }
        }
        break;
        
      default:
        optionList.value = [];
    }
  } catch (error) {
    console.warn('选项数据加载失败:', error);
    optionList.value = [];
  }
};

// Setup events
const setupEvents = () => {
  // 清空现有监听器
  Object.keys(listeners).forEach(key => delete listeners[key]);
  
  // 添加默认 change 事件
  listeners.change = (value) => {
    handleFormEvent('change', value, isInTable.value, {
      item: computedItem.value,
      data: props.data,
      row: props.row,
      index: props.index,
      handle: props.handle,
      formRef: props.formRef,
      type: props.type
    }, itemAttrs.value, emit);
  };
  
  // 添加其他事件
  if (props.item.events) {
    Object.keys(props.item.events).forEach(eventName => {
      listeners[eventName] = () => {
        const currentRow = props.row || props.data;
        const value = isInTable.value ? currentRow[computedItem.value.prop] : props.data[props.item.prop];
        
        handleFormEvent(eventName, value, isInTable.value, {
          item: computedItem.value,
          data: props.data,
          row: props.row,
          index: props.index,
          handle: props.handle,
          formRef: props.formRef,
          type: props.type
        }, itemAttrs.value, emit);
      };
    });
  }
};

// Initialize form model
const initFormModel = () => {
  const defaultValue = itemAttrs.value.multiple ? [] : '';
  initFormField(formModel.value, computedItem.value, defaultValue);
  
  // 处理多选字段的值转换
  if (itemAttrs.value.multiple) {
    const currentValue = formModel.value[computedItem.value.prop];
    formModel.value[computedItem.value.prop] = handleMultipleValue(currentValue, true);
  }
};

// Methods
const resetField = (value = undefined) => {
  const defaultValue = value !== undefined ? value : (itemAttrs.value.multiple ? [] : '');
  formModel.value[computedItem.value.prop] = defaultValue;
};

// Watch dictionary changes
watch(
  () => props.dic,
  (newDic) => {
    if (newDic) {
      const { optionKey, option, attrs } = props.item;
      const optionVal = option || attrs?.option;
      
      if (optionVal && newDic[optionVal]) {
        initOption(props.item, optionKey, newDic[optionVal]);
      }
    }
  },
  { deep: true, immediate: true }
);

// Setup
onMounted(() => {
  // 注册到 widgetItem
  widgetItem[computedItem.value.prop] = { resetField };
  
  // 初始化
  initFormModel();
  setupEvents();
  
  // 初始化选项数据
  const { optionKey, option, attrs } = props.item;
  const optionVal = option || attrs?.option;
  if (optionVal) {
    initOption(props.item, optionKey, optionVal);
  }
});

// 暴露方法给父组件
defineExpose({
  resetField
});
</script>

<style scoped>
/* 选择器组件样式 */
:root {
  --el-input-border-radius: 4px;
}

.el-input-group--prepend .el-input__wrapper {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
