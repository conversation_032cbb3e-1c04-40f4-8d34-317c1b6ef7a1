<template>
  <div @click.stop>
    <el-switch 
      v-model="formModel[item.prop]" 
      v-bind="item.attrs" 
      :loading="loading" 
      :before-change="onBeforeChange" 
      @change="onChange" 
    />
  </div>
</template>

<script setup>
import { reactive, inject, computed, onMounted, ref } from 'vue';
import { handleFormEvent } from '../../utils/form-helper.js';

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: null
  },
  row: {
    type: Object,
    default: null
  },
  scene: {
    type: String,
    default: ''
  },
  index: Number,
  handle: Function,
  formRef: Object,
  type: String
});

// Emits
const emit = defineEmits(['change']);

// Inject
const widgetItem = inject('widgetItem', {});

// Reactive data
const loading = ref(false);

// Computed
const formModel = computed(() => reactive(props.row || props.data || {}));
const isInTable = computed(() => props.scene === 'inTable');
const currentRow = computed(() => props.data || props.row);

// Initialize form model
const initFormModel = () => {
  if (formModel.value[props.item.prop] === undefined) {
    formModel.value[props.item.prop] = false;
  }
};

// Methods
const resetField = (value = undefined) => {
  const defaultValue = value !== undefined ? value : false;
  formModel.value[props.item.prop] = defaultValue;
};

const onBeforeChange = async () => {
  const hasBeforeChange = props.item.attrs && props.item.attrs['before-change'];
  
  if (!hasBeforeChange) return true;
  
  loading.value = true;
  
  try {
    const result = await props.item.attrs['before-change'](currentRow.value);
    loading.value = false;
    
    // 触发 switch 事件
    handleFormEvent('switch', currentRow.value[props.item.prop], isInTable.value, {
      item: props.item,
      data: props.data,
      row: props.row,
      index: props.index,
      handle: props.handle,
      formRef: props.formRef,
      type: props.type
    }, props.item.attrs, emit);
    
    return result;
  } catch (error) {
    loading.value = false;
    console.warn('开关状态变更前置检查失败:', error);
    return false;
  }
};

const onChange = (value) => {
  // 如果没有 before-change 处理函数，直接触发 switch 事件
  if (!props.item.attrs || !props.item.attrs['before-change']) {
    handleFormEvent('switch', value, isInTable.value, {
      item: props.item,
      data: props.data,
      row: props.row,
      index: props.index,
      handle: props.handle,
      formRef: props.formRef,
      type: props.type
    }, props.item.attrs, emit);
  }
};

// Setup
onMounted(() => {
  // 注册到 widgetItem
  widgetItem[props.item.prop] = { resetField };
  
  // 初始化
  initFormModel();
});

// 暴露方法给父组件
defineExpose({
  resetField
});
</script>

<style scoped>
/* 开关组件样式 */
.el-switch {
  --el-switch-on-color: var(--el-color-primary);
  --el-switch-off-color: var(--el-border-color-darker);
}

/* 加载状态样式 */
.el-switch.is-loading {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
