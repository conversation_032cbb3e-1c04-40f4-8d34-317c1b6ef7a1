<template>
  <el-input
    v-model.trim="formModel[item.prop]"
    v-bind="itemAttrs"
    :placeholder="itemAttrs.placeholder || '请输入' + (itemAttrs.label || '')"
    v-on="listeners"
  >
    <template v-for="slot in slotList" :key="slot.name" #[slot.name]>
      <template v-if="typeof item[slot.name] === 'string'">
        {{ item[slot.name] }}
      </template>
      <co-table-select
        v-else
        v-bind="{
          item: slot[slot.name],
          dic: $attrs.dic,
          mainProp: slot[slot.name].prop ? '' : item.prop
        }"
        :row="formModel"
        :style="{ minWidth: slot[slot.name].width || '80px' }"
        @change="onPendChange"
      />
    </template>
  </el-input>
</template>

<script setup>
import { reactive, inject, computed, onMounted } from 'vue';
import { handleFormEvent } from '../../utils/form-helper.js';
import CoTableSelect from './co-table-select.vue';

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: null
  },
  row: {
    type: Object,
    default: null
  },
  scene: {
    type: String,
    default: ''
  },
  dic: {
    type: Object,
    default: () => null
  },
  index: Number,
  handle: Function,
  formRef: Object,
  type: String
});

// Emits
const emit = defineEmits(['change']);

// Inject
const widgetItem = inject('widgetItem', {});

// Computed
const formModel = computed(() => reactive(props.row || props.data || {}));
const isInTable = computed(() => props.scene === 'inTable');
const itemAttrs = computed(() => Object.assign(props.item.attrs || {}, { clearable: true }));

// Reactive data
const slotList = reactive([]);
const listeners = reactive({});

// Setup slots
const setupSlots = () => {
  slotList.length = 0;

  // 判断是否有前置和后置插槽
  if (props.item.prepend) {
    slotList.push({
      name: 'prepend',
      prepend: props.item.prepend,
    });
  }

  if (props.item.append) {
    slotList.push({
      name: 'append',
      append: props.item.append,
    });
  }
};

// Setup events
const setupEvents = () => {
  // 清空现有监听器
  Object.keys(listeners).forEach(key => delete listeners[key]);

  // 添加默认 change 事件
  listeners.change = (value) => {
    handleFormEvent('change', value, isInTable.value, {
      item: props.item,
      data: props.data,
      row: props.row,
      index: props.index,
      handle: props.handle,
      formRef: props.formRef,
      type: props.type
    }, itemAttrs.value);
  };

  // 为表格内表单时追加其他事件
  if (isInTable.value && props.item.events) {
    Object.keys(props.item.events).forEach(eventName => {
      listeners[eventName] = () => {
        handleFormEvent(eventName, props.row[props.item.prop], isInTable.value, {
          item: props.item,
          data: props.data,
          row: props.row,
          index: props.index,
          handle: props.handle,
          formRef: props.formRef,
          type: props.type
        }, itemAttrs.value);
      };
    });
  }
};

// Initialize form model
const initFormModel = () => {
  if (!formModel.value[props.item.prop]) {
    formModel.value[props.item.prop] = '';
  }
};

// Methods
const resetField = (value) => {
  formModel.value[props.item.prop] = value;
};

const onPendChange = (data) => {
  emit('change', { prop: data.prop, value: data.value });
};

// Setup
onMounted(() => {
  // 注册到 widgetItem
  widgetItem[props.item.prop] = { resetField };

  // 初始化
  initFormModel();
  setupSlots();
  setupEvents();
});

// 暴露方法给父组件
defineExpose({
  resetField
});
</script>

<style scoped>
/* 输入框组件样式 */
.el-input-group--prepend .el-input__wrapper {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
