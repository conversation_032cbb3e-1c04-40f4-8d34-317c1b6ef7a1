<template>
  <div :class="item.prepend && 'el-input-group el-input--suffix el-input-group--prepend'">
    <div class="el-input-group__prepend" v-if="item.prepend">
      <co-table-select 
        v-bind="{ 
          item: item.prepend, 
          dic: $attrs.dic, 
          mainProp: item.prepend.prop ? '' : item.prop 
        }" 
        :row="formModel" 
        @change="(data) => $emit('change', { prop: data.prop, value: data.value })" 
      />
    </div>
    <component 
      :is="componentName" 
      v-model="formModel[item.prop]" 
      v-bind="itemAttrs" 
      :placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" 
      v-on="listeners" 
      @change="onDateChange"
    />
  </div>
</template>

<script setup>
import { reactive, inject, computed, onMounted, ref } from 'vue';
import { getType } from '../../utils/index.js';
import { DATE_TYPES } from '../../config/enums.js';
import { handleFormEvent, handleDateSplit, resetDateField } from '../../utils/form-helper.js';
import CoTableSelect from './co-table-select.vue';

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: null
  },
  row: {
    type: Object,
    default: null
  },
  scene: {
    type: String,
    default: ''
  },
  dic: {
    type: Object,
    default: () => null
  },
  index: Number,
  handle: Function,
  formRef: Object,
  type: String
});

// Emits
const emit = defineEmits(['change']);

// Inject
const widgetItem = inject('widgetItem', {});

// Reactive data
const splitPropData = reactive({});
const listeners = reactive({});
const resetCounter = ref(0);

// Computed
const formModel = computed(() => reactive(props.row || props.data || {}));
const isInTable = computed(() => props.scene === 'inTable');
const isDateType = computed(() => DATE_TYPES.includes(props.item.type));

// 计算组件属性
const itemAttrs = computed(() => {
  const attrs = Object.assign(props.item.attrs || {}, { clearable: true });
  const isTimeType = props.item.type === 'time';
  
  // 为非时间类型设置 type 属性
  if (!isTimeType) {
    attrs.type = props.item.type;
  }
  
  return attrs;
});

// 计算组件名称
const componentName = computed(() => {
  const isTimeType = props.item.type === 'time';
  const hasPickerOptions = isTimeType && itemAttrs.value.pickerOptions;
  
  if (isTimeType && !hasPickerOptions) {
    return 'el-time-picker';
  } else if (hasPickerOptions) {
    return 'el-time-select';
  } else {
    return 'el-date-picker';
  }
});

// Setup events
const setupEvents = () => {
  // 清空现有监听器
  Object.keys(listeners).forEach(key => delete listeners[key]);
  
  // 添加 blur 事件（如果在表格内且有配置）
  if (isInTable.value && props.item.events?.blur) {
    listeners.blur = () => {
      handleFormEvent('blur', formModel.value[props.item.prop], isInTable.value, {
        item: props.item,
        data: props.data,
        row: props.row,
        index: props.index,
        handle: props.handle,
        formRef: props.formRef,
        type: props.type
      }, itemAttrs.value, emit);
    };
  }
};

// Initialize form model
const initFormModel = () => {
  // 处理分割字段
  if (props.item.splitProp && !props.item.prepend) {
    formModel.value[props.item.splitProp[0]] = '';
    formModel.value[props.item.splitProp[1]] = '';
    
    // 注册到 widgetItem
    widgetItem[props.item.splitProp[0]] = { resetField };
    widgetItem[props.item.splitProp[1]] = { resetField };
  } else {
    if (!formModel.value[props.item.prop]) {
      formModel.value[props.item.prop] = '';
    }
    
    // 注册到 widgetItem
    widgetItem[props.item.prop] = { resetField };
  }
};

// Methods
const resetField = (value) => {
  resetDateField(value, props.item, formModel.value, splitPropData, resetCounter);
};

const onDateChange = (value) => {
  handleDateSplit(value, props.item, formModel.value, splitPropData);
  
  // 触发表单事件
  const eventValue = props.item.splitProp ? splitPropData : splitPropData[props.item.prop];
  handleFormEvent('change', eventValue, isInTable.value, {
    item: props.item,
    data: props.data,
    row: props.row,
    index: props.index,
    handle: props.handle,
    formRef: props.formRef,
    type: props.type
  }, itemAttrs.value, emit);
};

// Setup
onMounted(() => {
  // 初始化
  initFormModel();
  setupEvents();
});

// 暴露方法给父组件
defineExpose({
  resetField
});
</script>

<style scoped>
/* 日期组件样式 */
:root {
  --el-input-border-color: var(--el-border-color);
}

.el-input-group {
  display: flex;
  width: 100%;
}

.el-input-group__prepend {
  background-color: var(--el-fill-color-light);
  color: var(--el-color-info);
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  padding: 0 20px;
  white-space: nowrap;
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.el-input-group--prepend .el-input__wrapper {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
