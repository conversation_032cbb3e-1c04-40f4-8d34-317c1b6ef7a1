/**
 * Co-Table 表单组件入口文件
 * 统一导出所有表单项组件
 */

import CoTableInput from './co-table-input.vue';
import CoTableSelect from './co-table-select.vue';
import CoTableDate from './co-table-date.vue';
import CoTableSwitch from './co-table-switch.vue';

// 组件映射表
export const formComponents = {
  'co-table-input': CoTableInput,
  'co-table-select': CoTableSelect,
  'co-table-date': CoTableDate,
  'co-table-switch': CoTableSwitch,
  
  // 别名映射（向后兼容）
  'co-input': CoTableInput,
  'co-select': CoTableSelect,
  'co-date': CoTableDate,
  'co-switch': CoTableSwitch,
  
  // 类型映射
  'input': CoTableInput,
  'select': CoTableSelect,
  'date': CoTableDate,
  'datetime': CoTableDate,
  'daterange': CoTableDate,
  'datetimerange': CoTableDate,
  'time': CoTableDate,
  'timeselect': CoTableDate,
  'year': CoTableDate,
  'month': CoTableDate,
  'switch': CoTableSwitch
};

// 获取组件类型对应的组件
export function getFormComponent(type) {
  return formComponents[type] || formComponents['input'];
}

// 检查是否为日期类型
export function isDateType(type) {
  const dateTypes = [
    'date', 'datetime', 'daterange', 'datetimerange',
    'time', 'timeselect', 'year', 'month', 'dates',
    'week', 'months', 'years', 'monthrange', 'yearrange'
  ];
  return dateTypes.includes(type);
}

// 导出单个组件
export {
  CoTableInput,
  CoTableSelect,
  CoTableDate,
  CoTableSwitch
};

// 默认导出组件映射表
export default formComponents;
