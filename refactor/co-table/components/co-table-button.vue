<template>
  <el-button 
    :class="buttonClass" 
    v-bind="buttonAttrs" 
    @click="onClick"
  >
    <slot>{{ buttonAttrs.name || buttonAttrs.text }}</slot>
  </el-button>
</template>

<script setup>
import { computed, toRef } from 'vue';

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  disClick: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['click']);

// 计算按钮属性
const buttonAttrs = computed(() => {
  const item = { ...props.item };
  
  // 清理不需要传递给 el-button 的属性
  const cleanedItem = { ...item };
  
  // 删除内部使用的属性
  delete cleanedItem.className;
  delete cleanedItem.classNew;
  delete cleanedItem.tableKey;
  delete cleanedItem.rule;
  delete cleanedItem.attributes;
  delete cleanedItem.mark;
  delete cleanedItem.inTable;
  
  return cleanedItem;
});

// 计算按钮样式类
const buttonClass = computed(() => {
  return props.item.className || props.item.classNew || '';
});

// 点击事件处理
const onClick = (event) => {
  // 如果禁用点击，直接返回
  if (props.disClick) return;
  
  // 阻止事件冒泡
  event.stopPropagation();
  
  // 发送点击事件
  emit('click', event);
};

// 暴露方法给父组件（如果需要）
defineExpose({
  // 可以暴露一些方法供父组件调用
});
</script>

<style scoped>
/* 按钮组件样式 */
.el-button {
  /* 确保按钮的响应式更新正常工作 */
  transition: all 0.3s ease;
}

/* 加载状态样式优化 */
.el-button.is-loading {
  position: relative;
}

.el-button.is-loading .el-icon {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 禁用状态样式 */
.el-button.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 自定义按钮类型样式 */
.co-table-btn-text {
  padding: 0;
  border: none;
  background: none;
  color: var(--el-color-primary);
}

.co-table-btn-text:hover {
  color: var(--el-color-primary-light-3);
}

.co-table-btn-link {
  color: var(--el-color-primary);
  text-decoration: underline;
  background: none;
  border: none;
  padding: 0;
}

.co-table-btn-link:hover {
  color: var(--el-color-primary-light-3);
}
</style>
