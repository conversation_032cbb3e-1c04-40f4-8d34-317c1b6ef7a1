<template>
  <el-form 
    v-if="hasFormItem" 
    ref="formRef"
    class="co-table-content" 
    :model="model" 
    :size="configOpts.size" 
    :validate-on-rule-change="false"
    v-bind="formAttrs"
  >
    <slot />
  </el-form>
  <template v-else>
    <slot />
  </template>
</template>

<script setup>
import { ref, computed } from 'vue';

// Props
const props = defineProps({
  model: {
    type: Object,
    default: () => ({})
  },
  hasFormItem: {
    type: Boolean,
    default: false
  },
  configOpts: {
    type: Object,
    default: () => ({
      size: 'default'
    })
  },
  // 额外的表单属性
  labelWidth: {
    type: [String, Number],
    default: 'auto'
  },
  labelPosition: {
    type: String,
    default: 'right',
    validator: (value) => ['left', 'right', 'top'].includes(value)
  },
  inline: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  rules: {
    type: Object,
    default: () => ({})
  }
});

// Refs
const formRef = ref(null);

// 计算表单属性
const formAttrs = computed(() => ({
  labelWidth: props.labelWidth,
  labelPosition: props.labelPosition,
  inline: props.inline,
  disabled: props.disabled,
  rules: props.rules
}));

// 表单方法
const validate = (callback) => {
  if (!formRef.value) {
    console.warn('表单引用不存在');
    return Promise.resolve(false);
  }
  
  if (callback && typeof callback === 'function') {
    return formRef.value.validate(callback);
  }
  
  return formRef.value.validate();
};

const validateField = (props, callback) => {
  if (!formRef.value) {
    console.warn('表单引用不存在');
    return;
  }
  
  return formRef.value.validateField(props, callback);
};

const resetFields = () => {
  if (!formRef.value) {
    console.warn('表单引用不存在');
    return;
  }
  
  formRef.value.resetFields();
};

const clearValidate = (props) => {
  if (!formRef.value) {
    console.warn('表单引用不存在');
    return;
  }
  
  formRef.value.clearValidate(props);
};

const scrollToField = (prop) => {
  if (!formRef.value) {
    console.warn('表单引用不存在');
    return;
  }
  
  formRef.value.scrollToField(prop);
};

// 获取表单引用（向后兼容）
const getFormRef = () => {
  return formRef.value;
};

// 暴露方法给父组件
defineExpose({
  formRef: getFormRef,
  validate,
  validateField,
  resetFields,
  clearValidate,
  scrollToField,
  // 向后兼容的方法名
  getFormRef
});
</script>

<style scoped>
/* 容器组件样式 */
.co-table-content {
  width: 100%;
}

/* 表单项样式优化 */
.co-table-content :deep(.el-form-item) {
  margin-bottom: 0;
}

.co-table-content :deep(.el-form-item__label) {
  padding: 0 12px 0 0;
}

.co-table-content :deep(.el-form-item__content) {
  line-height: normal;
}

/* 内联表单样式 */
.co-table-content.el-form--inline :deep(.el-form-item) {
  margin-right: 16px;
  margin-bottom: 16px;
}

.co-table-content.el-form--inline :deep(.el-form-item:last-child) {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .co-table-content.el-form--inline :deep(.el-form-item) {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }
}

/* 表单验证错误样式 */
.co-table-content :deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

.co-table-content :deep(.el-form-item.is-error .el-textarea__inner) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

/* 表单项标签样式 */
.co-table-content :deep(.el-form-item__label) {
  color: var(--el-text-color-regular);
  font-weight: normal;
}

/* 必填项标记样式 */
.co-table-content :deep(.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label::before) {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
</style>
