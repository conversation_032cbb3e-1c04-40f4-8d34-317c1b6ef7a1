<template>
  <component :is="renderComponent" />
</template>

<script setup>
import { h, computed } from 'vue';
import { parseTime } from '../utils/formatter.js';

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  row: {
    type: Object,
    required: true
  },
  column: {
    type: Object,
    default: () => ({})
  },
  index: {
    type: Number,
    default: 0
  },
  handle: {
    type: Function,
    default: () => {}
  }
});

// 渲染带样式的内容
const renderByStyles = (styles, data) => {
  const rdData = data !== 0 && !data ? "-" : data;
  
  if (!styles) {
    return h("span", {}, rdData);
  }
  
  if (typeof styles === "string") {
    return h("span", { class: styles }, rdData);
  }
  
  return h("span", { style: styles }, rdData);
};

// 处理点击事件
const onHandle = (type) => {
  props.handle({ 
    type, 
    field: props.item.prop, 
    row: props.row, 
    index: props.index 
  });
};

// 获取格式化后的值
const getFormattedValue = () => {
  const { item, row, column, index } = props;
  
  if (item.attrs && item.attrs.formatter) {
    return item.attrs.formatter(row, column, row[item.prop], index);
  }
  
  return null;
};

// 类型模板映射
const typeTemplates = computed(() => ({
  // 日期类型
  date: () => {
    const formatter = getFormattedValue();
    const value = formatter || props.row[props.item.prop];
    const format = props.item.format || "yyyy-MM-dd HH:mm:ss";
    
    return h("span", {}, value ? parseTime(value, format) : "-");
  },
  
  // 下载类型
  download: () => {
    const formatter = getFormattedValue();
    const text = formatter || props.item.text || "点击下载";
    
    return h("span", {
      style: "cursor:pointer;color:var(--el-color-primary);",
      onClick: () => onHandle("download"),
    }, renderByStyles(props.item.styles, text));
  },
  
  // 预览类型
  preview: () => {
    const formatter = getFormattedValue();
    const text = formatter || props.item.text || props.row[props.item.prop] || "点击查看";
    
    return h("span", {
      style: "cursor:pointer;color:var(--el-color-primary);",
      onClick: () => onHandle("preview"),
    }, renderByStyles(props.item.styles, text));
  },
  
  // 图片类型
  img: () => {
    const imageUrl = props.row[props.item.prop];
    
    if (!imageUrl) {
      return h("span", {}, "-");
    }
    
    return h("img", {
      src: imageUrl,
      style: { 
        height: props.item.height || '32px',
        maxWidth: '100px',
        cursor: 'pointer',
        objectFit: 'cover'
      },
      onClick: () => onHandle("preview"),
      onError: (e) => {
        e.target.style.display = 'none';
        e.target.nextSibling && (e.target.nextSibling.style.display = 'inline');
      }
    });
  },
  
  // 枚举类型
  enum: () => {
    const rowProp = props.row[props.item.prop];
    
    if (!rowProp) {
      return h("span", {}, "-");
    }
    
    const valueKey = props.item.valueKey || "value";
    const labelKey = props.item.labelKey || "text";
    const itemColor = props.item.colors && props.item.colors[rowProp[valueKey]];
    
    let styles = {};
    if (itemColor) {
      if (itemColor.indexOf("#") > -1) {
        styles = { style: { color: itemColor } };
      } else {
        styles = { class: itemColor };
      }
    }
    
    return h("span", styles, rowProp[labelKey] || rowProp);
  },
  
  // 默认类型
  default: () => {
    const formatter = getFormattedValue();
    const value = formatter || props.row[props.item.prop];
    
    return renderByStyles(props.item.styles || "", value);
  }
}));

// 渲染组件
const renderComponent = computed(() => {
  const { item } = props;
  const templates = typeTemplates.value;
  
  // 根据类型选择对应的模板，如果没有则使用默认模板
  const template = templates[item.type] || templates.default;
  
  return template();
});
</script>

<style scoped>
/* 静态组件样式 */
:deep(span) {
  word-break: break-all;
  line-height: 1.5;
}

:deep(img) {
  border-radius: 4px;
  transition: all 0.3s ease;
}

:deep(img:hover) {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 链接样式 */
:deep(span[style*="cursor:pointer"]) {
  transition: color 0.3s ease;
}

:deep(span[style*="cursor:pointer"]:hover) {
  color: var(--el-color-primary-light-3) !important;
}

/* 枚举颜色样式 */
:deep(.enum-success) {
  color: var(--el-color-success);
}

:deep(.enum-warning) {
  color: var(--el-color-warning);
}

:deep(.enum-danger) {
  color: var(--el-color-danger);
}

:deep(.enum-info) {
  color: var(--el-color-info);
}

:deep(.enum-primary) {
  color: var(--el-color-primary);
}

/* 状态标签样式 */
:deep(.status-tag) {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

:deep(.status-tag.success) {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
  border: 1px solid var(--el-color-success-light-5);
}

:deep(.status-tag.warning) {
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
  border: 1px solid var(--el-color-warning-light-5);
}

:deep(.status-tag.danger) {
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  border: 1px solid var(--el-color-danger-light-5);
}

:deep(.status-tag.info) {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
  border: 1px solid var(--el-color-info-light-5);
}
</style>
