<template>
  <el-form
    ref="searchFormRef"
    class="co-table-search"
    :model="model"
    v-bind="formAttrs"
    :[styleName]="styles"
  >
    <template v-for="item in config.items" :key="item.prop">
      <el-form-item
        v-if="!item.hidden"
        :prop="item.prop"
        v-bind="item.attrs"
      >
        <slot
          :name="scene ? `search_${item.prop}` : item.prop"
          :item="item"
          :data="model"
        >
          <component
            :is="getFormComponent(getComponentType(item))"
            :item="item"
            :data="model"
            :dic="dic"
            :scene="scene"
            @change="onItemChange"
          />
        </slot>
      </el-form-item>
    </template>

    <el-form-item v-if="config.items && config.items.length > 0">
      <slot
        :name="scene ? 'search_operation' : 'operation'"
        :handle="onHandle"
      >
        <el-button
          v-bind="searchProps"
          @click="onHandle('search')"
        >
          {{ searchProps.text }}
        </el-button>
        <el-button
          v-bind="resetProps"
          @click="onHandle('reset')"
        >
          {{ resetProps.text }}
        </el-button>
      </slot>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { computed } from 'vue';
import { useSearch } from '../composables/useSearch.js';
import { getFormComponent } from './form-items/index.js';

// 组件名称
defineOptions({
  name: 'CoTableSearch',
  inheritAttrs: false
});

// Props
const props = defineProps({
  model: {
    type: Object,
    default: () => ({})
  },
  config: {
    type: Object,
    default: () => ({})
  },
  dic: {
    type: Object,
    default: () => null
  },
  scene: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['change', 'search', 'update:model']);

// 使用搜索功能
const {
  searchFormRef,
  searchProps,
  resetProps,
  styles,
  styleName,
  getComponentType,
  onItemChange,
  onHandle,
  setData,
  validate,
  resetFields,
  clearValidate
} = useSearch(props, emit);

// 计算表单属性
const formAttrs = computed(() => {
  // 设置默认属性
  const defaultAttrs = {
    inline: true,
    size: 'default',
    validateOnRuleChange: false
  };

  return defaultAttrs;
});

// 暴露方法给父组件
defineExpose({
  searchFormRef,
  setData,
  validate,
  resetFields,
  clearValidate,
  onHandle
});
</script>

<style lang="scss" scoped>
/* 搜索组件样式 */
.co-table-search {
  margin-bottom: 16px;
}

/* 内联表单样式 */
.co-table-search.el-form--inline {
  :deep(.el-form-item) {
    margin-right: 16px;
    margin-bottom: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

/* 表单项样式 */
.co-table-search :deep(.el-form-item) {
  .el-input,
  .el-cascader,
  .el-select,
  .el-autocomplete {
    width: 220px;
  }

  .el-date-editor {
    width: 360px;

    &.el-date-editor--datetimerange {
      width: 380px;
    }

    &.el-date-editor--monthrange,
    &.el-date-editor--yearrange {
      width: 270px;
    }
  }

  .el-input-group__prepend,
  .el-input-group__append {
    .el-select {
      width: 120px;
    }
  }

  .el-input:has(.el-input-group__prepend, .el-input-group__append) {
    width: 320px;
  }

  .el-input.el-input-group--append.el-input-group--prepend {
    width: 400px;
  }
}

/* 操作按钮样式 */
.co-table-search :deep(.el-form-item:last-child) {
  .el-button {
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .co-table-search.el-form--inline {
    :deep(.el-form-item) {
      display: block;
      margin-right: 0;
      margin-bottom: 16px;

      .el-input,
      .el-select,
      .el-date-editor,
      .el-cascader,
      .el-autocomplete {
        width: 100%;
      }
    }
  }
}

/* 表单验证样式 */
.co-table-search :deep(.el-form-item.is-error) {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  }

  .el-textarea__inner {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  }
}

/* 表单项标签样式 */
.co-table-search :deep(.el-form-item__label) {
  color: var(--el-text-color-regular);
  font-weight: normal;
  padding: 0 12px 0 0;
}

/* 表单项内容样式 */
.co-table-search :deep(.el-form-item__content) {
  line-height: normal;
}
</style>
