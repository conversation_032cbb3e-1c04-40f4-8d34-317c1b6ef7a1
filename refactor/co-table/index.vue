<template>
  <div
    class="co-table-page"
    :style="{
      '--highlight-color': highlightColor,
      '--selection-text': `'${selectionText}'`,
      '--header-color': mergeAttrs['header-cell-style']?.color,
    }"
  >
    <!-- 搜索组件 -->
    <template v-if="search">
      <co-table-search
        ref="searchRef"
        :model="searchFormData"
        :config="search"
        :dic="dicLoaded ? dicEnumData : null"
        :scene="isPageMode ? '' : 'inTable'"
        @search="onSearch"
        @change="onSearchChange"
      >
        <template v-for="(_, slotName) in searchSlots" :key="slotName" #[slotName]="slotProps">
          <slot :name="slotName" v-bind="slotProps || {}" />
        </template>
      </co-table-search>
    </template>

    <!-- 表格容器 -->
    <div v-loading="loading" class="co-table-container">
      <!-- 顶部操作按钮 -->
      <div v-if="topOperationList.length" class="top-operation">
        <slot name="topOperation" :list="topOperationList">
          <co-table-button
            v-for="item in topOperationList"
            :key="item.mark"
            :item="getButtonProps(item)"
            @click="dispatchHandle({ field: item.mark, btn: item, id: tableKey })"
          />
        </slot>
        <slot name="topOperationText" />
      </div>

      <!-- 表格表单容器 -->
      <co-table-container
        ref="tableFormRef"
        :model="{ data: tableData }"
        :has-form-item="hasFormItem"
        :config-opts="{ size: $attrs.size || 'default' }"
      >
        <!-- Element Plus 表格 -->
        <el-table
          :ref="tableKey + '_elTableRef'"
          :data="tableData"
          :header-cell-class-name="cellClass"
          :row-key="$attrs['row-key'] || '_uuid'"
          :row-class-name="rowClassName"
          v-bind="mergeAttrs"
          class="co-table"
          v-on="tableListeners"
        >
          <!-- 展开行插槽 -->
          <el-table-column v-if="$slots['expand']" type="expand">
            <template #default="{ row, column, $index }">
              <slot name="expand" v-bind="{ row, column, $index }" />
            </template>
          </el-table-column>

          <!-- 空数据插槽 -->
          <template v-if="$slots['empty']" #empty>
            <slot name="empty">暂无数据</slot>
          </template>

          <!-- 选择列 -->
          <template v-if="selection">
            <el-table-column
              type="selection"
              v-bind="selection"
              :align="selection.align || align"
            />
          </template>

          <!-- 数据列 -->
          <template v-for="item in header" :key="item.prop">
            <el-table-column
              v-if="!item.hidden"
              v-bind="item"
              :align="item.align || align"
            >
              <!-- 列头插槽 -->
              <template v-if="$slots[item.prop + '_header']" #header="slotHeader">
                <slot :name="item.prop + '_header'" v-bind="slotHeader" />
              </template>

              <!-- 列内容 -->
              <template v-if="item.type !== 'index'" #default="{ row, column }">
                <!-- 表单类型列 -->
                <template v-if="isFormType(item.type)">
                  <slot
                    v-if="$slots[item.prop + '_form-item']"
                    :name="item.prop + '_form-item'"
                    :dicEnum="dicEnum[item.prop]"
                    v-bind="{
                      row,
                      column,
                      $index: row._index,
                      item,
                      prop: item.prop,
                    }"
                  />
                  <el-form-item
                    v-else
                    :key="row._selected"
                    :prop="row._propPath + '.' + item.prop"
                    :rules="row._selected || isValidate ? item?.rules : []"
                    :class="{ 'table-switch-align': align === 'center' }"
                  >
                    <slot
                      :name="item.prop"
                      :dicEnum="dicEnum[item.prop]"
                      v-bind="{
                        row,
                        column,
                        $index: row._index,
                        item,
                        prop: item.prop,
                      }"
                    >
                      <component
                        :is="getFormComponent(getFormType(item.type))"
                        :type="item.type"
                        scene="inTable"
                        :form-ref="tableFormRef"
                        :index="row._index"
                        :item="item"
                        :dic="dicEnumData"
                        :row="row"
                        :handle="handleTableFormEvent"
                      />
                    </slot>
                  </el-form-item>
                </template>

                <!-- 非表单类型列 -->
                <template v-else>
                  <slot
                    :name="item.prop"
                    :dicEnum="dicEnum[item.prop]"
                    v-bind="{
                      row,
                      column,
                      $index: row._index,
                      item,
                      prop: item.prop,
                    }"
                  >
                    <!-- 字典类型 -->
                    <template v-if="dicKeyArr.includes(item.prop) && dicLoaded">
                      <span v-show="false">{{
                        ([propStyle, propData] = [
                          dicEnum[item.prop]["color"] && dicEnum[item.prop]["color"][row[item.prop]],
                          dicEnum[item.prop]["data"][row[item.prop]],
                        ])
                      }}</span>
                      <template v-if="propStyle">
                        <span v-if="propStyle.includes('#')" :style="{ color: propStyle }">
                          {{ propData || "-" }}
                        </span>
                        <span v-else :class="propStyle">{{ propData || "-" }}</span>
                      </template>
                      <span v-else>{{ propData || "-" }}</span>
                    </template>

                    <!-- 上传类型 -->
                    <template v-else-if="item.type === 'upload'">
                      <co-table-upload
                        v-bind="item"
                        @onSuccess="uploadSuccess($event, row, item, row._index)"
                      />
                    </template>

                    <!-- 静态组件 -->
                    <co-table-static
                      v-else
                      :item="item"
                      :column="column"
                      :index="row._index"
                      :row="row"
                      :handle="item.type === 'download' ? onDownLoad : dispatchHandle"
                    />
                  </slot>
                </template>
              </template>
            </el-table-column>
          </template>

          <!-- 操作列 -->
          <template v-if="!hiddenOperation">
            <el-table-column
              v-if="operationList.length"
              :fixed="operationOpts.fixed"
              :align="operationOpts.align || align"
              :label="operationOpts.label || '操作'"
              :width="operationOpts.width"
            >
              <template v-if="$slots['operation_header']" #header="slotHeader">
                <slot name="operation_header" v-bind="slotHeader" />
              </template>

              <template #default="{ row }">
                <slot
                  name="operation"
                  :list="getAllButtons(currentRowKey + row[currentRowKey])"
                  :row="row"
                  :index="row._index"
                >
                  <template v-if="Object.keys(showPermisBtn).length > 0">
                    <co-table-button
                      v-for="item in getShowButtons(currentRowKey + row[currentRowKey])"
                      :key="item.mark"
                      :item="getButtonProps(item, row)"
                      @click="dispatchHandle({
                        field: item.mark,
                        btn: item,
                        row,
                        index: row._index,
                        id: tableKey,
                      })"
                    />

                    <!-- 更多按钮下拉菜单 -->
                    <el-dropdown
                      v-if="hasMoreButtons(currentRowKey + row[currentRowKey])"
                      :trigger="moreConfig.trigger"
                      :hide-on-click="false"
                      style="padding-left: 10px"
                      @command="dispatchHandle"
                    >
                      <el-button v-bind="moreConfig.attrs">
                        {{ moreConfig.text }}
                        <el-icon class="el-icon--right">
                          <ArrowDown />
                        </el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
                            v-for="item in getMoreButtons(currentRowKey + row[currentRowKey])"
                            :key="item.mark"
                            :command="{
                              field: item.mark,
                              btn: item,
                              row,
                              index: row._index,
                              id: tableKey,
                            }"
                          >
                            {{ item.name }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </slot>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </co-table-container>

      <!-- 分页组件 -->
      <template v-if="pagination !== false">
        <el-pagination
          v-bind="paginationAttrs"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, provide, onMounted, useSlots } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';

// 导入组合式函数
import { useTable } from './composables/useTable.js';
import { useTableData } from './composables/useTableData.js';
import { useTableOperation } from './composables/useTableOperation.js';
import { useTablePagination } from './composables/useTablePagination.js';
import { usePermission } from './composables/usePermission.js';

// 导入组件
import CoTableSearch from './components/co-table-search.vue';
import CoTableButton from './components/co-table-button.vue';
import CoTableContainer from './components/co-table-container.vue';
import CoTableUpload from './components/co-table-upload.vue';
import CoTableStatic from './components/co-table-static.vue';
import { getFormComponent, isDateType } from './components/form-items/index.js';

// 导入工具函数
import { getGlobalConfig } from './config/index.js';
import { DATE_TYPES } from './config/enums.js';

// 组件名称
defineOptions({
  name: 'CoTable',
  inheritAttrs: false
});

// Props定义
const props = defineProps({
  // 基础配置
  id: String,
  tableKey: String,

  // 数据相关
  data: {
    type: Array,
    default: () => []
  },
  api: Function,

  // 表格配置
  config: {
    type: Object,
    default: () => ({})
  },
  header: {
    type: Array,
    default: () => []
  },
  tableAttrs: {
    type: Object,
    default: () => ({})
  },

  // 搜索配置
  search: {
    type: Object,
    default: null
  },

  // 分页配置
  pagination: {
    type: [Object, Boolean],
    default: true
  },
  paginationAttrs: {
    type: Object,
    default: () => ({})
  },

  // 字典配置
  dic: {
    type: Object,
    default: null
  },

  // 权限配置
  userPermissions: {
    type: Array,
    default: () => []
  },

  // 操作配置
  operationOpts: {
    type: Object,
    default: () => ({})
  },

  // 其他配置
  align: {
    type: String,
    default: 'left'
  },
  highlightColor: {
    type: String,
    default: '#409EFF'
  },
  selectionText: {
    type: String,
    default: '已选择'
  },

  // 兼容性配置
  rowKey: {
    type: String,
    default: 'id'
  },
  childrenKey: {
    type: String,
    default: 'children'
  },
  singleMode: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Boolean,
    default: false
  },
  isValidate: {
    type: Boolean,
    default: false
  },
  hiddenOperation: {
    type: Boolean,
    default: false
  },

  // Page-table 兼容性
  formModel: {
    type: Object,
    default: () => ({})
  },
  tableHeader: {
    type: Array,
    default: () => []
  },
  tableConfig: {
    type: Object,
    default: () => ({})
  },
  searchConfig: {
    type: Object,
    default: () => ({})
  }
});

// Emits定义
const emit = defineEmits([
  'search',
  'search-change',
  'handle',
  'operation',
  'selection-change',
  'select',
  'select-all',
  'row-click',
  'single-click',
  'upload-success',
  'download',
  'form-change',
  'data-loaded',
  'data-error',
  'pagination-change',
  'refresh',
  'loaded',
  'dicLoaded'
]);

// 获取插槽
const slots = useSlots();

// 计算搜索相关插槽
const searchSlots = computed(() => {
  const searchSlotNames = {};
  Object.keys(slots).forEach(name => {
    if (name.startsWith('search_') || name === 'operation') {
      searchSlotNames[name] = slots[name];
    }
  });
  return searchSlotNames;
});

// 判断是否为页面模式（page-table兼容）
const isPageMode = computed(() => {
  return !!(props.formModel || props.tableHeader?.length || props.searchConfig?.items);
});

// 兼容性处理：统一配置格式
const unifiedConfig = computed(() => {
  if (isPageMode.value) {
    return {
      ...props.tableConfig,
      operation: props.tableConfig.operation || props.config.operation
    };
  }
  return props.config;
});

const unifiedHeader = computed(() => {
  return props.tableHeader?.length ? props.tableHeader : props.header;
});

const unifiedSearch = computed(() => {
  return props.searchConfig?.items ? props.searchConfig : props.search;
});

// 使用组合式函数
const tableState = useTable(props, emit);
const paginationState = useTablePagination(props, emit);
const dataState = useTableData(props, emit, tableState, paginationState);
const operationState = useTableOperation(props, emit, tableState);
const permissionState = usePermission(props, null);

// 解构响应式数据和方法
const {
  tableRef,
  tableFormRef,
  selectedData,
  tableData,
  loading,
  tableKey,
  currentRowKey,
  mergeAttrs,
  initListeners,
  refresh,
  clearSelection
} = tableState;

const {
  searchFormData,
  dicEnumData,
  dicLoaded,
  dicKeyArr,
  dicEnum,
  onSearch: handleSearch,
  loadTableData,
  refreshData
} = dataState;

const {
  operationOpts,
  moreConfig,
  dispatchHandle,
  uploadSuccess,
  onDownLoad,
  getButtonProps,
  handleTableFormEvent
} = operationState;

const {
  paginationAttrs,
  handleCurrentChange,
  handleSizeChange
} = paginationState;

const {
  operationList,
  inTableRowBtn,
  showPermisBtn,
  renderOperation,
  setRowPermissions,
  getShowButtons,
  getAllButtons
} = permissionState;

// 计算属性
const selection = computed(() => {
  return unifiedConfig.value.selection || null;
});

const hasFormItem = computed(() => {
  return unifiedHeader.value.some(item => isFormType(item.type));
});

const topOperationList = computed(() => {
  return operationList.value.filter(btn => +btn.inTable === 2);
});

const cellClass = computed(() => {
  return (row, column, rowIndex, columnIndex) => {
    // 可以在这里添加自定义的单元格样式逻辑
    return '';
  };
});

const rowClassName = computed(() => {
  return (row, rowIndex) => {
    const classes = [];
    if (row.row._selected) {
      classes.push('selected-row');
    }
    return classes.join(' ');
  };
});

const tableListeners = computed(() => {
  return initListeners({});
});

// 临时变量（用于字典显示）
let propStyle = '';
let propData = '';

// 方法
const isFormType = (type) => {
  const formTypes = ['input', 'select', 'switch', 'inputNumber'];
  return formTypes.includes(type) || isDateType(type);
};

const getFormType = (type) => {
  return isDateType(type) ? 'date' : type;
};

const onSearch = (searchResult, type) => {
  handleSearch(searchResult, type);

  // Page-table 兼容性
  if (isPageMode.value) {
    emit('search', { params: searchResult, type });
  }
};

const onSearchChange = (prop, value) => {
  emit('search-change', prop, value);
};

const hasMoreButtons = (rowKey) => {
  const showButtons = getShowButtons(rowKey);
  const allButtons = getAllButtons(rowKey);
  return showButtons.length !== allButtons.length;
};

const getMoreButtons = (rowKey) => {
  const showButtons = getShowButtons(rowKey);
  const allButtons = getAllButtons(rowKey);
  return allButtons.slice(showButtons.length);
};

// 初始化
onMounted(() => {
  // 初始化权限
  renderOperation(unifiedConfig.value, props);

  // 如果有数据，直接设置
  if (props.data?.length) {
    tableState.initTableData(props.data);
  } else if (props.api) {
    // 如果有API，加载数据
    loadTableData();
  }

  // Page-table 兼容性：暴露方法
  if (isPageMode.value) {
    emit('loaded', { getDataList: loadTableData });
  }
});

// 暴露方法给父组件
defineExpose({
  // 表格方法
  refresh,
  clearSelection,
  loadTableData,
  refreshData,

  // 分页方法
  goToPage: paginationState.goToPage,
  resetPagination: paginationState.resetPagination,

  // 数据方法
  getTableData: dataState.getTableData,
  setTableData: dataState.setTableData,
  addTableRow: dataState.addTableRow,
  removeTableRow: dataState.removeTableRow,
  updateTableRow: dataState.updateTableRow,

  // 权限方法
  checkPermission: permissionState.checkPermission,

  // Page-table 兼容性
  onSearchHandle: onSearch,
  oldParams: searchFormData
});
</script>

<style lang="scss" scoped>
@import './styles/index.scss';

.co-table-page {
  .co-table-container {
    .top-operation {
      margin-bottom: 16px;

      .co-table-button {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .co-table {
      .selected-row {
        background-color: var(--highlight-color, #409EFF);
        opacity: 0.1;
      }

      .table-switch-align {
        text-align: center;
      }
    }
  }

  .el-pagination {
    margin-top: 16px;
    text-align: right;
  }
}
</style>
