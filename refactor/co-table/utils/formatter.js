/**
 * Co-Table 格式化工具函数
 * 提供日期、数字、文本等格式化功能
 */

/**
 * 格式化时间
 * @param {Date|string|number} time - 目标时间值
 * @param {string} pattern - 需要的格式，如：yyyy-MM-dd HH:mm:ss
 * @returns {string|null} 格式化后的日期字符串
 * @example
 * parseTime(new Date(), 'yyyy-MM-dd') // '2024-01-01'
 * parseTime('2024-01-01', 'yyyy年MM月dd日') // '2024年01月01日'
 * parseTime(1704067200000, 'yyyy-MM-dd HH:mm:ss') // '2024-01-01 00:00:00'
 */
export function parseTime(time, pattern = 'yyyy-MM-dd HH:mm:ss') {
  if (!time) return null;
  
  let date;
  
  // 处理不同类型的时间输入
  if (time instanceof Date) {
    date = time;
  } else if (typeof time === 'string') {
    // 处理字符串格式的时间
    if (/^[0-9]+$/.test(time)) {
      // 纯数字字符串
      time = parseInt(time);
    } else {
      // 替换横线为斜线，兼容不同浏览器
      time = time.replace(/-/g, '/');
    }
    date = new Date(time);
  } else if (typeof time === 'number') {
    // 处理时间戳（秒或毫秒）
    if (time.toString().length === 10) {
      time = time * 1000; // 秒转毫秒
    }
    date = new Date(time);
  } else {
    return null;
  }
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return null;
  }
  
  // 格式化对象
  const formatObj = {
    yyyy: date.getFullYear(),
    MM: String(date.getMonth() + 1).padStart(2, '0'),
    dd: String(date.getDate()).padStart(2, '0'),
    HH: String(date.getHours()).padStart(2, '0'),
    mm: String(date.getMinutes()).padStart(2, '0'),
    ss: String(date.getSeconds()).padStart(2, '0'),
    a: ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
  };
  
  // 替换格式字符串
  return pattern.replace(/(yyyy|MM|dd|HH|mm|ss|a)/g, (match) => {
    return formatObj[match] || match;
  });
}

/**
 * 格式化数字
 * @param {number} num - 要格式化的数字
 * @param {number} decimals - 小数位数
 * @param {string} thousandsSep - 千分位分隔符
 * @param {string} decimalSep - 小数点分隔符
 * @returns {string} 格式化后的数字字符串
 * @example
 * formatNumber(1234.567, 2) // '1,234.57'
 * formatNumber(1234.567, 0, ' ') // '1 235'
 */
export function formatNumber(num, decimals = 2, thousandsSep = ',', decimalSep = '.') {
  if (num === null || num === undefined || isNaN(num)) {
    return '-';
  }
  
  const number = Number(num);
  const parts = number.toFixed(decimals).split('.');
  
  // 添加千分位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSep);
  
  return parts.join(decimalSep);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 * @example
 * formatFileSize(1024) // '1.00 KB'
 * formatFileSize(1048576) // '1.00 MB'
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
}

/**
 * 格式化百分比
 * @param {number} value - 数值（0-1 或 0-100）
 * @param {number} decimals - 小数位数
 * @param {boolean} isDecimal - 输入值是否为小数（0-1）
 * @returns {string} 格式化后的百分比
 * @example
 * formatPercent(0.1234, 2, true) // '12.34%'
 * formatPercent(12.34, 2, false) // '12.34%'
 */
export function formatPercent(value, decimals = 2, isDecimal = true) {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }
  
  const percent = isDecimal ? value * 100 : value;
  return percent.toFixed(decimals) + '%';
}

/**
 * 格式化货币
 * @param {number} amount - 金额
 * @param {string} currency - 货币符号
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的货币字符串
 * @example
 * formatCurrency(1234.56) // '¥1,234.56'
 * formatCurrency(1234.56, '$') // '$1,234.56'
 */
export function formatCurrency(amount, currency = '¥', decimals = 2) {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '-';
  }
  
  return currency + formatNumber(amount, decimals);
}

/**
 * 截断文本
 * @param {string} text - 要截断的文本
 * @param {number} maxLength - 最大长度
 * @param {string} suffix - 后缀
 * @returns {string} 截断后的文本
 * @example
 * truncateText('这是一段很长的文本', 5) // '这是一段很...'
 */
export function truncateText(text, maxLength, suffix = '...') {
  if (!text || text.length <= maxLength) {
    return text || '';
  }
  
  return text.substring(0, maxLength) + suffix;
}

/**
 * 高亮关键词
 * @param {string} text - 原文本
 * @param {string} keyword - 关键词
 * @param {string} className - 高亮样式类名
 * @returns {string} 高亮后的HTML字符串
 * @example
 * highlightKeyword('Hello World', 'World') // 'Hello <mark>World</mark>'
 */
export function highlightKeyword(text, keyword, className = 'highlight') {
  if (!text || !keyword) return text;
  
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(regex, `<span class="${className}">$1</span>`);
}

/**
 * 格式化枚举值显示
 * @param {any} value - 枚举值
 * @param {Array|Object} enumData - 枚举数据
 * @param {string} labelKey - 标签字段名
 * @param {string} valueKey - 值字段名
 * @returns {string} 格式化后的显示文本
 * @example
 * formatEnum(1, [{label: '启用', value: 1}, {label: '禁用', value: 0}]) // '启用'
 */
export function formatEnum(value, enumData, labelKey = 'label', valueKey = 'value') {
  if (!enumData || value === null || value === undefined) {
    return '-';
  }
  
  if (Array.isArray(enumData)) {
    const item = enumData.find(item => item[valueKey] === value);
    return item ? item[labelKey] : value;
  }
  
  return enumData[value] || value;
}
