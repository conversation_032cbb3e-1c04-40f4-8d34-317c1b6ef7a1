/**
 * Co-Table 通用工具函数库
 * 提供类型检测、对象操作、数据处理等通用功能
 */

/**
 * 判断数据类型
 * @param {any} value - 要检测的数据
 * @returns {string} 具体的类型，如：Array、Object、String等
 * @example
 * getType([1, 2, 3]) // 'Array'
 * getType({}) // 'Object'
 * getType('hello') // 'String'
 */
export function getType(value) {
  const type = Object.prototype.toString.call(value);
  const typeMap = {
    '[object Array]': 'Array',
    '[object Object]': 'Object',
    '[object String]': 'String',
    '[object Number]': 'Number',
    '[object Boolean]': 'Boolean',
    '[object Function]': 'Function',
    '[object AsyncFunction]': 'AsyncFunction',
    '[object Promise]': 'Promise',
    '[object Date]': 'Date',
    '[object RegExp]': 'RegExp',
    '[object Null]': 'Null',
    '[object Undefined]': 'Undefined'
  };
  return typeMap[type] || 'Unknown';
}

/**
 * 深度合并两个对象
 * @param {object} target - 目标对象
 * @param {object} source - 源对象
 * @returns {object} 合并后的对象
 * @example
 * deepMerge({a: 1}, {b: 2}) // {a: 1, b: 2}
 * deepMerge({a: {x: 1}}, {a: {y: 2}}) // {a: {x: 1, y: 2}}
 */
export function deepMerge(target, source) {
  if (!target || !source) return target || source;
  
  const result = { ...target };
  
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      if (getType(source[key]) === 'Object' && getType(result[key]) === 'Object') {
        result[key] = deepMerge(result[key], source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  
  return result;
}

/**
 * 深度合并多个对象
 * @param {...object} objects - 要合并的对象列表
 * @returns {object} 合并后的对象
 * @example
 * deepMergeMultiple({a: 1}, {b: 2}, {c: 3}) // {a: 1, b: 2, c: 3}
 */
export function deepMergeMultiple(...objects) {
  return objects.reduce((result, obj) => deepMerge(result, obj), {});
}

/**
 * 深度克隆对象
 * @param {any} origin - 源数据
 * @returns {any} 克隆后的新数据
 * @example
 * const obj = {a: {b: 1}};
 * const cloned = deepClone(obj);
 * cloned.a.b = 2; // 不会影响原对象
 */
export function deepClone(origin) {
  if (origin === null || typeof origin !== 'object') {
    return origin;
  }
  
  if (origin instanceof Date) {
    return new Date(origin.getTime());
  }
  
  if (origin instanceof RegExp) {
    return new RegExp(origin);
  }
  
  const result = Array.isArray(origin) ? [] : {};
  
  for (const key in origin) {
    if (Object.prototype.hasOwnProperty.call(origin, key)) {
      result[key] = deepClone(origin[key]);
    }
  }
  
  return result;
}

/**
 * 生成简单的UUID（用于表格行标识）
 * @returns {string} 生成的UUID
 * @example
 * generateUUID() // '1234567890123'
 */
export function generateUUID() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 检查对象是否为空
 * @param {object} obj - 要检查的对象
 * @returns {boolean} 是否为空对象
 * @example
 * isEmpty({}) // true
 * isEmpty({a: 1}) // false
 */
export function isEmpty(obj) {
  if (!obj) return true;
  if (getType(obj) === 'Array') return obj.length === 0;
  if (getType(obj) === 'Object') return Object.keys(obj).length === 0;
  return false;
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 * @example
 * const debouncedFn = debounce(() => console.log('called'), 300);
 */
export function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 * @example
 * const throttledFn = throttle(() => console.log('called'), 300);
 */
export function throttle(func, delay) {
  let lastCall = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(this, args);
    }
  };
}

/**
 * 下载文件
 * @param {string} fileUrl - 文件URL
 * @param {string} fileName - 文件名
 * @example
 * downloadFile('http://example.com/file.pdf', 'document.pdf')
 */
export function downloadFile(fileUrl, fileName) {
  if (!fileUrl) return;

  fetch(fileUrl)
    .then(response => response.blob())
    .then(blob => {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);

      if (fileName) {
        link.download = fileName;
      } else {
        const urlParts = fileUrl.split('/');
        link.download = urlParts[urlParts.length - 1];
      }

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理 URL 对象
      URL.revokeObjectURL(link.href);
    })
    .catch(error => {
      console.error('文件下载失败:', error);
    });
}

// 为了向后兼容，保留原有的 Utils 类
export default class Utils {
  static getType = getType;
  static deepMerge = deepMerge;
  static deepMerge2 = deepMergeMultiple;
  static deepClone = deepClone;
  static uuid = generateUUID;
  static isEmpty = isEmpty;
  static parseTime = (time, pattern) => {
    // 导入格式化工具
    import('./formatter.js').then(({ parseTime }) => {
      return parseTime(time, pattern);
    });
  };
  static downFile = downloadFile;
}
