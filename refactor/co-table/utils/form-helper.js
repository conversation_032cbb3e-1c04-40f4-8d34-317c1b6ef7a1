/**
 * Co-Table 表单辅助工具
 * 提供表单事件处理、数据绑定等功能
 */

import { getType } from './index.js';

/**
 * 处理表单事件
 * @param {string} handleName - 事件名称
 * @param {any} value - 事件值
 * @param {boolean} inTable - 是否在表格内
 * @param {object} attrs - 属性对象
 * @param {object} itemAttrs - 项目属性
 * @param {Function} emit - emit 函数
 */
export function handleFormEvent(handleName, value, inTable = true, attrs, itemAttrs, emit = null) {
  const { item, data = null, row = data, index, handle, formRef, type } = attrs;
  const multiple = itemAttrs?.multiple;
  const isSelectMultiple = multiple && item.type === 'select' && getType(multiple) === 'String';
  
  if (inTable) {
    // 表格内表单处理
    if (isSelectMultiple) {
      value = value.join(multiple);
    }
    
    // 处理自定义事件
    if (item.events) {
      const handleType = getType(item.events[handleName]);
      if (handleType === 'Boolean' && item.events[handleName] === false) return;
      if (handleType === 'Function') {
        return item.events[handleName]({ 
          handle: handleName, 
          type, 
          prop: item.prop, 
          row, 
          index, 
          value 
        });
      }
    }
    
    // 调用表格处理函数
    if (handle) {
      handle({ handle: handleName, type, prop: item.prop, row, index, value, formRef });
    }
    return;
  }
  
  // 非表格内表单处理
  if (getType(value) === 'Object') {
    // 处理对象类型的值
    for (const key in value) {
      if (emit) {
        emit('change', { prop: key, value: value[key] });
      }
    }
    return;
  }
  
  if (item.prop) {
    // 处理前置字段缓存
    if (item.prop.includes('_prepend') && value) {
      // 这里可以添加缓存逻辑
    }
    
    // 发送 change 事件
    if (emit) {
      emit('change', { prop: item.prop, value });
    }
    
    // 处理自定义事件
    if (item.events) {
      const handleType = getType(item.events[handleName]);
      if (handleType === 'Boolean' && item.events[handleName] === false) return;
      if (handleType === 'Function') {
        return item.events[handleName]({ 
          handle: handleName, 
          type: item.type, 
          prop: item.prop, 
          value 
        });
      }
    }
  }
  
  // 处理多选字符串拼接
  if (isSelectMultiple && row) {
    row[item.prop] = value.join(multiple);
  }
}

/**
 * 初始化表单字段
 * @param {object} formModel - 表单模型
 * @param {object} item - 字段配置
 * @param {any} defaultValue - 默认值
 */
export function initFormField(formModel, item, defaultValue = '') {
  if (!formModel[item.prop]) {
    if (item.attrs?.multiple) {
      formModel[item.prop] = Array.isArray(defaultValue) ? defaultValue : [];
    } else {
      formModel[item.prop] = defaultValue;
    }
  }
}

/**
 * 处理多选字段的值转换
 * @param {any} value - 原始值
 * @param {boolean} isMultiple - 是否多选
 * @param {string} separator - 分隔符
 * @returns {any} 转换后的值
 */
export function handleMultipleValue(value, isMultiple, separator = ',') {
  if (!isMultiple) return value;
  
  const valueType = getType(value);
  if (['String', 'Number'].includes(valueType)) {
    return value.toString().split(separator).map(item => {
      const num = Number(item);
      return isNaN(num) ? item : num;
    });
  }
  
  return Array.isArray(value) ? value : [];
}

/**
 * 处理日期字段的分割逻辑
 * @param {any} value - 日期值
 * @param {object} item - 字段配置
 * @param {object} formModel - 表单模型
 * @param {object} splitData - 分割数据对象
 */
export function handleDateSplit(value, item, formModel, splitData) {
  const hasSplit = item.splitProp;
  
  if (value) {
    const isArrayValue = Array.isArray(value);
    
    // 有分割字段且为数组格式
    if (isArrayValue && getType(hasSplit) === 'Array') {
      if (!item.prepend) {
        splitData[hasSplit[0]] = value[0];
        splitData[hasSplit[1]] = value[1];
      }
    } else {
      // 默认逗号拼接
      const finalValue = isArrayValue && !item.prepend ? value.join(hasSplit) : value;
      splitData[item.prop] = finalValue;
    }
  } else {
    // 清空值
    formModel[item.prop] = value;
    if (hasSplit && !item.prepend) {
      splitData[hasSplit[0]] = '';
      splitData[hasSplit[1]] = '';
    } else {
      splitData[item.prop] = '';
    }
  }
}

/**
 * 重置日期字段
 * @param {any} value - 重置值
 * @param {object} item - 字段配置
 * @param {object} formModel - 表单模型
 * @param {object} splitData - 分割数据对象
 * @param {object} resetCounter - 重置计数器
 */
export function resetDateField(value, item, formModel, splitData, resetCounter) {
  const splitProp = item.splitProp;
  const itemProp = item.prop;
  
  if (value) {
    if (getType(value) === 'String') {
      const hasComma = value.includes(',');
      let echoValue = value;
      
      if (hasComma) {
        // 有逗号分隔的日期范围
        echoValue = value.split(',');
        formModel[itemProp] = echoValue;
        if (splitProp) {
          splitData[splitProp[0]] = echoValue[0];
          splitData[splitProp[1]] = echoValue[1];
        }
      } else {
        // 单个日期
        if (splitProp) {
          if (resetCounter.value < 1) {
            formModel[itemProp] = [value];
          } else {
            formModel[itemProp] = [...formModel[itemProp], value];
          }
          splitData[splitProp[0]] = echoValue[0];
          splitData[splitProp[1]] = echoValue[1];
        } else {
          formModel[itemProp] = echoValue;
        }
      }
    }
  } else {
    // 重置为空
    formModel[itemProp] = value;
    if (splitProp) {
      splitData[splitProp[0]] = value;
      splitData[splitProp[1]] = value;
    }
  }
  
  // 处理重置计数
  if (splitProp) {
    if (value) {
      resetCounter.value += 1;
    } else {
      resetCounter.value = 0;
    }
  }
}

/**
 * 创建表单字段管理器
 * @param {object} formModel - 表单模型
 * @param {object} item - 字段配置
 * @returns {object} 字段管理器
 */
export function createFieldManager(formModel, item) {
  return {
    getValue: () => formModel[item.prop],
    setValue: (value) => {
      formModel[item.prop] = value;
    },
    reset: (value = '') => {
      formModel[item.prop] = value;
    },
    validate: () => {
      // 这里可以添加字段验证逻辑
      return true;
    }
  };
}
