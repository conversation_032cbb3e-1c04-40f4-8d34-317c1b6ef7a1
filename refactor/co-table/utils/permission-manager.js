/**
 * Co-Table 权限管理器
 * 提供全局权限配置和管理功能
 */

import { reactive } from 'vue';
import { getGlobalConfig, setGlobalConfig } from '../config/index.js';

/**
 * 全局权限管理器
 */
class PermissionManager {
  constructor() {
    // 权限处理器注册表
    this.handlers = reactive({
      // 权限获取处理器
      getPermissions: null,
      // 权限检查处理器
      checkPermission: null,
      // 按钮规则解析处理器
      parseButtonRule: null,
      // 权限缓存处理器
      cachePermissions: null,
      // 权限清除处理器
      clearPermissions: null,
      // 路由权限获取处理器
      getRoutePermissions: null
    });
    
    // 权限配置
    this.config = reactive({
      // 是否启用权限检查
      enabled: true,
      // 路由权限字段名
      metaPermisKey: 'perms',
      // 默认权限
      defaultPermissions: [],
      // 权限缓存配置
      cache: {
        enabled: true,
        key: 'co_table_permissions',
        duration: 30 * 60 * 1000 // 30分钟
      }
    });
    
    // 权限缓存
    this.cache = new Map();
  }

  /**
   * 注册权限处理器
   * @param {string} type - 处理器类型
   * @param {Function} handler - 处理器函数
   */
  registerHandler(type, handler) {
    if (typeof handler !== 'function') {
      console.warn(`权限处理器 ${type} 必须是函数类型`);
      return;
    }
    
    this.handlers[type] = handler;
    console.log(`权限处理器 ${type} 注册成功`);
  }

  /**
   * 注销权限处理器
   * @param {string} type - 处理器类型
   */
  unregisterHandler(type) {
    this.handlers[type] = null;
    console.log(`权限处理器 ${type} 注销成功`);
  }

  /**
   * 获取权限处理器
   * @param {string} type - 处理器类型
   * @returns {Function|null} 处理器函数
   */
  getHandler(type) {
    return this.handlers[type];
  }

  /**
   * 设置权限配置
   * @param {object} config - 权限配置
   */
  setConfig(config) {
    Object.assign(this.config, config);
    
    // 同步到全局配置
    setGlobalConfig({
      permission: this.config
    });
  }

  /**
   * 获取权限配置
   * @param {string} key - 配置键名
   * @returns {any} 配置值
   */
  getConfig(key) {
    if (key) {
      return this.config[key];
    }
    return this.config;
  }

  /**
   * 获取用户权限
   * @param {object} options - 选项
   * @returns {Promise<Array>} 权限列表
   */
  async getPermissions(options = {}) {
    const handler = this.handlers.getPermissions;
    
    if (handler) {
      try {
        const permissions = await handler(options);
        
        // 缓存权限
        if (this.config.cache.enabled) {
          this.setCache('permissions', permissions);
        }
        
        return Array.isArray(permissions) ? permissions : [];
      } catch (error) {
        console.error('获取权限失败:', error);
        return this.config.defaultPermissions;
      }
    }
    
    // 尝试从缓存获取
    if (this.config.cache.enabled) {
      const cached = this.getCache('permissions');
      if (cached) {
        return cached;
      }
    }
    
    return this.config.defaultPermissions;
  }

  /**
   * 检查权限
   * @param {string|Array} permission - 权限标识
   * @param {Array} userPermissions - 用户权限列表
   * @param {object} options - 选项
   * @returns {boolean} 是否有权限
   */
  checkPermission(permission, userPermissions = [], options = {}) {
    if (!this.config.enabled) {
      return true;
    }
    
    const handler = this.handlers.checkPermission;
    
    if (handler) {
      try {
        return handler(permission, userPermissions, options);
      } catch (error) {
        console.error('权限检查失败:', error);
        return false;
      }
    }
    
    // 默认权限检查逻辑
    if (!permission) return true;
    if (!userPermissions || userPermissions.length === 0) return false;
    
    if (Array.isArray(permission)) {
      return permission.every(perm => userPermissions.includes(perm));
    }
    
    return userPermissions.includes(permission);
  }

  /**
   * 解析按钮规则
   * @param {string} rule - 规则表达式
   * @param {object} context - 上下文
   * @returns {boolean} 规则执行结果
   */
  parseButtonRule(rule, context = {}) {
    const handler = this.handlers.parseButtonRule;
    
    if (handler) {
      try {
        return handler(rule, context);
      } catch (error) {
        console.error('按钮规则解析失败:', error);
        return false;
      }
    }
    
    // 默认规则解析逻辑
    if (!rule) return true;
    
    try {
      const { row = {}, storage = {}, store = null } = context;
      const func = new Function('row', 'storage', 'store', `return ${rule}`);
      return Boolean(func(row, storage, store));
    } catch (error) {
      console.warn('按钮规则解析失败:', rule, error);
      return false;
    }
  }

  /**
   * 获取路由权限
   * @param {object} route - 路由对象
   * @returns {Array} 路由权限列表
   */
  getRoutePermissions(route) {
    const handler = this.handlers.getRoutePermissions;
    
    if (handler) {
      try {
        return handler(route);
      } catch (error) {
        console.error('获取路由权限失败:', error);
        return [];
      }
    }
    
    // 默认路由权限获取逻辑
    if (!route || !route.meta) return [];
    
    const permissions = route.meta[this.config.metaPermisKey];
    if (!permissions) return [];
    
    return Array.isArray(permissions) ? permissions : [permissions];
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   */
  setCache(key, value) {
    if (!this.config.cache.enabled) return;
    
    const cacheKey = `${this.config.cache.key}_${key}`;
    const cacheData = {
      value,
      timestamp: Date.now(),
      duration: this.config.cache.duration
    };
    
    this.cache.set(cacheKey, cacheData);
    
    // 同步到 localStorage
    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('权限缓存写入失败:', error);
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存值
   */
  getCache(key) {
    if (!this.config.cache.enabled) return null;
    
    const cacheKey = `${this.config.cache.key}_${key}`;
    
    // 先从内存缓存获取
    let cacheData = this.cache.get(cacheKey);
    
    // 如果内存中没有，尝试从 localStorage 获取
    if (!cacheData) {
      try {
        const stored = localStorage.getItem(cacheKey);
        if (stored) {
          cacheData = JSON.parse(stored);
          this.cache.set(cacheKey, cacheData);
        }
      } catch (error) {
        console.warn('权限缓存读取失败:', error);
        return null;
      }
    }
    
    if (!cacheData) return null;
    
    // 检查缓存是否过期
    const now = Date.now();
    if (now - cacheData.timestamp > cacheData.duration) {
      this.clearCache(key);
      return null;
    }
    
    return cacheData.value;
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键，不提供则清除所有
   */
  clearCache(key) {
    if (key) {
      const cacheKey = `${this.config.cache.key}_${key}`;
      this.cache.delete(cacheKey);
      
      try {
        localStorage.removeItem(cacheKey);
      } catch (error) {
        console.warn('权限缓存清除失败:', error);
      }
    } else {
      // 清除所有缓存
      this.cache.clear();
      
      try {
        const keys = Object.keys(localStorage);
        keys.forEach(k => {
          if (k.startsWith(this.config.cache.key)) {
            localStorage.removeItem(k);
          }
        });
      } catch (error) {
        console.warn('权限缓存清除失败:', error);
      }
    }
    
    // 调用自定义清除处理器
    const handler = this.handlers.clearPermissions;
    if (handler) {
      try {
        handler(key);
      } catch (error) {
        console.error('权限清除处理器执行失败:', error);
      }
    }
  }

  /**
   * 重置权限管理器
   */
  reset() {
    // 清除所有处理器
    Object.keys(this.handlers).forEach(key => {
      this.handlers[key] = null;
    });
    
    // 重置配置
    this.config.enabled = true;
    this.config.metaPermisKey = 'perms';
    this.config.defaultPermissions = [];
    
    // 清除缓存
    this.clearCache();
  }
}

// 创建全局权限管理器实例
export const permissionManager = new PermissionManager();

// 导出权限管理器类
export default PermissionManager;
