/**
 * Co-Table 权限工具函数
 * 提供权限检查、按钮权限控制等功能
 */

/**
 * 检查用户是否有指定权限
 * @param {string|Array} permission - 权限标识或权限数组
 * @param {Array} userPermissions - 用户权限列表
 * @returns {boolean} 是否有权限
 * @example
 * hasPermission('user:add', ['user:add', 'user:edit']) // true
 * hasPermission(['user:add', 'user:edit'], ['user:add']) // false (需要全部权限)
 */
export function hasPermission(permission, userPermissions = []) {
  if (!permission) return true;
  if (!userPermissions || userPermissions.length === 0) return false;
  
  if (Array.isArray(permission)) {
    // 需要拥有所有权限
    return permission.every(perm => userPermissions.includes(perm));
  }
  
  return userPermissions.includes(permission);
}

/**
 * 检查用户是否有任一权限
 * @param {Array} permissions - 权限数组
 * @param {Array} userPermissions - 用户权限列表
 * @returns {boolean} 是否有任一权限
 * @example
 * hasAnyPermission(['user:add', 'user:edit'], ['user:add']) // true
 */
export function hasAnyPermission(permissions, userPermissions = []) {
  if (!permissions || permissions.length === 0) return true;
  if (!userPermissions || userPermissions.length === 0) return false;
  
  return permissions.some(perm => userPermissions.includes(perm));
}

/**
 * 解析按钮规则表达式
 * @param {string} rule - 规则表达式
 * @param {object} row - 行数据
 * @param {object} storage - 存储对象
 * @param {object} store - 状态管理对象
 * @returns {boolean} 规则执行结果
 * @example
 * parseButtonRule('row.status === 1', {status: 1}) // true
 * parseButtonRule('storage.user.role === "admin"', {}, {user: {role: 'admin'}}) // true
 */
export function parseButtonRule(rule, row = {}, storage = {}, store = null) {
  if (!rule) return true;
  
  try {
    // 处理 storage 中的数据
    if (rule.includes('storage.')) {
      const storageMatches = rule.match(/storage\.(\w+(?:\.\w+)*)/g);
      if (storageMatches) {
        storageMatches.forEach(match => {
          const key = match.replace('storage.', '');
          const keys = key.split('.');
          let value = storage;
          
          for (const k of keys) {
            value = value?.[k];
          }
          
          // 如果 storage 中没有数据，尝试从 localStorage 或 sessionStorage 获取
          if (value === undefined) {
            const rootKey = keys[0];
            const storageValue = sessionStorage.getItem(rootKey) || localStorage.getItem(rootKey);
            if (storageValue) {
              try {
                const parsed = JSON.parse(storageValue);
                if (keys.length > 1) {
                  let temp = parsed;
                  for (let i = 1; i < keys.length; i++) {
                    temp = temp?.[keys[i]];
                  }
                  value = temp;
                } else {
                  value = parsed;
                }
              } catch {
                value = storageValue;
              }
            }
          }
          
          // 将 storage 对象更新
          if (value !== undefined) {
            const rootKey = keys[0];
            if (!storage[rootKey]) {
              storage[rootKey] = {};
            }
            let current = storage[rootKey];
            for (let i = 1; i < keys.length - 1; i++) {
              if (!current[keys[i]]) {
                current[keys[i]] = {};
              }
              current = current[keys[i]];
            }
            if (keys.length > 1) {
              current[keys[keys.length - 1]] = value;
            } else {
              storage[rootKey] = value;
            }
          }
        });
      }
    }
    
    // 创建安全的执行环境
    const func = new Function('row', 'storage', 'store', `return ${rule}`);
    return Boolean(func(row, storage, store));
  } catch (error) {
    console.warn('按钮规则解析失败:', rule, error);
    return false;
  }
}

/**
 * 过滤按钮列表（根据权限和规则）
 * @param {Array} buttons - 按钮列表
 * @param {object} options - 选项
 * @param {Array} options.userPermissions - 用户权限
 * @param {object} options.row - 行数据
 * @param {object} options.storage - 存储对象
 * @param {object} options.store - 状态管理对象
 * @param {string} options.tableKey - 表格标识
 * @returns {Array} 过滤后的按钮列表
 */
export function filterButtons(buttons, options = {}) {
  const {
    userPermissions = [],
    row = {},
    storage = {},
    store = null,
    tableKey = ''
  } = options;
  
  return buttons.filter(button => {
    // 检查权限
    if (button.permission && !hasPermission(button.permission, userPermissions)) {
      return false;
    }
    
    // 检查规则
    if (button.rule && !parseButtonRule(button.rule, row, storage, store)) {
      return false;
    }
    
    // 检查表格标识
    if (button.tableKey && !tableKey.includes(button.tableKey)) {
      return false;
    }
    
    return true;
  });
}

/**
 * 获取路由权限
 * @param {object} route - 路由对象
 * @param {string} permissionKey - 权限字段名
 * @returns {Array} 权限列表
 * @example
 * getRoutePermissions(route, 'perms') // ['user:add', 'user:edit']
 */
export function getRoutePermissions(route, permissionKey = 'perms') {
  if (!route || !route.meta) return [];
  
  const permissions = route.meta[permissionKey];
  if (!permissions) return [];
  
  return Array.isArray(permissions) ? permissions : [permissions];
}

/**
 * 合并权限列表
 * @param {Array} routePermissions - 路由权限
 * @param {Array} buttonPermissions - 按钮权限
 * @param {string} mergeMode - 合并模式：'push' | 'unshift' | 'replace'
 * @returns {Array} 合并后的权限列表
 */
export function mergePermissions(routePermissions = [], buttonPermissions = [], mergeMode = 'replace') {
  if (!routePermissions.length) return buttonPermissions;
  if (!buttonPermissions.length) return routePermissions;
  
  switch (mergeMode) {
    case 'push':
      return [...routePermissions, ...buttonPermissions];
    case 'unshift':
      return [...buttonPermissions, ...routePermissions];
    case 'replace':
    default:
      return routePermissions;
  }
}

/**
 * 创建权限检查器
 * @param {Array} userPermissions - 用户权限列表
 * @returns {object} 权限检查器对象
 * @example
 * const checker = createPermissionChecker(['user:add', 'user:edit']);
 * checker.has('user:add') // true
 * checker.hasAny(['user:add', 'user:delete']) // true
 */
export function createPermissionChecker(userPermissions = []) {
  return {
    has: (permission) => hasPermission(permission, userPermissions),
    hasAny: (permissions) => hasAnyPermission(permissions, userPermissions),
    hasAll: (permissions) => hasPermission(permissions, userPermissions),
    getPermissions: () => [...userPermissions]
  };
}
