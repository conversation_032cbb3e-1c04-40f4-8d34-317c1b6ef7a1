# Co-Table 重构项目

## 项目概述

这是 co-table 组件从 Options API 迁移到 Composition API 的渐进式重构项目。目标是统一 co-table 和 page-table 为一个组件，提高代码的可维护性、可复用性和灵活性，同时保持 API 向后兼容性。

## 技术栈

- Vue 3 Composition API
- `<script setup>` 语法
- Element Plus
- 纯 JavaScript（不使用 TypeScript）

## 目录结构

```
refactor/co-table/
├── index.vue                    # 统一主组件（整合 co-table 和 page-table）
├── components/                  # 子组件目录
│   ├── co-table-search.vue     # 搜索组件
│   ├── co-table-button.vue     # 按钮组件
│   ├── co-table-container.vue  # 容器组件
│   ├── co-table-upload.vue     # 上传组件
│   ├── co-table-static.vue     # 静态组件
│   └── form-items/             # 表单项组件
│       ├── co-table-input.vue
│       ├── co-table-select.vue
│       ├── co-table-date.vue
│       └── co-table-switch.vue
├── composables/                # 组合式函数
│   ├── useTable.js             # 核心表格逻辑
│   ├── useSearch.js            # 搜索逻辑
│   ├── usePermission.js        # 权限逻辑
│   └── useOperation.js         # 操作逻辑
├── utils/                      # 工具函数库
│   ├── index.js                # 通用工具
│   ├── permission.js           # 权限工具
│   └── formatter.js            # 格式化工具
├── config/                     # 配置管理
│   ├── index.js                # 默认配置
│   └── enums.js                # 简化枚举
├── styles/                     # 样式文件
│   └── index.scss              # 主样式文件
└── docs/                       # 文档目录
    ├── api.md                  # API 文档
    ├── examples.md             # 使用示例
    └── migration.md            # 迁移指南
```

## 设计原则

1. **分层架构**：组件、Hook、工具、配置四层分离
2. **向后兼容**：保持现有 API 接口不变
3. **响应式优先**：确保视图响应式更新
4. **模块化设计**：每个模块职责单一，便于维护
5. **权限抽离**：支持统一的权限处理机制

## 开发状态

- [x] 基础设施建设
- [ ] 工具函数和配置系统重构
- [ ] 表单组件重构
- [ ] 基础组件重构
- [ ] 搜索功能重构
- [ ] 核心表格重构
- [ ] 权限系统抽离
- [ ] 组件统一整合
- [ ] 测试和文档

## 使用说明

重构完成后，组件的使用方式将保持与原版本完全一致，现有页面无需修改即可使用新版本。

## 注意事项

- 所有组件使用 `co-table-` 前缀命名
- 保持与 Element Plus 的兼容性
- 确保响应式更新机制正常工作
- 支持回调传递机制
