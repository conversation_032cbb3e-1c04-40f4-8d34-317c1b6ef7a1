# Co-Table 开发指南

## 开发环境设置

### 前置要求

- Node.js >= 16.0.0
- Vue 3.x
- Element Plus 2.x

### 目录说明

```
refactor/co-table/
├── README.md                   # 项目说明
├── DEVELOPMENT.md              # 开发指南（本文件）
├── package.json                # 项目配置
├── .gitignore                  # Git 忽略文件
├── index.vue                   # 主组件入口（待创建）
├── components/                 # 子组件目录
│   ├── co-table-search.vue    # 搜索组件（待创建）
│   ├── co-table-button.vue    # 按钮组件（待创建）
│   ├── co-table-container.vue # 容器组件（待创建）
│   ├── co-table-upload.vue    # 上传组件（待创建）
│   ├── co-table-static.vue    # 静态组件（待创建）
│   └── form-items/            # 表单项组件
│       ├── co-table-input.vue    # 输入框组件（待创建）
│       ├── co-table-select.vue   # 选择器组件（待创建）
│       ├── co-table-date.vue     # 日期组件（待创建）
│       └── co-table-switch.vue   # 开关组件（待创建）
├── composables/               # 组合式函数
│   ├── useTable.js           # 核心表格逻辑（待创建）
│   ├── useSearch.js          # 搜索逻辑（待创建）
│   ├── usePermission.js      # 权限逻辑（待创建）
│   └── useOperation.js       # 操作逻辑（待创建）
├── utils/                    # 工具函数库
│   ├── index.js             # 通用工具（待创建）
│   ├── permission.js        # 权限工具（待创建）
│   └── formatter.js         # 格式化工具（待创建）
├── config/                  # 配置管理
│   ├── index.js            # 默认配置（待创建）
│   └── enums.js            # 简化枚举（待创建）
├── styles/                 # 样式文件
│   └── index.scss          # 主样式文件
└── docs/                   # 文档目录
    ├── api.md             # API 文档（待创建）
    ├── examples.md        # 使用示例（待创建）
    └── migration.md       # 迁移指南（待创建）
```

## 开发规范

### 命名规范

1. **组件命名**：所有组件使用 `co-table-` 前缀
2. **文件命名**：使用 kebab-case（短横线分隔）
3. **函数命名**：使用 camelCase
4. **常量命名**：使用 UPPER_SNAKE_CASE

### 代码规范

1. **使用 Composition API**：所有组件使用 `<script setup>` 语法
2. **响应式数据**：使用 `ref` 和 `reactive` 管理状态
3. **类型注释**：使用 JSDoc 注释说明函数参数和返回值
4. **模块化**：每个文件不超过 300 行，超出则拆分

### 兼容性要求

1. **API 兼容**：保持与原 co-table 和 page-table 的 API 兼容
2. **样式兼容**：保持与原有样式的兼容性
3. **功能兼容**：确保所有原有功能正常工作

## 开发流程

1. **任务执行顺序**：按照依赖关系执行任务
2. **代码审查**：每个任务完成后进行代码审查
3. **测试验证**：确保功能正常且兼容性良好
4. **文档更新**：及时更新相关文档

## 注意事项

- 确保响应式更新机制正常工作
- 支持回调传递机制（如 refresh 方法）
- 权限系统要能灵活扩展
- 保持与 Element Plus 的兼容性
