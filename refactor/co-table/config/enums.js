/**
 * Co-Table 枚举定义
 * 提供简化的枚举使用方法
 */

/**
 * 日期类型枚举
 */
export const DATE_TYPES = [
  'time',
  'timeselect', 
  'year',
  'years',
  'month',
  'dates',
  'date',
  'week',
  'months',
  'datetime',
  'datetimerange',
  'daterange',
  'monthrange',
  'yearrange'
];

/**
 * 表格列类型枚举
 */
export const COLUMN_TYPES = {
  INDEX: 'index',
  SELECTION: 'selection',
  EXPAND: 'expand',
  TEXT: 'text',
  INPUT: 'input',
  SELECT: 'select',
  DATE: 'date',
  SWITCH: 'switch',
  UPLOAD: 'upload',
  DOWNLOAD: 'download',
  PREVIEW: 'preview',
  IMAGE: 'img',
  ENUM: 'enum',
  OPERATION: 'operation'
};

/**
 * 按钮类型枚举
 */
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
  TEXT: 'text',
  DEFAULT: 'default'
};

/**
 * 按钮尺寸枚举
 */
export const BUTTON_SIZES = {
  LARGE: 'large',
  DEFAULT: 'default',
  SMALL: 'small'
};

/**
 * 表格尺寸枚举
 */
export const TABLE_SIZES = {
  LARGE: 'large',
  DEFAULT: 'default',
  SMALL: 'small'
};

/**
 * 对齐方式枚举
 */
export const ALIGN_TYPES = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right'
};

/**
 * 固定列位置枚举
 */
export const FIXED_TYPES = {
  LEFT: 'left',
  RIGHT: 'right'
};

/**
 * 排序方向枚举
 */
export const SORT_ORDERS = {
  ASC: 'ascending',
  DESC: 'descending'
};

/**
 * 表单标签位置枚举
 */
export const LABEL_POSITIONS = {
  LEFT: 'left',
  RIGHT: 'right',
  TOP: 'top'
};

/**
 * 通用状态枚举
 */
export const COMMON_STATUS = {
  ENABLED: { label: '启用', value: 1, color: 'success' },
  DISABLED: { label: '禁用', value: 0, color: 'danger' }
};

/**
 * 是否枚举
 */
export const YES_NO = {
  YES: { label: '是', value: 1, color: 'success' },
  NO: { label: '否', value: 0, color: 'info' }
};

/**
 * 创建枚举工具
 * @param {Array|Object} data - 枚举数据
 * @param {object} options - 选项
 * @returns {object} 枚举工具对象
 * @example
 * const statusEnum = createEnum([
 *   { label: '启用', value: 1, color: 'success' },
 *   { label: '禁用', value: 0, color: 'danger' }
 * ]);
 * statusEnum.getLabel(1) // '启用'
 * statusEnum.getColor(1) // 'success'
 * statusEnum.getOptions() // 返回所有选项
 */
export function createEnum(data, options = {}) {
  const {
    labelKey = 'label',
    valueKey = 'value',
    colorKey = 'color'
  } = options;
  
  // 标准化数据格式
  let enumData = [];
  if (Array.isArray(data)) {
    enumData = data;
  } else if (typeof data === 'object') {
    enumData = Object.entries(data).map(([key, value]) => {
      if (typeof value === 'object') {
        return { ...value, key };
      }
      return { [labelKey]: value, [valueKey]: key, key };
    });
  }
  
  return {
    /**
     * 获取标签
     * @param {any} value - 值
     * @returns {string} 标签
     */
    getLabel(value) {
      const item = enumData.find(item => item[valueKey] === value);
      return item ? item[labelKey] : value;
    },
    
    /**
     * 获取颜色
     * @param {any} value - 值
     * @returns {string} 颜色
     */
    getColor(value) {
      const item = enumData.find(item => item[valueKey] === value);
      return item ? item[colorKey] : '';
    },
    
    /**
     * 获取完整项
     * @param {any} value - 值
     * @returns {object} 完整项
     */
    getItem(value) {
      return enumData.find(item => item[valueKey] === value);
    },
    
    /**
     * 获取所有选项
     * @returns {Array} 所有选项
     */
    getOptions() {
      return [...enumData];
    },
    
    /**
     * 获取所有值
     * @returns {Array} 所有值
     */
    getValues() {
      return enumData.map(item => item[valueKey]);
    },
    
    /**
     * 获取所有标签
     * @returns {Array} 所有标签
     */
    getLabels() {
      return enumData.map(item => item[labelKey]);
    },
    
    /**
     * 检查值是否存在
     * @param {any} value - 值
     * @returns {boolean} 是否存在
     */
    hasValue(value) {
      return enumData.some(item => item[valueKey] === value);
    },
    
    /**
     * 转换为对象格式
     * @returns {object} 对象格式的枚举
     */
    toObject() {
      const result = {};
      enumData.forEach(item => {
        result[item[valueKey]] = item[labelKey];
      });
      return result;
    },
    
    /**
     * 转换为 Map 格式
     * @returns {Map} Map 格式的枚举
     */
    toMap() {
      const map = new Map();
      enumData.forEach(item => {
        map.set(item[valueKey], item);
      });
      return map;
    }
  };
}

/**
 * 创建简单枚举（只有标签和值）
 * @param {object} data - 枚举数据对象
 * @returns {object} 枚举工具对象
 * @example
 * const statusEnum = createSimpleEnum({
 *   1: '启用',
 *   0: '禁用'
 * });
 */
export function createSimpleEnum(data) {
  const enumData = Object.entries(data).map(([value, label]) => ({
    label,
    value: isNaN(value) ? value : Number(value)
  }));
  
  return createEnum(enumData);
}

/**
 * 创建带颜色的枚举
 * @param {Array} data - 枚举数据
 * @param {object} colors - 颜色映射
 * @returns {object} 枚举工具对象
 * @example
 * const statusEnum = createColorEnum([
 *   { label: '启用', value: 1 },
 *   { label: '禁用', value: 0 }
 * ], {
 *   1: 'success',
 *   0: 'danger'
 * });
 */
export function createColorEnum(data, colors = {}) {
  const enumData = data.map(item => ({
    ...item,
    color: colors[item.value] || ''
  }));
  
  return createEnum(enumData);
}

// 导出常用枚举实例
export const dateTypeEnum = createEnum(DATE_TYPES.map(type => ({ label: type, value: type })));
export const columnTypeEnum = createEnum(Object.entries(COLUMN_TYPES).map(([key, value]) => ({ label: key, value })));
export const buttonTypeEnum = createEnum(Object.entries(BUTTON_TYPES).map(([key, value]) => ({ label: key, value })));
export const commonStatusEnum = createEnum([COMMON_STATUS.ENABLED, COMMON_STATUS.DISABLED]);
export const yesNoEnum = createEnum([YES_NO.YES, YES_NO.NO]);

// 向后兼容导出
export const dateType = DATE_TYPES;
export { DATE_TYPES as dateTypes };
export { COLUMN_TYPES as columnTypes };
export { BUTTON_TYPES as buttonTypes };
