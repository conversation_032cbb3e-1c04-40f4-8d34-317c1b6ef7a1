/**
 * Co-Table 默认配置
 * 支持全局注册和灵活配置
 */

import { deepMerge } from '../utils/index.js';

/**
 * 默认配置对象
 */
export const defaultConfig = {
  // 权限相关配置
  permission: {
    // 路由权限字段名
    metaPermisKey: 'perms',
    // 是否启用权限检查
    enabled: true
  },
  
  // 搜索组件配置
  search: {
    // 搜索框样式
    style: null,
    // 搜索按钮配置
    searchButton: {
      type: 'primary',
      icon: 'Search',
      text: '搜索'
    },
    // 重置按钮配置
    resetButton: {
      type: 'default',
      icon: 'Refresh',
      text: '重置'
    },
    // 表单配置
    form: {
      inline: true,
      size: 'default',
      labelPosition: 'right'
    }
  },
  
  // 表格配置
  table: {
    // 是否显示加载状态
    loading: true,
    // Element Plus 表格属性
    attrs: {
      'header-cell-style': {
        backgroundColor: '#f5f7fa',
        color: '#303133'
      },
      stripe: false,
      border: false,
      size: 'default'
    },
    // 空数据显示
    emptyText: '暂无数据'
  },
  
  // 分页配置
  pagination: {
    // 请求参数映射
    request: {
      current: 'current',
      size: 'size'
    },
    // 响应数据映射
    response: {
      current: 'current',
      pages: 'pages',
      size: 'size',
      total: 'total',
      records: 'list'
    },
    // 分页组件配置
    component: {
      layout: 'total, sizes, prev, pager, next, jumper',
      pageSizes: [10, 20, 50, 100],
      size: 'default'
    }
  },
  
  // 字典配置
  dictionary: {
    // 获取字典数据的方法
    getDic: null,
    // 字典数据字段映射
    keys: {
      label: 'label',
      value: 'value'
    },
    // 缓存配置
    cache: {
      enabled: true,
      duration: 5 * 60 * 1000 // 5分钟
    }
  },
  
  // 文件上传配置
  upload: {
    // 上传方法
    method: null,
    // 默认配置
    default: {
      multiple: false,
      limit: 1,
      autoUpload: true,
      showFileList: false,
      accept: '*',
      maxSize: 10 // MB
    }
  },
  
  // 文件下载配置
  download: {
    // 下载方法
    method: null
  },
  
  // 操作按钮配置
  operation: {
    // 默认配置
    default: {
      showCount: -1, // -1 表示显示全部
      fixed: 'right',
      align: 'center',
      trigger: 'hover'
    },
    // 更多按钮配置
    more: {
      width: 200,
      text: '更多',
      attrs: {
        type: 'text',
        size: 'default'
      }
    }
  },
  
  // 主题配置
  theme: {
    // 主色调
    primaryColor: '#409EFF',
    // 成功色
    successColor: '#67C23A',
    // 警告色
    warningColor: '#E6A23C',
    // 危险色
    dangerColor: '#F56C6C',
    // 信息色
    infoColor: '#909399'
  }
};

/**
 * 全局配置对象
 */
let globalConfig = { ...defaultConfig };

/**
 * 设置全局配置
 * @param {object} config - 配置对象
 * @example
 * setGlobalConfig({
 *   search: {
 *     searchButton: { text: 'Search' }
 *   }
 * });
 */
export function setGlobalConfig(config) {
  globalConfig = deepMerge(globalConfig, config);
}

/**
 * 获取全局配置
 * @param {string} key - 配置键名，支持点号分隔的路径
 * @returns {any} 配置值
 * @example
 * getGlobalConfig('search.searchButton.text') // '搜索'
 * getGlobalConfig('search') // 整个搜索配置对象
 */
export function getGlobalConfig(key) {
  if (!key) return globalConfig;
  
  const keys = key.split('.');
  let result = globalConfig;
  
  for (const k of keys) {
    result = result?.[k];
    if (result === undefined) break;
  }
  
  return result;
}

/**
 * 重置全局配置
 */
export function resetGlobalConfig() {
  globalConfig = { ...defaultConfig };
}

/**
 * 合并配置
 * @param {object} userConfig - 用户配置
 * @param {object} baseConfig - 基础配置
 * @returns {object} 合并后的配置
 */
export function mergeConfig(userConfig = {}, baseConfig = globalConfig) {
  return deepMerge(baseConfig, userConfig);
}

/**
 * 创建配置管理器
 * @param {object} initialConfig - 初始配置
 * @returns {object} 配置管理器
 */
export function createConfigManager(initialConfig = {}) {
  let config = deepMerge(globalConfig, initialConfig);
  
  return {
    get: (key) => {
      if (!key) return config;
      const keys = key.split('.');
      let result = config;
      for (const k of keys) {
        result = result?.[k];
        if (result === undefined) break;
      }
      return result;
    },
    
    set: (key, value) => {
      const keys = key.split('.');
      let current = config;
      
      for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i];
        if (!current[k] || typeof current[k] !== 'object') {
          current[k] = {};
        }
        current = current[k];
      }
      
      current[keys[keys.length - 1]] = value;
    },
    
    merge: (newConfig) => {
      config = deepMerge(config, newConfig);
    },
    
    reset: () => {
      config = deepMerge(globalConfig, initialConfig);
    },
    
    getAll: () => ({ ...config })
  };
}

// 导出默认配置（向后兼容）
export default globalConfig;
