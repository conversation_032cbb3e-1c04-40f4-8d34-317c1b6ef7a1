<!--
  Co-Table 向后兼容入口文件
  保持与原有 co-table 组件的完全兼容
-->
<template>
  <co-table-main v-bind="$attrs" v-on="$listeners">
    <template v-for="(_, slotName) in $slots" :key="slotName" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps || {}" />
    </template>
  </co-table-main>
</template>

<script setup>
import CoTableMain from './index.vue';

// 组件名称
defineOptions({
  name: 'CoTable',
  inheritAttrs: false
});
</script>
