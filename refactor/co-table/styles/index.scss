/**
 * Co-Table 主样式文件
 * 保持与原有样式的兼容性，同时优化样式结构
 */

// 基础样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// CSS 变量定义
:root {
  --co-table-bg-white: #fff;
  --co-table-color-primary: #409EFF;
  --co-table-color-primary-disabled: rgba(25, 163, 223, 0.6);
  --co-table-color-success: #67C23A;
  --co-table-color-warning: #E6A23C;
  --co-table-color-danger: #F56C6C;
  --co-table-color-info: #909399;
  
  // 表格相关变量
  --co-table-header-bg: #f5f7fa;
  --co-table-header-color: #303133;
  --co-table-border-color: #ebeef5;
}

// 表格容器样式
.co-table-page {
  .co-table-container {
    .co-table {
      // 继承原有表格样式
    }
  }
}

// 搜索组件样式
.co-table-search {
  margin-bottom: 16px;
  
  .el-form--inline {
    .el-form-item {
      .el-input,
      .el-cascader,
      .el-select,
      .el-autocomplete {
        width: 220px;
      }
      
      .el-date-editor {
        width: 360px;
        
        &.el-date-editor--datetimerange {
          width: 380px;
        }
        
        &.el-date-editor--monthrange,
        &.el-date-editor--yearrange {
          width: 270px;
        }
      }
    }
  }
}

// 操作按钮样式
.co-table-operation {
  margin-bottom: 16px;
}

// 工具类样式
.co-table-hidden {
  display: none;
}

.co-table-text-primary {
  color: var(--co-table-color-primary);
}

.co-table-text-success {
  color: var(--co-table-color-success);
}

.co-table-text-warning {
  color: var(--co-table-color-warning);
}

.co-table-text-danger {
  color: var(--co-table-color-danger);
}

.co-table-text-info {
  color: var(--co-table-color-info);
}

// 响应式设计
@media (max-width: 768px) {
  .co-table-search {
    .el-form--inline {
      .el-form-item {
        display: block;
        margin-bottom: 16px;
        
        .el-input,
        .el-select,
        .el-date-editor {
          width: 100%;
        }
      }
    }
  }
}
