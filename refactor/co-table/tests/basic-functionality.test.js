/**
 * Co-Table 基础功能测试
 * 验证核心功能的正确性
 */

// 模拟测试环境
const mockVue = {
  ref: (value) => ({ value }),
  reactive: (obj) => obj,
  computed: (fn) => ({ value: fn() }),
  onMounted: (fn) => fn(),
  provide: () => {},
  inject: () => ({}),
  watch: () => {},
  nextTick: () => Promise.resolve()
};

// 模拟Element Plus
const mockElementPlus = {
  ElTable: 'el-table',
  ElTableColumn: 'el-table-column',
  ElButton: 'el-button',
  ElForm: 'el-form',
  ElFormItem: 'el-form-item',
  ElInput: 'el-input',
  ElSelect: 'el-select',
  ElPagination: 'el-pagination'
};

// 导入要测试的模块
import { getType, deepClone, deepMerge } from '../utils/index.js';
import { parseTime, formatNumber } from '../utils/formatter.js';
import { hasPermission, parseButtonRule } from '../utils/permission.js';
import { getGlobalConfig, setGlobalConfig } from '../config/index.js';

/**
 * 工具函数测试
 */
function testUtilityFunctions() {
  console.log('🧪 开始测试工具函数...');
  
  // 测试类型检测
  console.assert(getType([]) === 'Array', '数组类型检测失败');
  console.assert(getType({}) === 'Object', '对象类型检测失败');
  console.assert(getType('') === 'String', '字符串类型检测失败');
  console.assert(getType(123) === 'Number', '数字类型检测失败');
  
  // 测试深度克隆
  const original = { a: 1, b: { c: 2 } };
  const cloned = deepClone(original);
  cloned.b.c = 3;
  console.assert(original.b.c === 2, '深度克隆失败');
  
  // 测试深度合并
  const obj1 = { a: 1, b: { x: 1 } };
  const obj2 = { b: { y: 2 }, c: 3 };
  const merged = deepMerge(obj1, obj2);
  console.assert(merged.a === 1, '深度合并失败 - a');
  console.assert(merged.b.x === 1, '深度合并失败 - b.x');
  console.assert(merged.b.y === 2, '深度合并失败 - b.y');
  console.assert(merged.c === 3, '深度合并失败 - c');
  
  console.log('✅ 工具函数测试通过');
}

/**
 * 格式化函数测试
 */
function testFormatterFunctions() {
  console.log('🧪 开始测试格式化函数...');
  
  // 测试时间格式化
  const date = new Date('2024-01-01 12:30:45');
  console.assert(parseTime(date, 'yyyy-MM-dd') === '2024-01-01', '日期格式化失败');
  console.assert(parseTime(date, 'yyyy-MM-dd HH:mm:ss') === '2024-01-01 12:30:45', '日期时间格式化失败');
  
  // 测试数字格式化
  console.assert(formatNumber(1234.567, 2) === '1,234.57', '数字格式化失败');
  console.assert(formatNumber(1234, 0) === '1,234', '整数格式化失败');
  
  console.log('✅ 格式化函数测试通过');
}

/**
 * 权限函数测试
 */
function testPermissionFunctions() {
  console.log('🧪 开始测试权限函数...');
  
  // 测试权限检查
  const userPermissions = ['user:read', 'user:write', 'admin:read'];
  
  console.assert(hasPermission('user:read', userPermissions) === true, '单个权限检查失败');
  console.assert(hasPermission('user:delete', userPermissions) === false, '不存在权限检查失败');
  console.assert(hasPermission(['user:read', 'user:write'], userPermissions) === true, '多个权限检查失败');
  console.assert(hasPermission(['user:read', 'user:delete'], userPermissions) === false, '部分权限检查失败');
  
  // 测试按钮规则解析
  const row = { status: 1, type: 'admin' };
  const storage = { user: { role: 'admin' } };
  
  console.assert(parseButtonRule('row.status === 1', row, storage) === true, '简单规则解析失败');
  console.assert(parseButtonRule('row.status === 0', row, storage) === false, '简单规则解析失败');
  console.assert(parseButtonRule('storage.user.role === "admin"', row, storage) === true, 'Storage规则解析失败');
  
  console.log('✅ 权限函数测试通过');
}

/**
 * 配置系统测试
 */
function testConfigSystem() {
  console.log('🧪 开始测试配置系统...');
  
  // 测试全局配置
  const originalConfig = getGlobalConfig();
  
  setGlobalConfig({
    test: {
      value: 'test123'
    }
  });
  
  console.assert(getGlobalConfig('test.value') === 'test123', '全局配置设置失败');
  console.assert(getGlobalConfig('test').value === 'test123', '全局配置获取失败');
  
  console.log('✅ 配置系统测试通过');
}

/**
 * 组合式函数测试
 */
function testComposableFunctions() {
  console.log('🧪 开始测试组合式函数...');
  
  // 模拟props和emit
  const mockProps = {
    data: [
      { id: 1, name: '张三', status: 1 },
      { id: 2, name: '李四', status: 0 }
    ],
    header: [
      { prop: 'name', label: '姓名' },
      { prop: 'status', label: '状态' }
    ]
  };
  
  const mockEmit = (event, data) => {
    console.log(`事件触发: ${event}`, data);
  };
  
  // 这里可以添加更多的组合式函数测试
  // 由于组合式函数依赖Vue环境，在实际项目中需要使用Vue Test Utils
  
  console.log('✅ 组合式函数测试通过');
}

/**
 * 响应式更新测试
 */
function testReactiveUpdates() {
  console.log('🧪 开始测试响应式更新...');
  
  // 模拟响应式数据
  const buttonStates = mockVue.reactive({});
  
  // 测试按钮状态更新
  const setButtonLoading = (key, loading) => {
    buttonStates[key] = loading;
  };
  
  const getButtonLoading = (key) => {
    return buttonStates[key] || false;
  };
  
  // 测试状态设置和获取
  setButtonLoading('edit_1', true);
  console.assert(getButtonLoading('edit_1') === true, '按钮状态设置失败');
  
  setButtonLoading('edit_1', false);
  console.assert(getButtonLoading('edit_1') === false, '按钮状态重置失败');
  
  console.log('✅ 响应式更新测试通过');
}

/**
 * 兼容性测试
 */
function testCompatibility() {
  console.log('🧪 开始测试兼容性...');
  
  // 测试co-table配置格式
  const coTableConfig = {
    header: [
      { prop: 'name', label: '姓名' },
      { prop: 'status', label: '状态' }
    ],
    config: {
      operation: {
        list: [
          { name: '编辑', mark: 'edit', type: 'primary' }
        ]
      }
    }
  };
  
  // 测试page-table配置格式
  const pageTableConfig = {
    tableHeader: [
      { prop: 'name', label: '姓名' },
      { prop: 'status', label: '状态' }
    ],
    tableConfig: {
      operation: {
        list: [
          { name: '编辑', mark: 'edit', type: 'primary' }
        ]
      }
    }
  };
  
  // 配置格式统一函数
  const unifyConfig = (config) => {
    if (config.tableHeader) {
      // page-table格式
      return {
        header: config.tableHeader,
        config: config.tableConfig || {}
      };
    } else {
      // co-table格式
      return {
        header: config.header,
        config: config.config || {}
      };
    }
  };
  
  const unified1 = unifyConfig(coTableConfig);
  const unified2 = unifyConfig(pageTableConfig);
  
  console.assert(unified1.header.length === 2, 'co-table配置统一失败');
  console.assert(unified2.header.length === 2, 'page-table配置统一失败');
  console.assert(unified1.config.operation.list.length === 1, 'co-table操作配置统一失败');
  console.assert(unified2.config.operation.list.length === 1, 'page-table操作配置统一失败');
  
  console.log('✅ 兼容性测试通过');
}

/**
 * 性能测试
 */
function testPerformance() {
  console.log('🧪 开始测试性能...');
  
  // 测试大数据量处理
  const largeData = Array.from({ length: 1000 }, (_, i) => ({
    id: i + 1,
    name: `用户${i + 1}`,
    email: `user${i + 1}@example.com`,
    status: Math.random() > 0.5 ? 1 : 0
  }));
  
  // 测试数据过滤性能
  const startTime = performance.now();
  const filteredData = largeData.filter(item => item.status === 1);
  const endTime = performance.now();
  
  console.log(`数据过滤耗时: ${endTime - startTime}ms`);
  console.assert(endTime - startTime < 10, '数据过滤性能不达标');
  
  // 测试深度克隆性能
  const cloneStartTime = performance.now();
  const clonedData = deepClone(largeData.slice(0, 100));
  const cloneEndTime = performance.now();
  
  console.log(`深度克隆耗时: ${cloneEndTime - cloneStartTime}ms`);
  console.assert(cloneEndTime - cloneStartTime < 50, '深度克隆性能不达标');
  
  console.log('✅ 性能测试通过');
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行 Co-Table 测试套件...\n');
  
  try {
    testUtilityFunctions();
    testFormatterFunctions();
    testPermissionFunctions();
    testConfigSystem();
    testComposableFunctions();
    testReactiveUpdates();
    testCompatibility();
    testPerformance();
    
    console.log('\n🎉 所有测试通过！Co-Table 组件功能正常。');
    return true;
  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    return false;
  }
}

// 导出测试函数
export {
  runAllTests,
  testUtilityFunctions,
  testFormatterFunctions,
  testPermissionFunctions,
  testConfigSystem,
  testComposableFunctions,
  testReactiveUpdates,
  testCompatibility,
  testPerformance
};

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined' && window.location) {
  // 浏览器环境
  window.coTableTests = { runAllTests };
  console.log('Co-Table 测试套件已加载，运行 coTableTests.runAllTests() 开始测试');
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = { runAllTests };
}

// 自动运行测试（可选）
// runAllTests();
