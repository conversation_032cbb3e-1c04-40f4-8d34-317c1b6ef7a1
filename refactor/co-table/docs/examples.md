# Co-Table 使用示例

## 基础示例

### 简单表格

```vue
<template>
  <co-table
    :header="header"
    :data="data"
    @handle="onHandle"
  />
</template>

<script setup>
import { ref } from 'vue';

const header = ref([
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'email', label: '邮箱', width: 200 },
  { prop: 'status', label: '状态', width: 100 }
]);

const data = ref([
  { id: 1, name: '张三', email: '<EMAIL>', status: 1 },
  { id: 2, name: '李四', email: '<EMAIL>', status: 0 }
]);

const onHandle = ({ field, btn, row, index }) => {
  console.log('操作:', field, row);
};
</script>
```

### 带搜索的表格

```vue
<template>
  <co-table
    :header="header"
    :data="data"
    :search="searchConfig"
    @search="onSearch"
    @handle="onHandle"
  />
</template>

<script setup>
import { ref, reactive } from 'vue';

const header = ref([
  { prop: 'name', label: '姓名' },
  { prop: 'status', label: '状态' },
  { prop: 'createTime', label: '创建时间' }
]);

const data = ref([]);

const searchConfig = reactive({
  items: [
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      attrs: {
        placeholder: '请输入姓名'
      }
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      option: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    {
      prop: 'dateRange',
      label: '创建时间',
      type: 'daterange',
      attrs: {
        placeholder: '请选择时间范围'
      }
    }
  ]
});

const onSearch = ({ searchData, type }) => {
  console.log('搜索:', searchData, type);
  // 调用API加载数据
  loadData(searchData);
};

const onHandle = ({ field, btn, row }) => {
  switch (field) {
    case 'edit':
      editUser(row);
      break;
    case 'delete':
      deleteUser(row);
      break;
  }
};

const loadData = async (params) => {
  // 模拟API调用
  const response = await api.getUsers(params);
  data.value = response.data;
};
</script>
```

## 高级示例

### 带分页的表格

```vue
<template>
  <co-table
    :api="loadData"
    :header="header"
    :search="searchConfig"
    :config="tableConfig"
    :pagination="paginationConfig"
    @handle="onHandle"
  />
</template>

<script setup>
import { ref, reactive } from 'vue';

const header = ref([
  { prop: 'name', label: '姓名' },
  { prop: 'email', label: '邮箱' },
  { prop: 'status', label: '状态' }
]);

const searchConfig = reactive({
  items: [
    { prop: 'name', label: '姓名', type: 'input' },
    { prop: 'status', label: '状态', type: 'select', option: 'statusOptions' }
  ]
});

const tableConfig = reactive({
  selection: { type: 'selection' },
  operation: {
    list: [
      { name: '编辑', mark: 'edit', type: 'primary' },
      { name: '删除', mark: 'delete', type: 'danger' }
    ]
  }
});

const paginationConfig = reactive({
  layout: 'total, sizes, prev, pager, next, jumper',
  pageSizes: [10, 20, 50, 100]
});

const loadData = async (params) => {
  const response = await api.getUsers(params);
  return {
    list: response.data,
    total: response.total,
    current: response.current,
    size: response.size
  };
};

const onHandle = ({ field, btn, row }) => {
  // 处理操作
};
</script>
```

### 表格内表单

```vue
<template>
  <co-table
    :header="header"
    :data="data"
    :config="tableConfig"
    @form-change="onFormChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue';

const header = ref([
  { prop: 'name', label: '姓名', type: 'input' },
  { 
    prop: 'status', 
    label: '状态', 
    type: 'select',
    option: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  { 
    prop: 'active', 
    label: '激活状态', 
    type: 'switch',
    attrs: {
      'active-text': '是',
      'inactive-text': '否'
    }
  }
]);

const data = ref([
  { id: 1, name: '张三', status: 1, active: true },
  { id: 2, name: '李四', status: 0, active: false }
]);

const tableConfig = reactive({
  operation: {
    list: [
      { name: '保存', mark: 'save', type: 'primary' }
    ]
  }
});

const onFormChange = ({ prop, row, value }) => {
  console.log('表单变化:', prop, value, row);
  // 实时保存或验证
};
</script>
```

### 权限控制示例

```vue
<template>
  <co-table
    :header="header"
    :data="data"
    :config="tableConfig"
    :user-permissions="userPermissions"
    @handle="onHandle"
  />
</template>

<script setup>
import { ref, reactive } from 'vue';

const userPermissions = ref(['user:edit', 'user:view']);

const header = ref([
  { prop: 'name', label: '姓名' },
  { prop: 'email', label: '邮箱' },
  { prop: 'status', label: '状态' }
]);

const data = ref([
  { id: 1, name: '张三', email: '<EMAIL>', status: 1 },
  { id: 2, name: '李四', email: '<EMAIL>', status: 0 }
]);

const tableConfig = reactive({
  operation: {
    list: [
      {
        name: '查看',
        mark: 'view',
        type: 'info',
        permission: 'user:view'
      },
      {
        name: '编辑',
        mark: 'edit',
        type: 'primary',
        permission: 'user:edit',
        rule: 'row.status === 1' // 只有启用状态才能编辑
      },
      {
        name: '删除',
        mark: 'delete',
        type: 'danger',
        permission: 'user:delete' // 用户没有此权限，按钮不显示
      }
    ]
  }
});

const onHandle = ({ field, btn, row }) => {
  // 处理操作
};
</script>
```

### 自定义插槽示例

```vue
<template>
  <co-table
    :header="header"
    :data="data"
    :search="searchConfig"
    @handle="onHandle"
  >
    <!-- 搜索插槽 -->
    <template #search_name="{ item, data }">
      <el-input
        v-model="data[item.prop]"
        placeholder="自定义姓名搜索"
        clearable
      />
    </template>

    <!-- 列内容插槽 -->
    <template #status="{ row }">
      <el-tag :type="row.status === 1 ? 'success' : 'danger'">
        {{ row.status === 1 ? '启用' : '禁用' }}
      </el-tag>
    </template>

    <!-- 操作列插槽 -->
    <template #operation="{ row, list }">
      <el-button
        v-for="btn in list"
        :key="btn.mark"
        :type="btn.type"
        size="small"
        @click="onHandle({ field: btn.mark, btn, row })"
      >
        {{ btn.name }}
      </el-button>
    </template>

    <!-- 空数据插槽 -->
    <template #empty>
      <div style="padding: 40px;">
        <el-empty description="暂无数据" />
      </div>
    </template>
  </co-table>
</template>
```

### 字典数据示例

```vue
<template>
  <co-table
    :header="header"
    :data="data"
    :dic="dicData"
    @handle="onHandle"
  />
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

const header = ref([
  { prop: 'name', label: '姓名' },
  { prop: 'status', label: '状态' }, // 会自动使用字典显示
  { prop: 'type', label: '类型' }
]);

const data = ref([
  { id: 1, name: '张三', status: 1, type: 'admin' },
  { id: 2, name: '李四', status: 0, type: 'user' }
]);

const dicData = reactive({
  status: [
    { label: '启用', value: 1, color: 'success' },
    { label: '禁用', value: 0, color: 'danger' }
  ],
  type: [
    { label: '管理员', value: 'admin', color: '#409EFF' },
    { label: '普通用户', value: 'user', color: '#909399' }
  ]
});

// 或者使用异步字典
const loadDictionary = async () => {
  const statusDict = await api.getDictionary('status');
  const typeDict = await api.getDictionary('type');
  
  dicData.status = statusDict;
  dicData.type = typeDict;
};

onMounted(() => {
  loadDictionary();
});
</script>
```

### Page-Table 兼容示例

```vue
<template>
  <page-table
    :form-model="formModel"
    :table-header="tableHeader"
    :table-config="tableConfig"
    :search-config="searchConfig"
    @operation="onOperation"
    @search="onSearch"
  />
</template>

<script setup>
import { reactive } from 'vue';

const formModel = reactive({
  name: '',
  status: ''
});

const tableHeader = [
  { prop: 'name', label: '姓名' },
  { prop: 'status', label: '状态' }
];

const tableConfig = reactive({
  operation: {
    list: [
      { name: '编辑', mark: 'edit', type: 'primary' }
    ]
  }
});

const searchConfig = reactive({
  items: [
    { prop: 'name', label: '姓名', type: 'input' },
    { prop: 'status', label: '状态', type: 'select' }
  ]
});

const onOperation = (data, refreshFn) => {
  console.log('操作:', data);
  // 处理完成后调用刷新
  refreshFn && refreshFn();
};

const onSearch = (searchData) => {
  console.log('搜索:', searchData);
};
</script>
```

## 最佳实践

### 1. 响应式数据管理

```javascript
// 推荐：使用 reactive 管理复杂对象
const tableConfig = reactive({
  operation: {
    list: []
  }
});

// 推荐：使用 ref 管理简单数组
const data = ref([]);
const header = ref([]);
```

### 2. 按钮状态管理

```javascript
const onHandle = async ({ field, btn, row }) => {
  // 设置按钮加载状态
  btn.loading = true;
  
  try {
    await api.handleOperation(field, row);
    // 操作成功后刷新数据
    await refreshData();
  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    // 清除加载状态
    btn.loading = false;
  }
};
```

### 3. 权限配置

```javascript
// 全局权限配置
setGlobalConfig({
  permission: {
    enabled: true,
    metaPermisKey: 'perms'
  }
});

// 注册权限处理器
permissionManager.registerHandler('getPermissions', async () => {
  return store.getters.userPermissions;
});
```

### 4. 性能优化

```javascript
// 使用 computed 优化复杂计算
const filteredData = computed(() => {
  return data.value.filter(item => item.status === 1);
});

// 使用 shallowRef 优化大数据量
import { shallowRef } from 'vue';
const largeData = shallowRef([]);
```
