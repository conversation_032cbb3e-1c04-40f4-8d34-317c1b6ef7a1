# Co-Table API 文档

## 组件概述

Co-Table 是一个基于 Vue 3 Composition API 的高级表格组件，统一了原有的 co-table 和 page-table 功能，提供了完整的表格解决方案。

## 基础用法

### 安装和注册

```javascript
import { createApp } from 'vue';
import CoTable from './refactor/co-table/export.js';

const app = createApp({});

// 全局注册
app.use(CoTable, {
  config: {
    // 全局配置
  },
  permissionHandlers: {
    // 权限处理器
  }
});

// 或者局部导入
import { CoTableMain, CoTable, PageTable } from './refactor/co-table/export.js';
```

### 基本表格

```vue
<template>
  <co-table
    :header="tableHeader"
    :data="tableData"
    :config="tableConfig"
  />
</template>

<script setup>
const tableHeader = [
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  { prop: 'email', label: '邮箱' }
];

const tableData = [
  { id: 1, name: '张三', age: 25, email: 'zhang<PERSON>@example.com' },
  { id: 2, name: '李四', age: 30, email: '<EMAIL>' }
];

const tableConfig = {
  operation: {
    list: [
      { name: '编辑', mark: 'edit', type: 'primary' },
      { name: '删除', mark: 'delete', type: 'danger' }
    ]
  }
};
</script>
```

## Props 属性

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `id` | String | - | 表格唯一标识 |
| `tableKey` | String | - | 表格键名，用于权限控制 |
| `data` | Array | `[]` | 表格数据 |
| `api` | Function | - | 数据加载API函数 |
| `header` | Array | `[]` | 表格列配置 |
| `config` | Object | `{}` | 表格配置对象 |

### 搜索相关

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `search` | Object | `null` | 搜索配置 |

### 分页相关

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `pagination` | Object/Boolean | `true` | 分页配置，false时不显示分页 |
| `paginationAttrs` | Object | `{}` | 分页组件属性 |

### 权限相关

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `userPermissions` | Array | `[]` | 用户权限列表 |

### 兼容性属性（Page-Table）

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `formModel` | Object | `{}` | 搜索表单数据（page-table兼容） |
| `tableHeader` | Array | `[]` | 表格列配置（page-table兼容） |
| `tableConfig` | Object | `{}` | 表格配置（page-table兼容） |
| `searchConfig` | Object | `{}` | 搜索配置（page-table兼容） |

## Events 事件

### 基础事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `search` | `(searchData, type)` | 搜索事件 |
| `handle` | `(eventData)` | 操作按钮点击事件 |
| `selection-change` | `(selection)` | 选择变化事件 |
| `row-click` | `(eventData)` | 行点击事件 |

### 数据事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `data-loaded` | `(data, response, pagination)` | 数据加载完成 |
| `data-error` | `(error)` | 数据加载错误 |
| `refresh` | - | 表格刷新事件 |

### 分页事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `pagination-change` | `(paginationData)` | 分页变化事件 |

## Slots 插槽

### 搜索插槽

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| `search_{prop}` | `{ item, data }` | 搜索字段自定义插槽 |
| `search_operation` | `{ handle }` | 搜索操作按钮插槽 |

### 表格插槽

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| `{prop}` | `{ row, column, $index, item, prop }` | 列内容插槽 |
| `{prop}_header` | `{ column, $index }` | 列头插槽 |
| `operation` | `{ list, row, index }` | 操作列插槽 |
| `expand` | `{ row, column, $index }` | 展开行插槽 |
| `empty` | - | 空数据插槽 |

## 方法

### 表格方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `refresh` | `(newData?)` | - | 刷新表格 |
| `clearSelection` | - | - | 清空选中状态 |
| `loadTableData` | `(params?)` | Promise | 加载表格数据 |

### 分页方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `goToPage` | `(page)` | - | 跳转到指定页 |
| `resetPagination` | - | - | 重置分页 |

### 数据方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getTableData` | - | Array | 获取表格数据 |
| `setTableData` | `(data, pagination?)` | - | 设置表格数据 |
| `addTableRow` | `(row, index?)` | - | 添加表格行 |
| `removeTableRow` | `(target)` | - | 删除表格行 |
| `updateTableRow` | `(target, newData)` | - | 更新表格行 |

## 配置对象

### 表格配置 (config)

```javascript
const config = {
  // 选择配置
  selection: {
    type: 'selection',
    width: 55
  },
  
  // 操作配置
  operation: {
    list: [
      {
        name: '编辑',
        mark: 'edit',
        type: 'primary',
        rule: 'row.status === 1', // 显示规则
        permission: 'user:edit'    // 权限标识
      }
    ],
    merge: 'push', // 权限合并方式
    hiddenList: (row, tableKey) => false // 隐藏规则
  }
};
```

### 搜索配置 (search)

```javascript
const search = {
  items: [
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      attrs: {
        placeholder: '请输入姓名'
      }
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      option: 'statusOptions' // 字典键名
    }
  ]
};
```

### 列配置 (header)

```javascript
const header = [
  {
    prop: 'name',
    label: '姓名',
    width: 120,
    align: 'center'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    option: 'statusOptions',
    attrs: {
      disabled: true
    }
  }
];
```

## 组合式函数

### useTable

```javascript
import { useTable } from './refactor/co-table/export.js';

const {
  tableData,
  selectedData,
  loading,
  refresh,
  clearSelection
} = useTable(props, emit);
```

### useTableData

```javascript
import { useTableData } from './refactor/co-table/export.js';

const {
  searchFormData,
  dicEnumData,
  loadTableData,
  setTableData
} = useTableData(props, emit, tableState, paginationState);
```

### usePermission

```javascript
import { usePermission } from './refactor/co-table/export.js';

const {
  checkPermission,
  renderOperation,
  setRowPermissions
} = usePermission(props, route);
```

## 全局配置

```javascript
import { setGlobalConfig } from './refactor/co-table/export.js';

setGlobalConfig({
  // 权限配置
  permission: {
    enabled: true,
    metaPermisKey: 'perms'
  },
  
  // 字典配置
  dictionary: {
    getDic: async (key) => {
      // 获取字典数据的方法
      return await api.getDictionary(key);
    }
  },
  
  // 上传配置
  upload: {
    method: async (uploadData) => {
      // 上传方法
      return await api.upload(uploadData);
    }
  }
});
```

## 权限系统

### 权限处理器注册

```javascript
import { permissionManager } from './refactor/co-table/export.js';

// 注册权限获取方法
permissionManager.registerHandler('getPermissions', async () => {
  return await api.getUserPermissions();
});

// 注册权限检查方法
permissionManager.registerHandler('checkPermission', (permission, userPermissions) => {
  return userPermissions.includes(permission);
});
```

### 按钮权限控制

```javascript
const operationList = [
  {
    name: '编辑',
    mark: 'edit',
    permission: 'user:edit',           // 权限标识
    rule: 'row.status === 1',          // 显示规则
    tableKey: 'userTable'              // 表格标识
  }
];
```

## 类型定义

由于使用纯 JavaScript，建议在 JSDoc 中定义类型：

```javascript
/**
 * @typedef {Object} TableColumn
 * @property {string} prop - 字段名
 * @property {string} label - 列标题
 * @property {string} [type] - 列类型
 * @property {number} [width] - 列宽度
 * @property {string} [align] - 对齐方式
 */

/**
 * @typedef {Object} TableConfig
 * @property {Object} [selection] - 选择配置
 * @property {Object} [operation] - 操作配置
 */
```
