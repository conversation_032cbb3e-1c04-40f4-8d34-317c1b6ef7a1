{"name": "xyd-web-monorepo", "version": "1.0.0", "description": "XYD Web项目Monorepo架构", "private": true, "scripts": {"platform:dev": "cd apps/web-platform && pnpm run dev", "platform:build": "cd apps/web-platform && pnpm run build:docker", "platform:start": "cd apps/web-platform && pnpm run start", "ucenter:dev": "cd apps/web-ucenter && pnpm run dev", "ucenter:build": "cd apps/web-ucenter && pnpm run build:docker", "ucenter:start": "cd apps/web-ucenter && pnpm run start", "clean": "rimraf node_modules pnpm-lock.yaml && pnpm -r run clean"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "repository": {"type": "git", "url": "http://************:3000/xyd/xyd-web-monorepo.git"}, "pnpm": {"overrides": {"@zszc/co-form-v3": "3.1.8"}}, "keywords": ["monorepo", "xyd", "web", "platform", "ucenter"], "author": "XYD Team", "license": "MIT", "dependencies": {"@zszc/co-preview-v3": "^1.0.3"}}