export * from "./finance.js";
export * from "./other.js";
export * from "./cluster";
export * from "./content-manage.js";
export * from "./validation.js";

const modules = import.meta.glob("./**/*.(ts|js)", { eager: true });
const dictApi = {};

for (const item of Object.keys(modules)) {
  const module = modules[item];
  if (module.default) {
    Object.assign(dictApi, module.default);
  }
  Object.assign(dictApi, module);
}

export default dictApi;
