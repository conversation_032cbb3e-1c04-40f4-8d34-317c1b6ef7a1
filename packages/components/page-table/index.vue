<template>
  <co-search :model="formModel" :config="searchConfig" :dic="dicData" @search="onSearchHandle">
    <template v-for="(_, slotName) in slots" :key="slotName" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps || {}" />
    </template>
  </co-search>
  <co-table
    :id="props.id"
    :config="tableConfig"
    :header="tableHeader"
    @loaded="onTableLoaded"
    @dicLoaded="(data: DicItem) => (dicData = data)"
    @operation="(data: CoTableOpertaion)=>emits('operation', data, onSearchHandle)"
    @selection-change="(selection) => emits('selection-change', selection)"
  >
    <template v-for="(_, slotName) in slots" :key="slotName" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps || {}" />
    </template>
  </co-table>
</template>
<script lang="ts" name="InnerTable" setup>
const emits = defineEmits(["operation", "selection-change", "search"]);
const slots = useSlots();
// 显式声明 props 及其默认值
interface FinancialListProps {
  id?: string;
  formModel?: Record<string, any>;
  tableHeader: CoTableColumn[];
  tableConfig: CoTableConfig;
  searchConfig: CoSearchConfig;
}

const props = withDefaults(defineProps<FinancialListProps>(), {
  id: "",
  tableHeader: () => [] as CoTableColumn[],
  tableConfig: () => ({} as CoTableConfig),
  searchConfig: () => ({} as CoSearchConfig),
});
let onSearch: CoTableSearch = null;
const dicData = ref<DicItem>();
let oldParams = ref({});
// 表格挂在完成
const onTableLoaded = ({ getDataList }: CoTableLoadedParams) => (onSearch = getDataList);
const onSearchHandle = (params?: SearchParams, type: string): void => {
  if (params) oldParams.value = params;
  onSearch && onSearch({ params: oldParams.value });
  emits("search", { params: oldParams.value, type });
};
defineExpose({ onSearchHandle, oldParams });
</script>
<style lang="scss"></style>
