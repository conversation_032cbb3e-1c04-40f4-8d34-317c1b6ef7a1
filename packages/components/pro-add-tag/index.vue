<script setup>
defineOptions({
  name: 'AddTag',
  inheritAttrs: false,
});

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请输入内容，按回车添加',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  maxTags: {
    type: Number,
    default: 0, // 0表示不限制
  },
  maxLength: {
    type: Number,
    default: 10, // 单个最大字数限制
  },
  tagType: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: 'default',
  },
});

const emit = defineEmits(['update:modelValue', 'change', 'tag-add', 'tag-remove']);
const slots = useSlots();

// 响应式数据
const inputValue = ref('');
const inputRef = ref();
const tags = ref([]);

// 数据转换工具函数
const stringToArray = (str) => {
  if (!str || typeof str !== 'string') return [];
  return str.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
};

const arrayToString = (arr) => {
  return Array.isArray(arr) ? arr.join(',') : '';
};

// 字数计算工具函数
const getStringLength = (str) => {
  if (!str || typeof str !== 'string') return 0;
  return str.trim().length;
};

// 计算属性
const currentInputLength = computed(() => getStringLength(inputValue.value));
const isInputTooLong = computed(() => props.maxLength > 0 && currentInputLength.value > props.maxLength);

// 初始化数组
const initTags = () => {
  tags.value = stringToArray(props.modelValue);
};

// 操作方法
const addTag = (value) => {
  if (!value || typeof value !== 'string') return false;

  const trimmedValue = value.trim();
  if (!trimmedValue) return false;

  // 检查字数限制
  if (props.maxLength > 0 && getStringLength(trimmedValue) > props.maxLength) {
    ElMessage.error(`长度不能超过${props.maxLength}个字符`);
    return false
  }

  // 检查是否已存在
  if (tags.value.includes(trimmedValue)){
    ElMessage.error(`数据已存在,请勿重复添加`)
    return false
  }

  // 检查最大数限制
  if (props.maxTags > 0 && tags.value.length >= props.maxTags) {
    ElMessage.error(`数量不能超过${props.maxTags}个`)
    return false
  }

  tags.value.push(trimmedValue);
  emit('tag-add', trimmedValue);
  return true;
};

const removeTag = (index) => {
  if (index >= 0 && index < tags.value.length) {
    const removedTag = tags.value.splice(index, 1)[0];
    emit('tag-remove', removedTag);
  }
};

const removeTagByValue = (value) => {
  const index = tags.value.indexOf(value);
  if (index !== -1) {
    removeTag(index);
  }
};

// 输入框事件处理
const handleInputConfirm = () => {
  if (inputValue.value && addTag(inputValue.value)) {
    inputValue.value = '';
  }
};



const handleKeydown = (event) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    handleInputConfirm();
  }
};

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  const newTags = stringToArray(newValue);
  if (JSON.stringify(newTags) !== JSON.stringify(tags.value)) {
    tags.value = newTags;
  }
}, { immediate: true });

// 监听tags变化并更新modelValue
watch(tags, (newTags) => {
  const newValue = arrayToString(newTags);
  if (newValue !== props.modelValue) {
    emit('update:modelValue', newValue);
    emit('change', newValue);
  }
}, { deep: true });

// 暴露方法给父组件
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  clear: () => {
    tags.value = [];
    inputValue.value = '';
  },
  addTag,
  removeTag,
  removeTagByValue,
  getTags: () => [...tags.value],
  getTagsString: () => arrayToString(tags.value),
});
</script>

<template>
  <div class="add-tag-container">
    <!-- 输入框区域 -->
    <div class="input-wrapper">
      <el-input
        ref="inputRef"
        v-model="inputValue"
        :placeholder="placeholder"
        :disabled="disabled || (maxTags > 0 && tags.length >= maxTags)"
        :size="size"
        v-bind="$attrs"
        @keydown="handleKeydown"
        class="tag-input"
      >
        <template #append>
          <el-button
            :disabled="!inputValue || disabled || (maxTags > 0 && tags.length >= maxTags) || isInputTooLong"
            @click="handleInputConfirm"
            :size="size"
            type="primary"
          >
            添加
          </el-button>
        </template>

        <!-- 动态透传所有插槽（排除append插槽，因为我们已经使用了） -->
        <template v-for="(_, slotName) in slots" :key="slotName" #[slotName]="slotProps">
          <slot v-if="slotName !== 'append'" :name="slotName" v-bind="slotProps || {}" />
        </template>
      </el-input>
    </div>

    <!-- 显示区域 -->
    <div class="tags-wrapper" v-if="tags.length > 0">
      <el-tag
        v-for="(tag, index) in tags"
        :key="`tag-${index}-${tag}`"
        :type="tagType"
        :size="size"
        closable
        :disable-transitions="true"
        @close="removeTag(index)"
        class="tag-item"
      >
        {{ tag }}
      </el-tag>
    </div>

    <!-- 提示信息 -->
    <div class="hint-text" v-if="maxTags > 0 || maxLength > 0">
      <span v-if="maxTags > 0">最大数量限制: {{ tags.length }}/{{ maxTags }}</span>
      <span v-if="maxTags > 0 && maxLength > 0" class="separator">|</span>
      <span v-if="maxLength > 0" :class="{ 'text-error': isInputTooLong }">
        字数: {{ currentInputLength }}/{{ maxLength }}
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.add-tag-container {
  width: 100%;

  .input-wrapper {
    width: 100%;
    margin-bottom: 8px;

    .tag-input {
      width: 100%;
    }
  }

  .tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 8px;

    .tag-item {
      margin: 0;
    }
  }

  .hint-text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;

    .separator {
      color: var(--el-border-color);
    }

    .text-error {
      color: var(--el-color-danger);
      font-weight: 500;
    }
  }
}
</style>
