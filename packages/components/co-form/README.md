# 使用示例v3.1.4 - 更新日期2025.06.19

## 1.安装

```
import coForm from './form.vue';
app.use(coForm, {
    fileSrc: fileSrcFn
});
```

## 2.引入

```
<co-form ref="textRef" :form-list="formList" :form-data="formData" @event="onFormEvent"></co-form>

// 状态值切换触发
function onFormEvent(item, value, newFormData){
    item：当前表单项
    value：改变后的值
    newFormData：所有数据
}

```

## 3.使用

```
// vue3.0定义
const textRef = ref();

// 获取表单数据 - 进行表单验证
// opt.isFile       boolean：文件只返回file对象，默认返回所有，注：当修改时，返回文件数据为传入的文件地址或文件ID或其他信息
// opt.isFileOrigin boolean：如果不是file对象，则返回传入值，注：当修改时，传入的数据可能会经过处理，一般不需要考虑此字段
// opt.isFileUid    boolean：文件值为file对象时绑定uid，否则绑定到id上，默认都在uid上，注：当uid不存在时，绑定到ID上
// opt.setIds       Array：额外返回的数据的ID，插槽绑定时可能会用到，一般不需要考虑，注：返回的数据是根据formList规则生成
// opt.default      Object: 合并返回数据，注：在父组件合并数据也可以（举例：修改时的ID）
const data = textRef.getFormData(opt:Object);

// 获取表单数据 - 不进行表单验证     id:获取的ID数据，不传返回所有数据，保存时考虑使用
const data = textRef.getFormDataKey(id:String, opt:Object, isPromise:Boolean);

// 获取newFormData数据 - 不建议直接修改此对象，请使用setFormListData
const data = textRef.getNewFormData();

// 修改newFormData数据  id:要修改的id   val:要修改的值   is:是否清空验证，默认清空
textRef.setFormDataKey(id:String, val:String|Number, is:Boolean);

// 修改list列表         id:要修改的id   val:要修改的数组  is:是否清除数据，默认清除
textRef.setFormListData(id:String, list:Array, is:Boolean);

// 新验证某id的rule     id:要验证的id   
textRef.setValidateField(id:String);
```

## 4.props说明

| 属性            | 格式      | 说明                                                                  |
|---------------|---------|---------------------------------------------------------------------|
| formList      | Array   | 表单列表                                                                |
| formData      | Object  | 表单数据                                                                |
| shareData     | Object  | 共享数据，平常不建议使用，当存在两个co-form存在判断数据使用，由父组件传入，只传{}即可；注：此时所有的数据将会被绑定到此字段上 |
| setData       | Object  | 合并数据，效果等同于opt.default                                               |
| dictConfig    | Object  | 动态字典数据，需要配合dicKey使用。举例：{ dicKey:ref([]) }  -- (对dicKey进行value赋值即可)  |
| labelWidth    | String  | 标签宽度，等同与el-form                                                     |
| labelPosition | String  | 表单域标签的位置，等同与el-form                                                 |
| isLabel       | Boolean | 是否显示标签，默认true                                                       |
| inline        | Boolean | 是否行内表单模式，默认false                                                    |
| size          | String  | 用于控制该表单域下组件的默认尺寸，等同与el-form                                         |

## 5.formList 表单列表说明

| 属性            | 格式                     | 说明                                                                     |
|---------------|------------------------|------------------------------------------------------------------------|
| id            | String                 | 数据ID，支持a.b格式，如果为a.b格式，则getFormData方法返回数据格式和赋值格式为{a:{b:xxx}}            |
| uid           | String                 | 将返回的数据ID改为此字段，注：修改时formData赋值字段仍为id                                    |
| name          | String                 | 标签label文本                                                              |
| type          | String                 | 表单类型                                                                   |
| dw            | String                 | 数据单位，当存在单位时，建议使用此字段                                                    |
| css           | String                 | 在el-form-item上添加的类名                                                    |
| slot          | String                 | 插槽                                                                     |
| rowSlot       | String                 | 右侧插槽                                                                   |
| isSetNo       | Boolean                | 此ID数据是否不做返回                                                            |
| set           | Boolean                | 当type为text、text-link、text-image时有效，此时默认不返回此ID数据，set可改变此判断              |
| textId        | String                 | 当type为text时有效，表单将此字段为从formData获取展示，表单提交时，仍以id为数据字段                     |
| tips          | String                 | 当type为非text时有效，简短的输入提示                                                 |
| popperClass   | String                 | 当type为时间组件有效，同el时间组件下的popperClass                                      |
| disabledDate  | Function               | 当type为时间组件有效，同el时间组件下的disabledDate                                     |
| placeholder   | String                 | 相应的表单存在，修改默认占位内容                                                       |
| attributes    | Object                 | Element的属性，当需要定义Element更多表单属性时可以使用此参数                                  |
| list          | Array                  | 下拉选择、单选框、复选框的数据源，如果为单选框或复选框，请修改type                                    |
| dicKey        | String                 | 下拉选择、单选框、复选框的数据源，不同于list，此字段数据从dictConfig中获取。                          |
| handle        | Function               | 当list和dicKey存在时有效，用于处理list或dicKey中的text，一般不需要                          |
| setNameId     | String                 | 返回值字段，当list和dicKey存在时有效，将list或dicKey中的text值以此字段返回                      |
| splitMark     | String                 | 返回值字段，当list和dicKey且setNameId存在时有效，将setNameId的数据以此字段连接成一个字符串            |
| split         | String                 | 返回值字段，如果返回的数据为数组格式，则根据此字段将数组连接成一个字符串                                   |
| split         | String                 | 另外赋值时，将赋值的数据以此字段分割。注：多值下拉选择、复选框可能用到；提示：多值下拉选择、复选框框在model绑定时为数组格式。      |
| splitNumber   | Boolean                | 当split存在有效，赋值时，将split分割的数组处理成数值型                                       |
| isNumber      | Boolean                | 赋值时，将赋值数据改为number类型。提示：如果list或dicKey中value与赋值数据类型不同，会造成回显不正确。          |
| isString      | Boolean                | 赋值时，将赋值数据改为String类型                                                    |
| relation      | Boolean/Object/Array   | 是否显示，格式参考以下                                                            |
| required      | Boolean/Object/Array   | 是否必填，格式参考以下                                                            |
| some          | Boolean                | relation、required为数组时有效，将every判断改为some判断；提示：every所有通过则通过，some是一个通过则通过。 |
| upload        | Object                 | 当type为附件时有效，附件参数，格式参考以下                                                |
| trueValue     | String/Number          | 当type为checkboxValue时有效，选中的值                                            |
| falseValue    | String/Number          | 当type为checkboxValue时有效，未选中的值                                           |
| activeValue   | String/Number          | 当type为switch时有效，选中的值                                                   |
| inactiveValue | String/Number          | 当type为switch时有效，未选中的值                                                  |
| value         | String/Number/Object   | 赋值时，如果值不存在，则默认赋值，格式参考以下                                                |
| isBefore      | String                 | 如果值不为空，则修改显示类型为此字段 --- 在默认赋值之前                                         |
| isAfter       | String                 | 如果值不为空，则修改显示类型为此字段 --- 在默认赋值之后                                         |
| default       | String                 | 当type为text时有效，默认显示值。                                                   |
| pattern       | String                 | 额外验证规则，正则表达式，此字段存在时，validate无效                                         |
| validate      | String/Object/Function | 额外验证规则，详情参考以下                                                          |
| children      | Array[Object]          | 多个输入项，子项的参数与此列表相同                                                      |

### 5.1 relation、required 字段说明

```
1. Boolean：直接判断
2. Object格式
   Object格式1：{id:String, val:function / 值 / Array[值] }
   Object格式2：{oid: String, val:function / 值 / Array[值],  isWatch:true/false}
   Object格式3：{id: String, noVal:值 / Array[值]}
   Object格式4：{oid: String, noVal:值 / Array[值], isWatch:true/false}
3. Array格式：[Object格式]
4. 字段说明：
   id：表单内的值
   oid：formData的值
   val：为函数时，以函数返回结果判定；为此值时，以值对比判定
   noVal：不为此值时判定成立
   isWatch：是否开启对shareData, formData值的watch监听，注意：请勿乱用
```

### 5.2 upload字段说明

```
tip1：是否显示上传提醒文字，默认显示
isBtn：附件选择是否是button按钮，默认button按钮
isName：文字选择按钮是否显示文件名，默认显示
t1：文件未选择时的提示文字
t2：文件选择后的提醒文字
upType：文件上传格式，String|Array[String]格式
sizeMax：文件上传最大直，默认10MB，单位MB
tips：额外的上传提醒文字
isOriginValue: boolean格式，如果不是file对象，则返回传入值，注：当修改时，传入的数据可能会经过处理，一般不需要考虑此字段
```

### 5.3 value字段说明

```
String：直接返回
Number：直接返回
Object1：{text:String, value:String|Number} 返回value值，如果为字典对象则可能是此格式
Object2：{oid:String} 获取formData值，然后在进行判断返回
```

### 5.4 validate字段说明 - 表单验证规则

```
1. Object：使用此数据，作为验证规则，数据内容参考el-form的验证规则
2. Function：验证时，回调此函数。参考el-form的validator验证函数
3. String：内置验证规则，可能值
    number 正整数
    money 金额
    moneyTenThousand 金额 - 单位万元
    phone 手机、电话
    phone2 手机
    email 电子邮箱
    isIdentityId 身份证
    checkSocialCreditCode 统一社会信用代码
    chinese 中文汉字
    noChinese 不包含中文字符
4. 字段说明：
   注意：number / money / moneyTenThousand 表单项可添加额外验证字段
   min 最低值
   max 最大值
   mins 最低值(包含)  
```

## 6. formData说明

```
1. 可以将后台返回的数据当成formData传入
2. 本表单数据分两部分，第一部分是传入的formData和formList，然后组件会根据formList生成一个新的newFormList和newFormData
3. 表单验证是根据newFormData处理的，如果使用了插槽并且进行了验证，需要将数据绑定到newFormData上
4. 具体说明：v-slot:default={data, row, value}中的data是newFormData，row是当前项(处理后)，value是当前值
5. 或者使用textRef.setFormDataKey对newFormData赋值
6. 赋值数据可以为字典结构，即：{text:String, value:String|Number}格式
7. 对于附件，如果需要回显文件名，赋值数据需要改为Object格式，即{src:文件地址，usrc:文件名}，除非文件地址包含文件名
8. 对于地址，组件会判断是否为file或者链接，如果都不是，则会使用fileSrc从服务器获取，详情查看表单引入配置
```

## 7. 安装配置说明

### 7.1 fileSrc - 文件读取说明
```

```

## 8. 组件修改须知

```
组件更改后，需要在README.md最顶部更新当前版本，同时修改本组件的使用说明，并且在version建立一个md文档，写清更新内容
```
