
/**
 * 定义配置文件
 */
const defaultConfig = {
    // 附件处理函数
    fileSrc: void 0,
    // 选择附件后执行上传函数，存在此函数时，默认立刻上传附件
    fileUpload: void 0,
    // 第三方组件
    components: {},
    // 第三方组件默认不生成form验证规则的type类型
    verifyNoTypeList: [],
    // 第三方组件默认不返回值的type类型，
    getNoTypeList: [],
    // 默认值处理完毕后，调用此函数，做最终处理
    valueHandle: void 0,
    // 表单项处理完毕后，调用此函数，做最终处理
    formItemHandle: void 0,
    // 第三方校验规则
    verifyValidate: {},
    // 选项配置
    options: {
        // 表单域标签的宽度
        labelWidth: '145px',
        // 表单域标签的位置
        labelPosition: 'right',
        // 是否显示表单域标签
        isLabel: true,
        // 是否为行内表单模式
        inline: false,
        // 用于控制该表单内组件的尺寸
        size: void (0)
    }
};

export default defaultConfig;
