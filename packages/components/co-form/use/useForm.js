import {ref, unref, nextTick, watch, computed} from 'vue';
import * as deal from '../utils/deal.js';
import {isJSON, isNull} from '../utils/pub.js';
import {getVal, showVerify} from '../utils/other.js';
import {verifyFormRules} from '../utils/verify.js';
import {useWatchList} from './useWatchList.js';
import defaultConfig from '../config.js';

// 监听表单变化
export function useForm(options, props, emit) {

  const newFormList = ref([]); //表单列表
  const newFormData = ref({}); //表单数据
  const placeholder = ref({}); //表单提示信息
  const newFormRules = ref({}); //表单验证
  let requiredIds = {}; //是否必填
  let relationIds = {}; //是否显示
  let isInit = false; // 表单是否正在初始化

  const {createWatchList} = useWatchList({newFormRules, getNewFormRules, clearValidate}, props);


  let storeFormList; //重新创建表单
  let isInitExecute = false;

  // 监听watch的变化，防止formInit连续执行
  function watchInit(callback) {
    if (isInitExecute) {
      return;
    }
    isInitExecute = true;
    nextTick().then(() => {
      isInitExecute = false;
      callback();
    });
  }

  // 表单监听
  watch(() => props.formList, () => {
    isInit = true;
    watchInit(formInit);
  });

  // 数据监听
  watch(() => props.formData, () => {
    isInit = true;
    watchInit(formInit);
  });

  // 表单域标签的宽度
  const labelWidth = computed(() => {
    return props.labelWidth === '0' ? '' : props.labelWidth;
  });


  // 生成表单组件
  function formInit() {
    // 重新创建表单
    if (storeFormList !== props.formList) {
      storeFormList = props.formList;
      newFormList.value = deal.getNewFormList(props.formList); //重新创建表单列表
      const rsInfo = deal.getFormListInfo(newFormList.value, props.shareData); //获取一些信息
      newFormData.value = rsInfo.newFormData; //表单数据
      placeholder.value = rsInfo.placeholder; //表单提示信息
      requiredIds = rsInfo.requiredIds; //是否必填
      relationIds = rsInfo.relationIds; //是否显示
      createWatchList({...requiredIds, ...relationIds}); //动态创建侦听器
    }
    // 表单默认数据初始化
    deal.setDefaultData(newFormList.value, newFormData.value, props.formData);
    // 表单验证
    newFormRules.value = getNewFormRules();
    // 清除表单验证
    clearValidate();
    // 重新生成节点
    if (options.pubOptions.formItemInit) {
      options.pubOptions.formItemInit();
    }
    // 初始化完毕
    setTimeout(() => (isInit = false), 500);
  }


  // 获取表单数据
  function getValueFn(d) {
    if (d._id) {
      return getVal(d._id, newFormData.value);
    } else if (d.oid) {
      return getVal(d.oid, props.formData);
    } else {
      return '';
    }
  }

  // 表单项显示处理函数
  function showFn(d, m) {
    if (isNull(d)) {
      return true;
    } else if (typeof d === 'boolean') {
      return d;
    } else if (Object.prototype.toString.call(d) === '[object Array]') {
      if (m.some) {
        return d.some(o => showVerify(o, getValueFn(o)));
      } else {
        return d.every(o => showVerify(o, getValueFn(o)));
      }
    } else {
      return showVerify(d, getValueFn(d));
    }
  }

  // 获取表单验证信息
  function getNewFormRules() {
    return verifyFormRules({
      newFormList: newFormList.value,
      newFormData: newFormData.value,
      placeholder: placeholder.value,
      formData: props.formData,
      showFn: showFn
    });
  }

  // 清空校验信息
  function clearValidate() {
    nextTick().then(() => {
      const formRef = unref(options.formRef);
      formRef && formRef.clearValidate();
    });
  }

  // 任一表单项被校验后触发
  function onFormValidate() {
    if (isInit) {
      clearValidate();  // 清除表单验证
    }
  }

  // 回调事件
  function onEmitEvent(row) {
    // 是否必填 || 是否显示
    if (requiredIds[row._id] || relationIds[row._id]) {
      // 重新修改表单验证信息
      newFormRules.value = getNewFormRules();
      // 表单验证提示清除
      clearValidate();
    }
    // 拿取第三方组件
    const components = defaultConfig.components[row.type];
    // 如果是第三方组件
    if (components) {
      const value = components.getValueEmit ? components.getValueEmit(row, newFormData.value, props.formData) : newFormData.value[row._id];
      emit('event', row, value, newFormData.value, newFormList.value);
      return;
    }
    emit('event', row, getVal(row._id, newFormData.value), newFormData.value, newFormList.value);
  }

  // 附件选择后触发
  function onFileEvent(row) {
    const rv = getVal(row._id, newFormData.value);
    const vv = isJSON(rv) && rv.src ? rv.src : rv;
    emit('event', row, vv, newFormData.value, newFormList.value);
  }

  // 初始化
  formInit();

  return {
    newFormList, newFormData, newFormRules, placeholder, labelWidth,
    getValueFn, showFn, onEmitEvent, onFileEvent, onFormValidate
  };
}


