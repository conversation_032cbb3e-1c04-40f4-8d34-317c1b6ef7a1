import {unref, nextTick} from 'vue';
import {isJSON, isArray} from '../utils/pub.js';
import {formListHandle} from '../utils/dealFn.js';
import {setFormDataFile, setFormDataInit} from '../utils/road.js';
import {formStrTo, setVal} from '../utils/other.js';
import * as deal from '../utils/deal.js';
import defaultConfig from '../config.js';

// 暴露函数
export function useExpose(options, props, expose) {

  /**
   * 获取表单所有数据 - 数据获取
   * @param opt.isFile {boolean} 文件字段只返回file对象
   * @param opt.isFileOrigin {boolean} 如果不是file对象，则返回传入值
   * @param opt.isFileUid {boolean} 文件值为file对象时绑定uid，否则绑定到id上，默认都在uid上
   * @param opt.setIds {array} 额外返回的数据的ID，插槽绑定时可能会用到
   * @param opt.default {object} 默认数据
   */
  function getFormDataFn(opt = {}) {
    const rdata = {};
    let newFormData = unref(options.newFormData);
    let newFormList = unref(options.newFormList);
    let dictConfig = unref(props.dictConfig);
    if (opt.setIds && opt.setIds.length) {
      const newSetIds = opt.setIds.map(m => formListHandle(isJSON(m) ? m : ({id: m})));
      newFormList = [].concat(newFormList).concat(newSetIds);
    }
    newFormList.forEach((m) => {
      if (m.children) {
        m.children.forEach(n => {
          formDataRowHandle(rdata, n, newFormData, opt, dictConfig);
        });
      } else {
        formDataRowHandle(rdata, m, newFormData, opt, dictConfig);
      }
    });
    return Object.assign(rdata, opt.default || {}, props.setData);
  }

  // 默认不提交的类型
  const vList = ['tips', 'divider', 'text', 'text-link', 'text-image'];

  // 将数据推入到返回JSON中
  function pushData(data, item, val) {
    if (isArray(val) && isArray(val[0]) && val.every(v => v[0] === 'push')) {
      val.forEach((v) => setVal(data, v[1], v[2]));
      return;
    }
    const orgItem = item;
    const id = orgItem.uid || orgItem.id;
    setVal(data, id, val);
  }


  // 表单行数据处理
  function formDataRowHandle(rdata, row, newFormData, opt, dictConfig) {
    const newGetNoTypeList = [...vList, ...defaultConfig.getNoTypeList];
    if (row.set !== true && newGetNoTypeList.includes(row.type) ||
      row.setRelation !== true && !options.showFn(row.relation, row) ||
      row.isSetNo) {
      return;
    }
    // 拿取第三方组件
    const components = dictConfig.components[row.type];
    if (components) {
      let val;
      if (components.getValueFn) {
        val = components.getValueFn(row, opt, options);
      } else {
        val = newFormData[row._id];
      }
      pushData(rdata, row, val);
      return;
    }
    if (row.type === 'uploadFile' || row.type === 'uploadPic' || row.type === 'uploadFileAll') {
      setFormDataFile(rdata, row, newFormData, opt);
    } else {
      setFormDataInit(rdata, row, newFormData, opt, dictConfig);
    }
  }

  // 指定字段进行表单验证
  function getFieldPromise(opt) {
    const formRef = unref(options.formRef);
    const field = opt.field.map((m) => formStrTo(m));
    return new Promise((resolve, reject) => {
      let et;
      formRef.validateField(field, (e) => (et = et || e));
      et ? reject(et) : resolve();
    });
  }

  // 获取表单所有数据
  function getFormData(opt = {}) {
    let resPromise;
    if (isArray(opt.field)) {
      resPromise = getFieldPromise(opt);
    } else {
      resPromise = unref(options.formRef).validate();
    }
    return resPromise.then(() => {
      return getFormDataFn(opt);
    }).catch(() => {
      return Promise.reject({type: 'co-form', msg: '请检查填写信息'});
    });
  }

  /**
   * 根据id获取表单当前生成数据 - 父节点调用
   * @param a {string|object} 返回的ID 或 请求参数
   * @param [b] {object} 请求参数，参考getFormData
   * @param [is] {boolean} 是否返回Promise，默认返回
   * @returns {Promise<Awaited<any>>|any}
   */
  function getFormDataKey(a, b = {}, is) {
    const opt = isJSON(a) ? a : b;
    let data = getFormDataFn(opt);
    if (a && typeof a === 'string') {
      data = data[formStrTo(a)];
    }
    return is ? Promise.resolve(data) : data;
  }

  /**
   * 获取newFormData数据
   */
  function getNewFormData() {
    return unref(options.newFormData);
  }

  /**
   * 修改newFormData数据
   * @param id {string} 要修改的表单id
   * @param val {string|number} 要修改的值
   * @param [is] {boolean} 是否去掉验证提醒
   */
  function setFormDataKey(id, val, is) {
    const newData = unref(options.newFormData);
    const _id = formStrTo(id);
    // 写入数据
    setVal(newData, _id, val);
    // 是否去掉验证提醒
    if (is !== false) {
      setTimeout(() => {
        const formRef = unref(options.formRef);
        formRef && formRef.clearValidate(_id);
      }, 0);
    }
  }

  /**
   * 批量修改newFormData数据
   */
  function setFormDataAll(data, is) {
    if (!isJSON(data)) {
      return;
    }
    const newData = unref(options.newFormData);
    const ids = Object.keys(data);
    ids.forEach((id) => {
      setVal(newData, formStrTo(id), getVal(id, data));
    });
    if (is !== false) {
      setTimeout(() => {
        const formRef = unref(options.formRef);
        formRef && formRef.clearValidate(ids);
      }, 0);
    }
  }

  /**
   * 根据id重新生成list列表
   * @param id {string} 要修改的表单id
   * @param list {array} 要修改的表单列表
   * @param is {boolean} 是否保留数据
   */
  function setFormListData(id, list, is) {
    const _id = formStrTo(id);
    const newData = unref(options.newFormData);
    const newList = unref(options.newFormList);
    const row = newList.find((m) => m._id === _id);
    if (is !== false) {
      newData[row._id] = void 0;
    }
    deal.setFormListData(row, list);
  }

  /**
   * 重新验证某id的rule
   */
  function setValidateField(id) {
    nextTick(() => {
      const formRef = unref(options.formRef);
      formRef && formRef.value.validateField(id);
    });
  }

  expose({
    getFormData,
    getFormDataKey,
    getNewFormData,
    setFormDataKey,
    setFormDataAll,
    setFormListData,
    setValidateField
  });
}
