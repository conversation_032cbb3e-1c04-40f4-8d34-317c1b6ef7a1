<script>
import render from './render';
import config from './config';

export default {
  props: {
    formList: {type: Array, default: () => []},
    formData: {type: Object, default: () => ({})},
    shareData: {type: Object}, // 共享数据，平常不建议使用，当存在两个form存在数据判断使用，由父组件传入，只传{}即可 (注：此时所有的数据将会被绑定到此字段上)
    setData: {type: Object},
    dictConfig: {type: Object, default: () => ({})}, //动态字典数据，需要配合dicKey使用
    labelWidth: {type: String, default: () => config.options.labelWidth},
    labelPosition: {type: String, default: () => config.options.labelPosition},
    isLabel: {type: Boolean, default: () => config.options.isLabel},
    inline: {type: Boolean, default: () => config.options.inline},
    size: {type: String, default: () => config.options.size}
  },
  setup: render
};
</script>

<style lang="scss">
@import './form.scss';
</style>

