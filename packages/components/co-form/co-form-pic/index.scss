.co-form-pic {
  position: relative;

  .file-container {width:150px;height:80px;position:relative;}
  .dragger {width:100%;height:100%;}


  //---------- 图片展示部分 ----------
  .framework {
    width:100%;height:100%;border-radius:4px;border:1px dashed #C9CDD4;margin-right:12px;box-sizing: border-box;
    display:inline-flex;justify-content:center;align-items:center;flex-direction:column;vertical-align:top;cursor:pointer;
  }
  .framework:hover {border-color:#165DFF;}
  .framework i {margin-top:6px;}
  .framework .el-image {width:100%;height:100%;}
  .framework .icon-plus {margin-top:0;}
  .framework .image-tips {color:#86909C;line-height:2;font-size:13px;}
  .framework .image-name {
    max-height:40px;line-height:15px;color:#165DFF;font-size:13px;margin-top:5px;padding:0 10px;text-align:center;display:block;cursor:pointer;overflow:hidden;
    text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;line-clamp:2;-webkit-line-clamp:2;word-break:break-all;word-wrap:break-word;white-space:normal;overflow-wrap:break-word;
  }

  //---------- 提示部分 ----------
  .file-tips {font-size:12px;padding:0;display:block;line-height:14px;margin-top:2px;}
  .file-tip1 {color:#86909C;}
  .file-tip2 {color:#4E5969;}
  .file-tip3 {color:#F53F3F;}

  //---------- 提示部分 ----------
  .is-dragger .framework {border:1px solid #00B42A;}

  //---------- 功能操作 ----------
  .file-icon {
    position:absolute;top:0;left:0;right:0;bottom:0;border-radius:4px;background-color:rgba(0,0,0,0.5);
    z-index:5;align-items:center;justify-content:center;display:none;
  }
  .file-icon i {margin:3px 2%;cursor:pointer;}
  .file-icon i:hover {color:#165DFF;}
  .file-container:hover > .file-icon {display:flex;}
}

.el-form-item .co-form-pic .file-tips {
  position: absolute;
  top: 100%;
  left: 0;
}
