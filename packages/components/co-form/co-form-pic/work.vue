<template>
  <div class="framework" @click="onDropEvent()">
    <template v-if="loading">
      <icon-mod icon="Loading" size="14" color="#666" class="icon-loading is-loading"></icon-mod>
      <span class="image-tips">加载中...</span>
    </template>
    <template v-else-if="!fileInfo.src">
      <icon-mod icon="Plus" size="23" color="#aaa" class="icon-plus"></icon-mod>
    </template>
    <template v-else-if="fileInfo.isPicture">
      <el-image fit="cover" :src="fileInfo.src" :preview-src-list="[fileInfo.src]" :hide-on-click-modal="true" :preview-teleported="true"/>
    </template>
    <template v-else>
      <icon-mod icon="Document" size="21" color="#2E5CF6" class="icon-tickets"></icon-mod>
      <div class="image-name">{{ fileInfo.name }}</div>
    </template>
  </div>
</template>

<script setup>
import {IconMod} from '../element';

const props = defineProps({
  fileInfo: {type: Object, default: () => ({})},
  loading: {type: Boolean, default: false}
});
const emit = defineEmits(['dropEvent']);

function onDropEvent() {
  emit('dropEvent');
}
</script>
