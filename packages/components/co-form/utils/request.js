import axios from 'axios';
import defaultConfig from '../config';
import {getFileName, getFileExt, isPicture, isPreview, isText} from './pub';

/**
 * 获取文件预览地址
 * @param {String|File} v 文件路径 或者 file对象
 * @param {String} [n] 文件名，当v1为外联地址时有效
 * @returns {Promise}
 */
export function requestFileSrc(v, n) {
  const fileSrc = defaultConfig.fileSrc;
  if (!fileSrc) {
    return Promise.reject(new Error('文件预览fileSrc未初始化'));
  }
  // 如果是函数
  if (typeof fileSrc === 'function') {
    return fileSrc(v, n).then(fileSrcHandle);
  }
  // 定义请求对象
  let options = typeof fileSrc === 'string' ? {url: fileSrc} : fileSrc;
  if (!options.url) {
    return Promise.reject(new Error('文件预览未定义URL字段'));
  }
  // 发生请求，获取文件预览地址
  return requestSrc(options, v, n);
}

const bodyType = {
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'png': 'image/png',
  'pdf': 'application/pdf',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'zip': 'application/zip',
  'rar': 'application/x-rar-compressed',
  'txt': 'text/plain',
  'html': 'text/html'
};

// 文件预览函数处理
function fileSrcHandle(data) {
  // 文件名
  if (!data.name) {
    data.name = getFileName(data.url);
  }
  // 文件类型
  if (!data.ext) {
    data.ext = getFileExt(data.url, data.name);
  }
  // 是否为图片
  if (typeof data.isPicture === 'undefined') {
    data.isPicture = isPicture(data.url, data.ext);
  }
  // 是否可预览
  if (typeof data.isPreview === 'undefined') {
    data.isPreview = isPreview(data.url, data.ext);
  }
  // 是否为文本
  if (typeof data.isText === 'undefined') {
    data.isText = isText(data.url, data.ext);
  }
  return data;
}

// 发生请求，获取文件预览地址
function requestSrc(options, v, n) {
  // 请求request
  const $request = defaultConfig.request || axios;
  // 请求头部 - 支持函数
  const headers = typeof options.headers === 'function' ? options.headers() : options.headers;
  // 额外参数 - 支持函数
  const data = typeof options.data === 'function' ? options.data() : options.data ? {...options.data} : {};
  // 定义请求体
  const requestOptions = {
    url: options.url,
    baseURL: options.baseURL, //代理地址
    headers: headers, //请求头部
    timeout: options.timeout || 50000, // 请求超时时间
    method: options.method || 'get', // 请求方式
    responseType: 'blob'
  };
  // 定义ID字段
  const key = options.key || 'id';
  // 定义请求参数key
  const requestKey = requestOptions.method.toLowerCase() === 'get' ? 'params' : 'data';
  // 赋值
  data[key] = v; //id:赋值
  requestOptions[requestKey] = data;
  // 发起请求
  return $request(requestOptions).then((res) => {
    if (res.data.size < 150) {
      return Promise.reject(new Error('文件预览失败'));
    }
    const patt = new RegExp('filename=([^;]+\\.[^.;]+);*'); //定义正则表达式
    const disposition = window.decodeURI(res.headers['content-disposition']);
    const result = patt.exec(disposition);
    const fileName = (result ? result[1].replace(/"/g, '') : n) || ''; // 文件名
    const ext = getFileExt(fileName); // 获取后缀名
    const blobType = res.data.type === 'application/octet-stream' ? bodyType[ext] || res.data.type : res.data.type; //获取文件格式
    const blob = new Blob([res.data], {type: blobType});
    return {
      src: URL.createObjectURL(blob),
      name: fileName,
      ext: ext,
      isPreview: isPreview(fileName, data.ext),
      isPicture: isPicture(fileName, data.ext),
      isText: isText(fileName, data.ext)
    };
  });
}
