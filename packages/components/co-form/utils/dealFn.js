import {getVal, dataFormat, formIdHandle, formStrTo} from './other';
import {isArray, isJSON, isNull, isNumber, isFirefox} from './pub';
import {ElMessageBox} from '../element';
import defaultConfig from '../config.js';

// 表单默认值
function formTypeDefault(m) {
  // 拿取第三方组件
  const components = m.type && defaultConfig.components[m.type];
  // 如果存在第三方组件 && 存在修改表单类型函数
  if (components && components.updateTypeFn) {
    return components.updateTypeFn(m);
  }
  if (m.disabled && (m.type === 'uploadFile' || m.type === 'uploadFileAll')) {
    return 'text-link';
  }
  if (m.disabled && m.type === 'uploadPic' || m.type === 'image') {
    return 'text-image';
  }
  if (m.type) {
    return m.type;
  }
  if (m.list || m.dic<PERSON><PERSON>) {
    return 'select';
  } else if (m.children) {
    return 'children';
  } else if (m.slot) {
    return 'slot';
  } else {
    return 'inputText';
  }
}

// 表单attributes
function formAttributes(m) {
  const ra = m.attributes ? Object.assign({}, m.attributes) : {};
  // 是否禁止
  if (typeof m.disabled === 'boolean') {
    ra.disabled = m.disabled;
  }
  switch (m.type) {
    case 'divider': // 分割线
      ra['content-position'] = m.contentPosition;
      break;
    case 'select': // 下拉框
      ra.clearable = true;
      ra.multiple = m.multiple; //是否多选
      ra['popper-class'] = ['zIndex-5000', m.popperClass].join(' ');
      ra.filterable = m.filterable; //是否可筛选
      break;
    case 'date':  // 日期
      ra.type = m.type;
      ra['value-format'] = m.valueFormat || 'YYYY-MM-DD';
      ra['popper-class'] = ['zIndex-5000', m.popperClass].join(' ');
      ra['disabled-date'] = m.disabledDate;
      break;
    case 'datetime':  // 日期时间
      ra.type = m.type;
      ra['value-format'] = m.valueFormat || 'YYYY-MM-DD HH:mm:ss';
      ra['popper-class'] = ['zIndex-5000', m.popperClass].join(' ');
      ra['disabled-date'] = m.disabledDate;
      break;
    case 'daterange': //关联日期
      ra.type = m.type;
      ra['value-format'] = m.valueFormat || 'YYYY-MM-DD';
      ra['popper-class'] = ['zIndex-5000', m.popperClass].join(' ');
      ra['disabled-date'] = m.disabledDate;
      ra['start-placeholder'] = ra.startPlaceholder || '开始日期';
      ra['end-placeholder'] = ra.startPlaceholder || '结束日期';
      ra['range-separator'] = ra.rangeSeparator || '至';
      break;
    case 'switch':  // 开关
      ra['active-value'] = m.activeValue;
      ra['inactive-value'] = m.inactiveValue;
      break;
    case 'number': //数字输入
      ra.type = isFirefox ? 'text' : 'number';
      break;
    case 'textarea':  // 多行文本
      ra.type = 'textarea';
      ra.rows = m.rows || 4;
      ra.maxlength = m.max;
      ra['show-word-limit'] = typeof m.showWordLimit === 'boolean' ? m.showWordLimit : !!m.max;
      break;
  }
  return ra;
}

// 获取重复ID提示
export function repeatPrompt() {
  const htm = '存在重复的ID，请处理成不同的ID，然后使用uid可保证返回结果一致';
  ElMessageBox.alert(htm, '提示', {
    type: 'warning'
  });
}

// 表单内容处理
export function formListHandle(m) {
  // 字段数据处理
  m = Object.assign({}, m);
  // 数据ID处理
  m = formIdHandle(m);
  // for 循环的 key
  m.key = m._id;
  // 定义type
  m.type = formTypeDefault(m);
  // 定义attributes
  m.attributes = formAttributes(m);
  // list数据处理
  if (m.list) {
    m.newList = dataFormat(m.list);
  }
  // 数据处理，required: 是否必填
  if (m.required) {
    m.required = isArray(m.required) ? m.required.map(formIdHandle) : formIdHandle(m.required);
  }
  // 数据处理，relation: 是否显示
  if (m.relation) {
    m.relation = isArray(m.relation) ? m.relation.map(formIdHandle) : formIdHandle(m.relation);
  }
  //表单项处理完毕后，调用此函数，做最终处理
  if (defaultConfig.formItemHandle) {
    return defaultConfig.formItemHandle(m, m);
  }
  return m;
}

// 获取默认值
function getDefaultValue(value, formData) {
  if (isNull(value)) {
    return;
  }
  if (isJSON(value) && value.oid) {
    value = getVal(value.oid, formData);
  }
  if (isJSON(value) && !isNull(value.value)) {
    return value.value;
  } else {
    return value;
  }
}

// 处理开关默认值
function getSwitchValue(m, val) {
  const attributes = m.attributes;
  const activeValue = attributes['active-value']; //打开值
  const inactiveValue = attributes['inactive-value']; //关闭值
  if (isNull(val)) {
    return !isNull(inactiveValue) ? inactiveValue : false;
  }
  if (isNull(activeValue) && isNull(inactiveValue)) {
    return typeof val === 'boolean' ? val : false;
  }
  if (activeValue === val || inactiveValue === val) {
    return val;
  } else {
    return false;
  }
}

// 获取处理的value值
function getFormValue(m, val) {
  // 将返回值进行字符串分割
  if (m.split && typeof val === 'string') {
    val = val.split(m.split);
    val = m.splitNumber ? val.map(m => +m) : val;
  }
  // 如果需要转为数值型
  if (m.isNumber && typeof val === 'string') {
    val = parseFloat(val);
  }
  // 如果需要转为字符串
  if (m.isString && isNumber(val)) {
    val = '' + val;
  }
  // 单一复选框
  if (m.type === 'checkboxValue') {
    val = typeof val === 'boolean' ? val : val === m.trueValue;
  }
  // 如果是开关
  if (m.type === 'switch') {
    val = getSwitchValue(m, val);
  }
  // 复选框 和 下拉框多选 需要为数组格式
  if ((m.type === 'checkbox' || m.multiple) && !isArray(val)) {
    val = val && typeof val !== 'object' || val === 0 ? [val] : [];
  }
  return val;
}

// 文件类型对象 - 不支持数组格式
const fileTypeList = ['uploadFile', 'uploadPic'];
// 文件类型对象 - 支持数组格式
const fileTypeAllList = ['uploadFileAll', 'text-link', 'image'];

export function formDataHandle(m, formData) {
  // 获取默认值
  let val = getVal(m.id, formData);
  // 如果值不为空，则修改显示类型 --- 在默认值之前
  if (m.isBefore && !isNull(val)) {
    m.type = m.isBefore;
  }
  // 默认值赋值
  if (isNull(val)) {
    val = getDefaultValue(m.value, formData);
  } else if (fileTypeAllList.includes(m.type)) { //支持数组格式
    val = isJSON(val) ? val : {src: val};
  } else if (fileTypeList.includes(m.type)) { //不支持数组格式
    val = isArray(val) ? val[0] : val;
    val = isJSON(val) ? val : {src: val};
  } else if (isJSON(val) && m.type !== 'text') { //JSON格式数据
    val = val.value;
  }
  // 如果值不为空，则修改显示类型 --- 在默认值之后
  if (m.isAfter && !isNull(val)) {
    m.type = m.isAfter;
  }
  let newVal;
  // 拿取第三方组件
  const components = m.type && defaultConfig.components[m.type];
  if (components && components.setValueFn) {
    newVal = components.setValueFn(val, m, formData);
  } else {
    newVal = getFormValue(m, val);
  }
  // 最终数据处理
  if (defaultConfig.valueHandle) {
    return defaultConfig.valueHandle(val, m, formData);
  }
  return newVal;
}
