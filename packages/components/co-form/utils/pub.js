import {requestFileSrc} from './request.js';
import {ElMessage} from '../element';

// 图片文件类型
export const PICTURE_TYPE = ['png', 'jpg', 'jpeg', 'bmp'];
// 可以预览的文件类型
export const PREVIEW_TYPE = ['png', 'jpg', 'jpeg', 'bmp', 'pdf'];
// 文本类型
export const TEXT_TYPE = ['txt', 'html'];

// 是否为空
export function isNull(a) {
  return typeof a === 'undefined' || a === null || a === '';
}

// 判断是否是数字
export function isNumber(a) {
  return typeof a !== 'undefined' && a != null && !isNaN(a) && Object.prototype.toString.call(a) === '[object Number]';
}

// 是否为json
export function isJSON(a) {
  return a && Object.prototype.toString.call(a) === '[object Object]';
}

// 是否为数组
export function isArray(a) {
  return a && Object.prototype.toString.call(a) === '[object Array]';
}

// 是否为文件
export function isFile(a) {
  return a && Object.prototype.toString.call(a) === '[object File]';
}

// 判断是否为外链地址
export function isExternal(v) {
  return /^(https?:|mailto:|tel:|blob:)/.test(v);
}

// 是否为火狐浏览器
export const isFirefox = (function () {
  const userAgent = navigator.userAgent;
  return /firefox/i.test(userAgent);
})();

/**
 * 是否为图片格式
 * @param {String|File} a 文件路径 或者 file对象
 * @param {String} [b] 后缀名，可不传
 * @returns {boolean}
 */
export function isPicture(a, b) {
  if (b) {
    return PICTURE_TYPE.includes(b.toLowerCase());
  } else if (a) {
    return PICTURE_TYPE.includes(getFileExt(a));
  } else {
    return false;
  }
}

/**
 * 是否为可预览
 * @param {String|File} a 文件路径 或者 file对象
 * @param {String} [b] 后缀名，可不传
 * @returns {boolean}
 */
export function isPreview(a, b) {
  if (b) {
    return PREVIEW_TYPE.includes(b.toLowerCase());
  } else if (a) {
    return PREVIEW_TYPE.includes(getFileExt(a));
  } else {
    return false;
  }
}

/**
 * 是否为文本类型
 * @param {String|File} a 文件路径 或者 file对象
 * @param {String} [b] 后缀名，可不传
 * @returns {boolean}
 */
export function isText(a, b) {
  if (b) {
    return TEXT_TYPE.includes(b.toLowerCase());
  } else if (a) {
    return TEXT_TYPE.includes(getFileExt(a));
  } else {
    return false;
  }
}

/**
 * 获取文件扩展名
 * @param {String|File} a 文件路径 或者 file对象
 * @param {String} [b] 文件名，可以不传
 * @returns {String}
 */
export function getFileExt(a, b) {
  const name = b || getFileName(a);
  const ext = name && name.indexOf('.') > -1 ? name.substring(name.lastIndexOf('.') + 1).toLowerCase() : '';
  return ext.toLowerCase();
}

/**
 * 获取文件名
 * @param {String|File} a 文件路径 或者 file对象
 * @returns {String}
 */
export function getFileName(a) {
  if (!a) {
    return '';
  } else if (isFile(a)) {
    return a.name;
  } else {
    const sa = a.indexOf('?') ? a.split('?')[0] : a;
    const sp = sa.includes('/') ? sa.split('/') : sa.split('\\');
    return sp[sp.length - 1];
  }
}

/**
 * 文件预览处理
 * @param {String|File} v 文件路径 或者 file对象
 * @param {String} [n] 文件名，当v为外链时有效
 * @param {Boolean} [is] 当不是File对象和外链时，是否从服务器获取数据，默认获取
 * @returns {Promise}
 */
export function getFileSrc(v, n, is = true) {
  const p1 = isFile(v);
  // 如果是文件 || 外链
  if (p1 || isExternal(v) || !is) {
    const name = p1 ? v.name : n || v;
    const ext = getFileExt(name); // 获取后缀名
    return Promise.resolve({
      src: p1 ? URL.createObjectURL(v) : v,
      name: getFileName(name),
      ext: ext,
      isPicture: isPicture(name, ext),
      isPreview: isPreview(name, ext),
      isText: isText(name, ext)
    });
  } else {
    return requestFileSrc(v, n);
  }
}

/**
 * 文件执行下载
 * @param {String|File} a  文件路径 或 文件file对象
 * @param {String} [name] 下载文件名，可不传
 */
export const downloadFile = function (a, name) {
  const p1 = isFile(a);
  if (p1 && 'msSaveOrOpenBlob' in window.navigator) {
    window.navigator.msSaveOrOpenBlob(new Blob([a], {type: a.type}));
    return;
  }
  if (p1 && !name) {
    name = a.name;
  }
  const href = p1 ? URL.createObjectURL(a) : a;
  const link = document.createElement('a');
  if (name) {
    link.setAttribute('download', name);
  }
  link.setAttribute('href', href);
  link.setAttribute('target', '_blank');
  link.click();
};

/**
 * 根据地址进行XMLHttpRequest下载
 * @param {String} a  文件路径 或 文件file对象
 * @param {String} [name] 下载文件名，可不传
 */
export const downloadText = function (a, name) {
  if (/^(blob:)/.test(a) || isFile(a)) {
    downloadFile(a, name);
    return;
  }
  const xhr = new XMLHttpRequest();
  xhr.open('GET', a, true);
  xhr.responseType = 'blob';
  xhr.onload = function () {
    if (xhr.status === 200) {
      const blobUrl = URL.createObjectURL(xhr.response);
      const link = document.createElement('a');
      if (name) {
        link.download = name;
      }
      link.href = blobUrl;
      document.body.appendChild(link);
      link.click();
      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl); // 释放 Blob URL
      }, 0);
    }
  };
  xhr.onerror = function () {
    ElMessage.error('预览下载失败，当前文件不存在');
  };
  xhr.send();
};


/**
 * 文件选择处理
 * @param target 选择文件后，可以拿到target对象
 * @param {Number} [a] 文件可上传大小，默认5MB
 * @param {Array|String} [b] 文件可上传类型，
 * @returns {{name: String, size: Number, ext: String, err: String, file: File}}
 * {name:文件名, size:文件大小, ext:文件类型, file:文件上传对象, err:选择的文件是否异常，通过这个判断是否可以上传}
 */
export function fileHandle(target, a, b) {
  if (b && typeof b === 'string') {
    b = b.split(',');
  }
  const sizeMax = a || 5;
  const upType = b || ['zip', 'rar', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'png', 'jpg', 'jpeg', 'bmp'];
  const upTipsSize = sizeMax < 0.9765625 ? Math.floor(sizeMax * 1024) + 'KB' : sizeMax + 'MB';
  let name, size = 0, ext, err;
  const file = isFile(target) ? target : target.files && target.files[0];
  if (file) {
    name = file.name.substring(file.name.lastIndexOf('\\') + 1); //文件名
    size = file ? file.size : 0; //文件大小
    ext = name.substring(name.lastIndexOf('.') + 1).toLowerCase(); //文件扩展名
  } else {
    return {name: '', size: 0, ext: '', err: '', file: null};
  }
  if (upType.indexOf(ext) === -1) {
    err = '选择文件格式不正确，支持格式: ' + upType.join(',');
  } else if (size <= 0) {
    err = '选择文件大小不能为0';
  } else if (size > sizeMax * 1024 * 1024) {
    err = '选择文件过大，最大' + upTipsSize;
  }
  if (err) {
    return {name: '', size: 0, ext: '', err: err, file: null};
  }
  return {name: name, size: size, ext: ext, err: err, file: file};
}

/**
 * 统一社会信用代码校验
 * @param code 统一社会信用代码
 * @returns {boolean} 验证结果
 */
export function checkSocialCreditCode(code) {
  let patrn = /^[0-9A-Z]+$/;
  //18位校验及大写校验
  if (!code || code.length !== 18 || patrn.test(code) === false) {
    return false;
  }
  let Ancode; //统一社会信用代码的每一个值
  let Ancodevalue; //统一社会信用代码每一个值的权重
  let total = 0;
  let weightedfactors = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28]; //加权因子
  let str = '0123456789ABCDEFGHJKLMNPQRTUWXY';
  //不用I、O、S、V、Z
  for (let i = 0; i < code.length - 1; i++) {
    Ancode = code.substring(i, i + 1);
    Ancodevalue = str.indexOf(Ancode);
    total = total + Ancodevalue * weightedfactors[i];
    //权重与加权因子相乘之和
  }
  let logiccheckcode = 31 - (total % 31);
  if (logiccheckcode === 31) {
    logiccheckcode = 0;
  }
  let Str = '0,1,2,3,4,5,6,7,8,9,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,T,U,W,X,Y';
  let Array_Str = Str.split(',');
  logiccheckcode = Array_Str[logiccheckcode];
  return logiccheckcode === code.substring(17, 18);
}

/**
 * 验证身份证号
 * @param a 身份证号
 * @returns {String} 验证结果
 */
export function isIdentityId(a) {
  if (!a) {
    return '身份证号不能为空';
  }
  a = a.toString();
  let pattern = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/;//长度或格式校验
  // 地区校验
  let aCity = {
    11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林', 23: '黑龙江',
    31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东',
    41: '河南', 42: '湖北', 43: '湖南', 44: '广东', 45: '广西', 46: '海南',
    50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏',
    61: '陕西', 62: '甘肃', 63: '青海', 64: '宁夏', 65: '新疆',
    71: '台湾', 81: '香港', 82: '澳门', 91: '国外'
  };
  // 出生日期验证
  let day = (a.substr(6, 4) + '-' + Number(a.substr(10, 2)) + '-' + Number(a.substr(12, 2))).replace(/-/g, '/');
  let date = new Date(day);
  // 身份证号码校验 最后4位  包括最后一位的数字/字母X
  let sum = 0, weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2], codes = '10X98765432';
  for (let i = 0; i < a.length - 1; i++) {
    sum += a[i] * weights[i];
  }
  let err, last = codes[sum % 11]; //计算出来的最后一位身份证号码;
  if (a === '') {
    err = '身份证号不能为空';
  } else if (!pattern.exec(a)) {
    err = '你输入的身份证长度或格式错误';
  } else if (!aCity[parseInt(a.substr(0, 2))]) {
    err = '你的身份证地区非法';
  } else if (day !== date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate()) {
    err = '身份证上的出生日期非法';
  } else if (a[a.length - 1] !== last) {
    err = '你输入的身份证号非法';
  }
  return err;
}
