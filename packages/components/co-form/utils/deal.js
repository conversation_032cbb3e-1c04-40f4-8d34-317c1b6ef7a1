import {unref} from 'vue';
import {setPlaceholder, setRequiredIds, dataFormat} from './other';
import {formListHandle, formDataHandle, repeatPrompt} from './dealFn.js';

// 根据formList，生成新的表单
export function getNewFormList(formList) {
  return formList.map((m, i) => {
    m = formListHandle(m);
    // 如果是多层数据
    if (m.children) {
      m.key = 'son' + i;
      m.children = m.children.map(n => formListHandle(n));
    }
    if (!m.key) {
      m.key = 'key' + i;
    }
    return m;
  });
}

// 初始化表单信息
export function getFormListInfo(newFormList, shareData) {
  const newFormData = shareData ? unref(shareData) : {}; //表单数据
  const placeholder = {}; //表单提示信息
  const requiredIds = {}; //是否必填
  const relationIds = {}; //是否显示
  let isRepeatList = [], isRepeat = false; //是否重复ID
  function createFn(m) {
    if (m._id) {
      newFormData[m._id] = void (0);
      // 重复判断
      if (!isRepeatList.includes(m._id)) {
        isRepeatList.push(m._id);
      } else {
        isRepeat = true;
      }
    }
    // 表单提示信息
    setPlaceholder(placeholder, m);
    // 失去焦点、状态切换，是否触发事件字段，减少不必要操作
    setRequiredIds(requiredIds, m.required); // 是否必填
    setRequiredIds(relationIds, m.relation); // 是否显示
  }

  // 循环
  newFormList.forEach(m => {
    // 如果是标题
    if (m.type === 'tips') {
      return;
    }
    if (m.children) {
      m.children.forEach(createFn);
    } else {
      createFn(m);
    }
  });

  if (isRepeat) {
    repeatPrompt();
  }

  return {newFormData, placeholder, requiredIds, relationIds};
}


// 表单默认数据初始化
export function setDefaultData(newFormList, newFormData, formData) {
  newFormList.forEach(m => {
    // 如果是标题
    if (m.type === 'tips') {
      return;
    }
    if (m.children) {
      m.children.forEach((n) => {
        newFormData[n._id] = formDataHandle(n, formData);
      });
    } else {
      newFormData[m._id] = formDataHandle(m, formData);
    }
  });
}

// 重新生成list列表
export function setFormListData(row, list) {
  if (list) {
    row.newList = dataFormat(list);
  }
}


