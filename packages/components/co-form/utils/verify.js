import {isJ<PERSON><PERSON>, isNull, isArray, checkSocialCreditCode, isIdentityId} from './pub';
import defaultConfig from '../config.js';

const reg_phone1 = /^1\d{10}$/;  //手机验证正则
const reg_phone2 = /^\d[\d-]{0,18}\d$/; //电话号码验证
const reg_email = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/; //电子邮箱
const reg_number = /^[0-9]{1,100}$/;  //正整数
const reg_money = /^-?(0|[1-9]\d*)(\.\d{1,2})?$/;  //金额
const reg_money_6 = /^-?(0|[1-9]\d*)(\.\d{1,6})?$/;  //金额-万元
const reg_chinese = /^[\u4e00-\u9fa5]*$/;  //中文汉字
const reg_noChinese = /^[^\u4e00-\u9fa5]*$/;  //不包含中文字符

export const verifyRule = {
  reg_phone1,
  reg_phone2,
  reg_email,
  reg_number,
  reg_money,
  reg_money_6,
  reg_chinese,
  reg_noChinese
};

/**
 * 根据配置动态生成数字验证正则表达式
 * @param {Object} config - 配置对象
 * @param {number} config.int - 整数位数限制，默认10位
 * @param {number} config.decimal - 小数位数限制，默认2位
 * @param {boolean} config.minus - 是否允许负数，默认false
 * @returns {RegExp} 生成的正则表达式
 */
function generateNumberRegex(config = {}) {
  const {
    int = 10,           // 默认整数位数限制
    decimal = 2,        // 默认小数位数限制
    minus = false       // 默认不允许负数
  } = config;

  // 负号部分（可选）
  const negativeSign = minus ? '-?' : '';

  // 整数部分：0 或者 1-9开头的数字，限制位数
  const integerPart = int === 1
    ? '(0|[1-9])'
    : `(0|[1-9]\\d{0,${int - 1}})`;

  // 小数部分：可选的小数点和小数位数
  const decimalPart = decimal > 0
    ? `(\\.\\d{1,${decimal}})?`
    : '';

  // 组合完整的正则表达式
  const regexStr = `^${negativeSign}${integerPart}${decimalPart}$`;

  return new RegExp(regexStr);
}

/**
 * 表单验证判断
 * @param m 当前列表数据
 * @param newFormData 表单数据
 * @param formData 传入数据
 * @returns {Object} 验证规则
 */
function verifyRules(m, newFormData, formData) {
  if (m.pattern) { //自定义正则表达式
    m.pattern = typeof m.pattern === 'string' ? new RegExp(m.pattern) : m.pattern;
    return {validator: textValidator, name: m.name, reg: m.pattern, row: m};
  } else if (typeof m.validate === 'object') {  //自定义对象
    // 检查是否为数字位数配置
    if (m.validate.type === 'number' && (
      m.validate.int !== undefined ||
      m.validate.decimal !== undefined ||
      m.validate.minus !== undefined
    )) {
      // 生成动态正则表达式
      const regex = generateNumberRegex({
        int: m.validate.int,
        decimal: m.validate.decimal,
        minus: m.validate.minus
      });

      // 返回数字位数验证规则
      return {
        validator: numberDigitsValidator,
        name: m.name,
        reg: regex,
        int: m.validate.int,
        decimal: m.validate.decimal,
        minus: m.validate.minus,
        min: m.validate.min || m.min,
        max: m.validate.max || m.max,
        mins: m.validate.mins || m.mins,
        dw: m.validate.dw || m.dw
      };
    }
    return m.validate;
  } else if (typeof m.validate === 'function') { //自定义函数
    return {validator: m.validate, name: m.name, row: m, data: newFormData, formData: formData};
  } else if (m.validate && defaultConfig.verifyValidate && defaultConfig.verifyValidate[m.validate]) {
    return {validator: defaultConfig.verifyValidate[m.validate], name: m.name, row: m, data: newFormData, formData: formData};
  } else if (m.validate === 'number') { //正整数
    return {validator: regExpValidator, reg: reg_number, name: m.name, min: m.min, max: m.max, mins: m.mins, dw: m.dw};
  } else if (m.validate === 'money') { //金额
    return {validator: regExpValidator, reg: reg_money, name: m.name, min: m.min, max: m.max, mins: m.mins, dw: m.dw};
  } else if (m.validate === 'moneyTenThousand') { //金额 - 单位万元
    return {validator: regExpValidator, reg: reg_money_6, name: m.name, min: m.min, max: m.max, mins: m.mins, dw: m.dw};
  } else if (m.validate === 'phone') { //手机、电话
    return {validator: phoneValidator, name: m.name};
  } else if (m.validate === 'phone2') { //手机
    return {pattern: reg_phone1, name: m.name, message: m.name + '格式不正确'};
  } else if (m.validate === 'email') { //电子邮箱
    return {pattern: reg_email, name: m.name, message: m.name + '格式不正确'};
  } else if (m.validate === 'isIdentityId') { //身份证
    return {validator: $_isIdentityIdValidator};
  } else if (m.validate === 'checkSocialCreditCode') { //统一社会信用代码
    return {validator: $_checkSocialCreditCodeValidator};
  } else if (m.validate === 'chinese') { //中文汉字
    const msg = m.name + '只能输入中文汉字';
    return {validator: textValidator, reg: reg_chinese, name: m.name, msg: msg, min: m.min, max: m.max};
  } else if (m.validate === 'noChinese') { //不包含中文字符
    const msg = m.name + '不能包含中文字符';
    return {validator: textValidator, reg: reg_noChinese, name: m.name, msg: msg, min: m.min, max: m.max};
  } else if (!isNull(m.min) || !isNull(m.max)) { //长度验证
    return {validator: textValidator, name: m.name, min: m.min, max: m.max};
  }
}

// 数组正则表达式验证
function regExpValidator(rule, value, callback) {
  if (!value && value !== 0) {
    callback();
    return;
  }
  let err;
  if (rule.reg && !rule.reg.test(value)) {
    err = rule.msg || (rule.name + '格式不正确');
  } else if (rule.mins === 0 && rule.mins >= value) {
    err = rule.name + '不能为负数或者为0';
  } else if (rule.mins && rule.mins >= value) {
    err = rule.name + '不能低于' + rule.mins + (rule.dw || '');
  } else if (!isNull(rule.min) && rule.min > value) {
    err = rule.name + '最低为' + rule.min + (rule.dw || '');
  } else if (!isNull(rule.max) && rule.max < value) {
    err = rule.name + '最高为' + rule.max + (rule.dw || '');
  }
  if (err) {
    callback(new Error(err));
  } else {
    callback();
  }
}

// 文本正则表达式验证
function textValidator(rule, value, callback) {
  if (isNull(value)) {
    callback();
    return;
  }
  let err;
  if (rule.reg && !rule.reg.test(value)) {
    err = rule.msg || (rule.name + '格式不正确');
  } else if (!isNull(rule.min) && rule.min > value.toString().length) {
    err = rule.name + '最少输入' + rule.min + '位字符';
  } else if (!isNull(rule.max) && rule.max < value.toString().length) {
    err = rule.name + '最多输入' + rule.max + '位字符';
  }
  if (err) {
    callback(new Error(err));
  } else {
    callback();
  }
}

//手机号码 or 电话验证
function phoneValidator(rule, value, callback) {
  if (value && !reg_phone1.test(value) && !reg_phone2.test(value)) {
    callback(new Error(rule.name + '格式不正确'));
  } else {
    callback();
  }
}

/**
 * 数字位数验证器
 * @param {Object} rule - 验证规则对象
 * @param {string|number} value - 待验证的值
 * @param {Function} callback - 回调函数
 */
function numberDigitsValidator(rule, value, callback) {
  // 空值检查
  if (!value && value !== 0) {
    callback();
    return;
  }

  const strValue = String(value);
  let err;

  // 负数支持检查
  if (!rule.minus && strValue.startsWith('-')) {
    err = rule.name + '不能为负数';
  } else {
    // 分离负号和数字部分
    const isNegative = strValue.startsWith('-');
    const numberPart = isNegative ? strValue.slice(1) : strValue;

    // 分离整数和小数部分
    const parts = numberPart.split('.');
    const integerPart = parts[0] || '0';
    const decimalPart = parts[1] || '';

    // 整数位数验证
    if (rule.int && integerPart.length > rule.int) {
      err = rule.name + `整数部分不能超过${rule.int}位`;
    }
    // 小数位数验证
    else if (rule.decimal !== undefined && decimalPart.length > rule.decimal) {
      if (rule.decimal === 0) {
        err = rule.name + '必须为整数';
      } else {
        err = rule.name + `小数部分不能超过${rule.decimal}位`;
      }
    }
    // 正则表达式格式验证
    else if (rule.reg && !rule.reg.test(strValue)) {
      err = rule.name + '格式不正确';
    }
    // 数值范围验证（兼容现有逻辑）
    else {
      const numValue = parseFloat(strValue);
      if (rule.mins === 0 && numValue <= 0) {
        err = rule.name + '必须大于0';
      } else if (rule.mins && numValue < rule.mins) {
        err = rule.name + '不能低于' + rule.mins + (rule.dw || '');
      } else if (!isNull(rule.min) && numValue < rule.min) {
        err = rule.name + '最低为' + rule.min + (rule.dw || '');
      } else if (!isNull(rule.max) && numValue > rule.max) {
        err = rule.name + '最高为' + rule.max + (rule.dw || '');
      }
    }
  }

  if (err) {
    callback(new Error(err));
  } else {
    callback();
  }
}

// 身份证
const $_isIdentityIdValidator = function (rule, value, callback) {
  let err = value ? isIdentityId(value) : '';
  if (err) {
    callback(new Error(err));
  } else {
    callback();
  }
};

// 统一社会信用代码
const $_checkSocialCreditCodeValidator = function (rule, value, callback) {
  let is = value ? checkSocialCreditCode(value) : true;
  if (!is) {
    callback(new Error('统一社会信用代码格式不正确'));
  } else {
    callback();
  }
};

// 附件验证规则
export function uploadFileValidator(rule, value, callback) {
  const val = isJSON(value) ? value.src : value;
  let err;
  if (isJSON(value) && value.error) {
    err = value.error;
  } else if (isArray(val) && !val.length && rule.required) {
    err = rule.row && rule.row.msg || '请选择' + rule.name;
  } else if (!val && rule.required) {
    err = rule.row && rule.row.msg || '请选择' + rule.name;
  }
  if (err) {
    callback(new Error(err));
  } else {
    callback();
  }
}

// 验证规则通过 change 触发
const changeVerify = [
  'date', 'datetime', 'daterange',
  'select', 'radio', 'checkbox', 'checkboxValue', 'switch',
  'uploadFile', 'uploadPic', 'uploadFileAll'
];
// 附件验证
const fileValidator = ['uploadFile', 'uploadPic', 'uploadFileAll'];

// 获取表单验证
function getVerifyRules(row, options, type) {
  // 拿取第三方组件
  const components = defaultConfig.components[row.type];
  // 验证规则触发方式
  let isChange;
  if (components) {
    isChange = components.trigger || 'change';
  } else {
    isChange = changeVerify.includes(row.type) ? 'change' : 'blur';
  }
  const trigger = row.trigger || isChange;
  const verify = [];
  if (options.showFn(row.required, row)) {
    let r1;
    if (components && components.validate) {
      r1 = {required: true, validator: components.validate, name: row.name, row: row, data: options.newFormData, formData: options.formData};
    } else if (fileValidator.includes(row.type)) {
      r1 = {required: true, validator: uploadFileValidator, name: row.name};
    } else {
      r1 = {required: true, message: options.placeholder[row._id]};
    }
    if (type === 1) {
      r1.trigger = trigger;
    }
    verify.push(r1);
  }
  if (!components) {
    const r2 = verifyRules(row, options.newFormData, options.formData);
    if (r2) {
      if (type === 1) {
        r2.trigger = trigger;
      }
      verify.push(r2);
    }
  }
  return verify;
}

const noList = ['divider', 'text', 'text-link', 'text-image', 'tips'];

// 获取验证信息
function getRulesInfo(rules, row, options, type) {
  const newNoList = [...defaultConfig.verifyNoTypeList, ...noList];
  // 如果为此类型 || 表单禁止输入
  if ((newNoList.includes(row.type) || row.disabled) && row.isRequired !== true) {
    return;
  }
  // 获取表单验证
  const rv = getVerifyRules(row, options, type);
  if (rv && rv.length) {
    rules[row._id] = rv;
  }
}

/**
 * 检测children数组中是否包含必填子字段
 * @param {Array} children - 子字段数组
 * @param {Object} options - 表单选项，包含showFn函数
 * @returns {boolean} 是否包含必填子字段
 */
export function hasRequiredChildren(children, options) {
  if (!children || !Array.isArray(children)) {
    return false;
  }

  return children.some(child => {
    // 检查子字段是否显示（隐藏的字段不参与必填检测）
    if (!options.showFn(child.relation, child)) {
      return false;
    }

    // 检查子字段是否为必填
    // 使用与验证系统相同的逻辑判断必填状态
    const isRequired = options.showFn(child.required, child);
    return isRequired;
  });
}

/**
 * 表单验证规则
 */
export function verifyFormRules(options) {
  const rules = {};
  options.newFormList.forEach(m => {
    // 如果为隐藏状态
    if (!options.showFn(m.relation, m)) {
      return;
    }
    // 如果是单列
    if (!m.children) {
      getRulesInfo(rules, m, options, 1);
      return;
    }
    // 如果是多列（children字段）- 重构为独立验证
    m.children.forEach(n => {
      // 判断是否为隐藏状态，隐藏状态不需要验证规则
      if (options.showFn(n.relation, n)) {
        // 直接为每个子字段创建独立的验证规则，绑定到子字段的_id上
        getRulesInfo(rules, n, options, 1);
      }
    });
  });
  return rules;
}

