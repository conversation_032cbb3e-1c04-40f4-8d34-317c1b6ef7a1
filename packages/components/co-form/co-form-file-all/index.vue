<template>
  <div style="width:100%;">
    <el-upload class="co-form-file-all" v-model:file-list="fileList"
               multiple action="#" :auto-upload="false"
               :limit="useOptions.limit"
               :accept="upType.accept"
               :on-preview="onPreview"
               :on-change="onChange"
               :on-remove="onRemove"
               :on-exceed="onExceed">
      <el-button type="primary">请选择文件</el-button>
      <template #tip>
        <div class="el-upload__tip">{{ prompt }}</div>
      </template>
    </el-upload>
    <el-image-viewer v-on:close="onClosePreview" :url-list="[previewURL]" :hide-on-click-modal="true" :teleported="true" v-if="previewURL"/>
  </div>
</template>

<script setup>
import {ref, computed, watch, toRaw, nextTick} from 'vue';
import {ElMessage, ElUpload, ElImageViewer, getFormItem} from '../element';
import {useFormPreview} from '../use/usePreview.js';
import * as other from './other';

const props = defineProps({
  // 参数信息 sizeMax:{Number}文件大小  upType:{Array|String}文件类型  tips:{String}补充提醒文字
  options: {type: Object, default: () => ({})},
  // v-model绑定的值
  modelValue: [Array, Object, String, File],
  // 绑定值是否为JSON对象，默认为String
  // 在ElForm表单下，绑定值强行为JSON对象，绑定格式{src:选择的文件, error:选择文件异常提醒}
  toJSON: {type: Boolean, default: false},
  // 是否支持拖拽上传
  dragger: {type: Boolean, default: false}
});

const emit = defineEmits(['update:modelValue', 'event']);
const {previewURL, onPreview, onClosePreview, onPreviewDestroy} = useFormPreview();

// 定义
const useOptions = {
  upTypeDefault: ['zip', 'rar', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'png', 'jpg', 'jpeg', 'bmp'], //上传默认类型
  sizeMaxDefault: 10, //上传默认大小，单位：MB
  limit: props.options.limit || 9, //选择文件最大数量
  isOriginValue: props.options.isOriginValue === true, //是否返回本来的值
  formItem: getFormItem(),
  toJSON: props.toJSON
};
// 文件列表
const fileList = ref([]);
// 当前上传列表
let saveFileList = void (0);
// 如果传入的参数有变化
watch(() => props.modelValue, function (newValue) {
  if (saveFileList !== toRaw(newValue)) {
    init();
  }
}, {immediate: true});

// 文件支持类型
const upType = computed(() => {
  let type = props.options.upType || useOptions.upTypeDefault;
  if (typeof type === 'string') {
    type = type.split(',');
  }
  return {
    text: '支持格式: ' + type.join(', '),
    accept: type.map(ext => ('.' + ext)).join(','),
    value: type
  };
});

// 文件大小
const sizeMax = computed(() => {
  const size = props.options.sizeMax || useOptions.sizeMaxDefault;
  const tips = size < 0.9765625 ? window.Math.floor(size * 1024) + 'KB' : size + 'MB';
  return {
    text: '最大' + tips,
    value: size
  };
});

// 提示信息
const prompt = computed(() => {
  let rt = '';
  if (props.options.tips) {
    rt += props.options.tips + '；';
  }
  return rt + upType.value.text + '；' + sizeMax.value.text;
});

// 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
function onChange() {
  nextTick(() => {
    const re = other.fileVerify(fileList.value, upType.value, sizeMax.value);
    if (re.error) {
      fileList.value = re.fileList;
      ElMessage({message: re.error, type: 'warning', showClose: true});
    }
    saveFileList = other.getValue(fileList.value, useOptions);
    emit('update:modelValue', saveFileList);
    emit('event', 'select', saveFileList);
  });
}

// 文件列表移除文件时的钩子
function onRemove() {
  nextTick(() => {
    saveFileList = other.getValue(fileList.value, useOptions);
    emit('update:modelValue', saveFileList);
    emit('event', 'delete', saveFileList);
  });
}

// 当超出限制时，执行的钩子函数
function onExceed() {
  ElMessage.warning('选择文件数量超过限制，最多' + useOptions.limit + '个');
}

// 初始化执行
function init() {
  onPreviewDestroy();  // 清除预览缓存
  fileList.value = other.getDefaultValue(props.modelValue); // 获取文件列表
  saveFileList = other.getValue(fileList.value, useOptions); // 绑定值处理
  emit('update:modelValue', saveFileList);
}

</script>

<style lang="scss">
@import "./index.scss";
</style>
