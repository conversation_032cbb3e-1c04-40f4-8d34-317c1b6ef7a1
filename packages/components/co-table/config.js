// export const VERSION = '1.0.0';
const defaultConfig = {
	metaPermisKey: 'perms',
	search: {
		style: null,
		search: { type: 'primary', icon: 'Search', name: '搜索' },
		reset: { type: 'default', icon: 'Refresh', name: '重置' },
	},
	loading: true,
	attrs: {
		'header-cell-style': { backgroundColor: '#f5f7fa', color: '#303133' },
	}, // element表格属性
	getDic: null,
	dicKeys: ['label', 'value'],
	page: {
		request: {
			current: 'current',
			size: 'size',
		},
		response: {
			current: 'current',
			pages: 'pages',
			size: 'size',
			total: 'total',
			records: 'list',
		},
	},
	upload: null,
	downFile: null,
};
export const dateType = ['time', 'timeselect', 'year', 'years', 'month', 'dates', 'date', 'week', 'months', 'datetime', 'datetimerange', 'daterange', 'monthrange', 'yearrange'];
export default defaultConfig;
