<template>
	<el-form ref="searchFormRef" :model="model" v-bind="$attrs" :inline="$attrs.inline || true" :size="$attrs.size || 'default'" :validate-on-rule-change="false" :[styleName]="_styles">
		<template v-for="item in config.items">
			<el-form-item v-if="!item.hidden" :key="item.prop" :prop="item.prop" v-bind="item.attrs">
				<slot :name="$attrs.scene ? `search_${item.prop}` : item.prop" :item="item" :data="model">
					<component :is="'co-' + getType(item)" :item="item" :data="model" :dic="dic" @change="onItemChange" />
				</slot>
			</el-form-item>
		</template>
		<el-form-item v-if="config.items">
			<slot :name="$attrs.scene ? 'search_operation' : 'operation'" :handle="onHandle">
				<el-button v-bind="searchProps" @click="onHandle('search')">{{ searchProps.name }}</el-button>
				<el-button v-bind="resetProps" @click="onHandle('reset')">{{ resetProps.name }}</el-button>
			</slot>
		</el-form-item>
	</el-form>
</template>
<script>
import defaultConfig, { dateType } from './config.js';
import coFormItem from './components/co-form-item/index.js';
import Utils from './utils';
export default {
	name: 'CoSearch',
	components: {
		...coFormItem,
	},
	inheritAttrs: false,
	props: {
		model: {
			type: Object,
			default: () => ({}),
		},
		config: {
			type: Object,
			default: () => {},
		},
		dic: {
			type: Object,
			default: () => null,
		},
	},
	data() {
		return {
			formModel: {},
			widgetItem: {},
		};
	},
	provide() {
		return {
			widgetItem: this.widgetItem,
		};
	},
	created() {
		const {
			config: { styles },
		} = this;
		this._styles = styles || defaultConfig.search.style;
		this.styleName = typeof this._styles === 'string' ? 'class' : 'style';
		this.oldModel = { ...this.model };
		this.searchProps = defaultConfig.search.search;
		this.resetProps = defaultConfig.search.reset;
	},
	mounted() {
		this.searchFormRef = this.$refs.searchFormRef;
	},
	methods: {
		getType(item) {
			if (dateType.includes(item.type)) {
				return 'date';
			}
			return item.type;
		},
		onItemChange({ prop, value }) {
			this.model[prop] = value;
			this.$attrs.scene && this.$emit('update:model', this.model), this.$emit('change', { prop, value });
		},
		onHandle(type = 'search') {
			const formModelArr = Object.keys(this.model);
			const { oldModel, widgetItem } = this;
			const searchResult = {};
			for (const widget of formModelArr) {
				// 重置
				if (type === 'reset') {
					const setVal = oldModel ? oldModel[widget] : '';
					if (widgetItem[widget]) {
						widgetItem[widget].resetField(setVal);
					}
					this.model[widget] = setVal;
				} else {
					// 搜索
					const hasPrepend = widget.includes('_prepend'); // 判断是否包含约定的_prepend
					// 如果有_prepend
					if (hasPrepend) {
						// 当前prepend有值
						if (this.model[widget]) {
							const mainProp = widget.replace(/\_prepend$/, '');
							// 如果是时间类型组件
							if (widgetItem[mainProp]?.isDateType) {
								// if (this.model[mainProp]) {
								// 是否有分割字段
								const splitProp = widgetItem[mainProp]?.item.splitProp;
								// 有分割 将分割字段与item.prop进行拼接，将选择的时间一一赋值，(当有splitProp时 选择时间默认为数组格式)
								if (splitProp && Utils.getType(splitProp) === 'Array') {
									if (this.model[mainProp]?.length) {
										searchResult[`${this.model[widget]}${splitProp[0]}`] = this.model[mainProp][0] ?? '';
										searchResult[`${this.model[widget]}${splitProp[1]}`] = this.model[mainProp][1] ?? '';
									}
								} else {
									//  没有分割 按照选择的prepend字段进行赋值
									searchResult[this.model[widget]] = this.model[mainProp].join(splitProp || ',');
								}
								// }
							} else {
								this.model[mainProp] && (searchResult[this.model[widget]] = this.model[mainProp]);
							}
						} else {
							delete searchResult[widgetItem[widget].cacheKey];
							widgetItem[widget].cacheKey = undefined;
						}
					} else if (!widgetItem[widget]?.item.prepend || !widgetItem[widget]?.item.prepend.prop.includes('_prepend')) {
						searchResult[widget] = this.model[widget];
					}
				}
			}
			// this.$attrs.scene && this.$emit('update:model', this.model);
			this.$emit('search', searchResult, type);
		},
		setData(data) {
			const formModel = reactive(this.model);
			const widgetItem = this.widgetItem;
			const formModelArr = Object.keys(formModel);
			for (const [key, value] of Object.entries(data)) {
				if (widgetItem[key]) {
					widgetItem[key].resetField(value);
				}
				if (formModelArr.includes(key)) {
					formModel[key] = value || '';
				} else {
					formModel[key] = value || '';
				}
			}
		},
	},
};
</script>

<style lang="scss">
.el-form--inline {
	.el-form-item {
		.el-input,
		.el-cascader,
		.el-select,
		.el-autocomplete {
			width: 220px;
		}
		.el-date-editor {
			width: 360px;
			&.el-date-editor--datetimerange {
				width: 380px;
			}
			&.el-date-editor--monthrange,
			&.el-date-editor--yearrange {
				width: 270px;
			}
		}
		.el-input-group__prepend,
		.el-input-group__append {
			.el-select {
				width: 120px;
			}
		}
		.el-input:has(.el-input-group__prepend, .el-input-group__append) {
			width: 320px;
		}
		.el-input.el-input-group--append.el-input-group--prepend {
			width: 400px;
		}
	}
}
</style>
