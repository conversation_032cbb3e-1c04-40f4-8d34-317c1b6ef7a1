<template>
	<el-select v-model="formModel[item.prop]" clearable :placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" v-bind="itemAttrs" v-on="listeners">
		<el-option v-for="opt in optionList" :key="opt[valueKey]" :label="opt[labelKey]" :value="opt[valueKey]" :disabled="itemAttrs.disabled" />
	</el-select>
</template>
<script>
import Utils from '../../utils';
import defaultConfig from '../../config';
import { handleFn } from './common.js';
import { reactive } from 'vue';
export default {
	name: 'CoSelect',
	inheritAttrs: false,
	props: {
		slotName: {
			type: String,
			default: '',
		},
		dic: {
			type: Object,
			default: () => null,
		},
	},
	data() {
		return {
			listeners: {},
			labelKey: defaultConfig.dicKeys[0],
			valueKey: defaultConfig.dicKeys[1],
			optionList: [],
			formModel: {},
		};
	},
	inject: ['widgetItem'],
	created() {
		// mainProp:为主要的prop，不是prepend的prop 用于给下拉的key赋值input输入的value
		const {
			$attrs: { data = null, item, row = data, scene, mainProp = '' },
			listeners,
		} = this;
		this.item = item;
		const itemAttrs = (this.itemAttrs = Object.assign(item.attrs || {}));
		this._inTable = scene === 'inTable';
		this.formModel = reactive(row);
		mainProp && (item.prop = `${mainProp}_prepend`);
		if (!this.formModel[item.prop]) {
			this.formModel[item.prop] = itemAttrs.multiple ? [] : '';
		}
		if (itemAttrs.multiple) {
			const itemProp = row[item.prop];
			if (['String', 'Number'].includes(Utils.getType(itemProp))) {
				this.formModel[item.prop] = itemProp.toString().split(',').map(Number);
			}
		}
		this.widgetItem[item.prop] = this;
		const { optionKey, option, attrs, filter } = item;
		const optionVal = option || attrs.option;
		this.filterOption = (options) => (typeof filter === 'function' ? filter(options) : options);
		if (optionKey) [this.labelKey, this.valueKey] = optionKey;
		// 监听 dic
		const watcher = this.$watch(
			() => this.dic,
			(val) => {
				if (val && val[optionVal]) {
					this.initOption(item, optionKey, val[optionVal]);
					watcher();
				}
			},
			{ deep: true }
		);
		this.initOption(item, optionKey, optionVal);

		this.initEvent({ events: item.events, itemAttrs, $attrs: this.$attrs, listeners });
	},
	methods: {
		initEvent({ events, itemAttrs, $attrs, listeners }) {
			listeners['change'] = (value) => handleFn.call(this, 'change', value, this._inTable, $attrs, itemAttrs);
			if (events) {
				for (const evName of Object.keys(events)) {
					this._inTable
						? (listeners[evName] = () => handleFn.call(this, evName, $attrs.row[this.item.prop], this._inTable, $attrs, itemAttrs))
						: (listeners[evName] = () => handleFn.call(this, evName, $attrs.data[$attrs.item.prop], false, $attrs, itemAttrs));
				}
			}
		},
		initOption(item, optionKey, optionVal) {
			const optionValueType = Utils.getType(optionVal);
			// 根据自定义optionKey参数设置labelKey,valueKey
			if (optionValueType === 'Array') {
				if (optionKey) [this.labelKey, this.valueKey] = optionKey;
				this.optionList = this.filterOption(optionVal);
				return;
			}
			if (optionValueType === 'Function') {
				const result = optionVal();
				if (Utils.getType(result) === 'Promise') {
					result.then((data) => {
						this.optionList = this.filterOption(data);
					});
				} else {
					this.optionList = result;
				}
				return;
			}
			if (optionValueType === 'AsyncFunction') {
				optionVal().then((data) => (this.optionList = this.filterOption(data)));
				return;
			}
			if (optionValueType === 'Promise') {
				optionVal.then((data) => (this.optionList = data));
				return;
			}
			// option 当为.json 字典值时
			if (optionValueType === 'String' && optionVal.substr(optionVal.length - 5, 5) === '.json') {
				const pval = optionVal.replace(/\.json$/, '');
				defaultConfig.getDic(pval).then((resDic) => {
					this.optionList = this.filterOption(resDic);
				});
			}
		},
		resetField(value = undefined) {
			this.formModel[this.item.prop] = value;
		},
	},
};
</script>
<style>
:root {
	--el-input-border-radius: 4px;
}
.el-input-group--prepend .el-input__wrapper {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}
</style>
