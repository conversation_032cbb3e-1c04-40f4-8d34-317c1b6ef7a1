<template>
	<el-button :class="item.classNew" v-bind="item" @click="onClick">
		<slot>{{ item.name }}</slot>
	</el-button>
</template>
<script>
export default {
	name: 'CoButton',
	inheritAttrs: false,
	created() {
		const { item } = this.$attrs;
		item['classNew'] = item['className'];
		delete item['className'];
		this.item = item;
		item.tableKey && delete item.tableKey;
		delete item.rule;
		delete item.attributes;
	},
	methods: {
		onClick(e) {
			if (this.$attrs['dis-click']) return;
			e.stopPropagation();
			this.$emit('click');
		},
	},
};
</script>
