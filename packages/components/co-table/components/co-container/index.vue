<template>
	<el-form v-if="hasFormItem" class="zs-table-content" ref="tFormRef" :model="model" :size="configOpts.size" :validate-on-rule-change="false">
		<slot />
	</el-form>
	<template v-else>
		<slot />
	</template>
</template>
<script>
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'CoContainer',
	inheritAttrs: false,
	props: {
		model: Object,
		hasFormItem: Boolean,
		configOpts: Object,
	},
	methods: {
		formRef() {
			return this.$refs.tFormRef;
		},
	},
});
</script>
