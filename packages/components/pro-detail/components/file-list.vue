<template>
	<div v-if="isOperation">
		<co-table :data="pageConfig.tableData" :config="pageConfig.tableConfig" :header="pageConfig.tableHeader" align="left">
			<template #operation="{ row }">
				<el-button icon="view" link v-if="showBtn('preview')" type="primary" @click="viewHandler(row)"> 预览 </el-button>
				<el-button icon="download" link v-if="showBtn('download')" type="primary" @click="downHanlder(row)">下载</el-button>
				<!-- <el-button icon="delete" link v-if="showBtn('remove')" type="primary" @click="removeHandler(row)">删除</el-button> -->
			</template>
		</co-table>
		<co-preview v-model="pageConfig.previewUrl" width="80%" :title="pageConfig.previewTitle" :layer="true" />
	</div>
</template>

<script setup>
import { getFiles, downLoadFileByUrl } from '/@/api/common/upload.js';
import { useMessageBox } from '/@/hooks/message';
defineOptions({
	name: 'FileList',
});
const props = defineProps({
	fileListId: {
		type: String,
		default: '',
	},
	oper: {
		type: Object,
		default: () => ({}),
	},
	isOperation: {
		type: Boolean,
		default: true,
	},
});
const pageConfig = reactive({
	previewUrl: '',
	previewTitle: '',
	tableConfig: {
		pagination: false,
	},
	tableData: [],
	tableHeader: [
		{ prop: 'fileName', label: '文件名称' },
		{ prop: 'sizeText', label: '文件大小' },
		{ prop: 'uploadTime', label: '上传时间' },
		{ prop: 'insertPersonName', label: '上传人' },
		{ prop: 'operation', label: '操作' },
	],
});
watch(
	() => props.fileListId,
	(newVal) => {
		if (newVal) {
			getFiles(newVal.split(',')).then(({ data }) => {
				data.forEach((item) => {
					item.sizeText = (item.fileSize / 1024).toFixed(2) + 'kB';
				});
				pageConfig.tableData = data;
			});
		}
	}
);

function showBtn(type) {
	return !!props.oper[type];
}

// 预览
function viewHandler(row) {
	pageConfig.previewUrl = row.fileUrl;
	pageConfig.previewTitle = row.fileName;
}
// 下载
function downHanlder(row) {
	// console.log('row', row);
	downLoadFileByUrl({ url: row.fileUrl, name: row.fileName });
}
// 删除
function removeHandler() {
	useMessageBox()
		.confirm('是否删除该文件', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
		.then(() => {});
}
</script>

<style lang="scss" scoped></style>
