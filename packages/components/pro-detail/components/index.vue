<template>
	<div>
		<v-ele-table :table-data="tableData" />
	</div>
</template>
<script>
export default {
	name: 'AuditRecords',
	props: {
		list: {
			type: Array,
			default: () => {
				return []
			}
		}
	},
	data() {
		return {
			tableData: {
				// operation:this.isOperation && {
				//   buttonToSet:[
				//     { mark:'remove',text:'删除',rule:'true',className:'co-text-red' }
				//   ]
				// },
				header: [
					// { field:'fileName',name:'操作步骤' },
					{ field: 'applyPersonName', name: '审核人' },
					{ field: 'applyTime', name: '申请时间', type: 'date' }
					// { field:'insertPersonName',name:'办理时间',type:'date' },
					// { field:'applyDesc',name:'办理结果及意见',type:'date' },
				],
				dataList: []
			}
		}
	},
	watch: {
		list: {
			handler(val) {
				this.tableData.dataList = val
			},
			immediate: true,
			deep: true
		}
	}
}
</script>
