<template>
  <div class="pro-detail">
    <el-collapse v-model="activeNames">
      <div class="page" v-for="(item, index) in descriptionsItem" :key="index">
        <el-collapse-item :disabled="item.disabled" :name="item.title">
          <template v-slot:title v-if="item.title">
            <slot name="title">
              <div class="title-content">
                <span
                  class="title"
                  :class="{ 'title-block': !item.isHiddenTitleBlock }"
                  :style="{ color: item.fontColor || '' }"
                  >{{ item.title }}</span
                >
                <span class="title-desc" v-if="item.titleDescType">
                  <span v-if="item.titleDescType == 'desc'"></span>
                  <slot
                    v-else-if="item.titleDescType == 'customDec'"
                    :name="'customDec' + item.titleDescKey"
                    :data="formData"
                  />
                </span>
              </div>
            </slot>
          </template>
          <el-row>
            <template v-if="item.type">
              <el-col
                v-for="chil in item.children"
                :span="chil.span || 24 / (item.column || 2)"
                :key="chil.field"
                :class="{ 'col-desc-border': border }"
              >
                <!-- 描述列表 -->
                <div v-if="item.type == 'descrip'" class="descrip-item">
                  <span
                    class="desc-span"
                    :style="{
                      width: (item.labelWidth || chil.labelWidth || 140) + 'px',
                      textAlign: item.labelAlign || chil.labelAlign,
                    }"
                  >
                    <slot :name="`${chil.field}_label`" :label="chil.title">{{ chil.title }}</slot>
                  </span>
                  <div class="desc-content">
                    <slot :name="chil.field" :row="formData" :field="chil.field">
                      <!-- 通过字典或接口获取数据-->
                      <div v-if="chil.dicId || chil.interfaceName">
                        <!--自动处理多选的情况-->
                        <div v-if="isMultiSelect(formData[chil.field])">
                          <el-tag class="mr-2" v-for="item in formData[chil.field].split(',')">{{
                            (chil.option && chil.option[item]) || "-"
                          }}</el-tag>
                        </div>
                        <span v-else>
                          <el-tag v-if="chil.tag">{{
                            (chil.option && chil.option[formData[chil.field]]) || "-"
                          }}</el-tag>
                          <span v-else>{{ (chil.option && chil.option[formData[chil.field]]) || "-" }}</span>
                        </span>
                      </div>
                      <!-- 通过option获取名称-->
                      <div v-else-if="chil.option && chil.option.length">
                        {{ getName(chil) || "-" }}
                      </div>
                      <!-- 接口返回true和false，展示是、否-->
                      <div v-else-if="formData[chil.field] === true || formData[chil.field] === false">
                        {{ formData[chil.field] ? "是" : "否" }}
                      </div>
                      <!-- 富文本类展示-->
                      <div v-else-if="chil.type == 'html'" v-html="formData[chil.field]" />
                      <!-- 图片文件展示-->
                      <div v-else-if="chil.img === true">
                        <div v-if="chil.fileError" class="text-red-500">{{ chil.fileError }}</div>
                        <div v-else-if="chil.fileData && chil.fileData.length > 0" class="flex flex-wrap gap-2">
                          <div v-for="(file, index) in chil.fileData" :key="index" class="relative">
                            <el-image
                              :src="file.fileUrl"
                              :alt="file.fileName"
                              fit="cover"
                              class="w-[160px] rounded-lg border border-gray-200 cursor-pointer hover:shadow-md transition-shadow "
                              :preview-src-list="chil.fileData.map(f => f.fileUrl)"
                              :initial-index="index"
                            />
                            <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg truncate">
                              {{ file.fileName }}
                            </div>
                          </div>
                        </div>
                        <div v-else class="text-gray-500">暂无图片</div>
                      </div>
                      <!-- 视频文件展示-->
                      <div v-else-if="chil.video === true">
                        <div v-if="chil.fileError" class="text-red-500">{{ chil.fileError }}</div>
                        <div v-else-if="chil.fileData && chil.fileData.length > 0" class="space-y-2">
                          <div v-for="(file, index) in chil.fileData" :key="index"
                               class=" p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex items-center space-x-3">
                              <div>
                                <div class="font-medium text-gray-900">{{ file.fileName }}</div>
                                <div class="text-sm text-gray-500">
                                  {{ formatFileSize(file.fileSize) }}
                                  <span v-if="file.uploadTime" class="ml-2">{{ file.uploadTime }}</span>
                                </div>
                              </div>
                            </div>
                            <div class="flex space-x-2">
                              <el-button
                                type="primary"
                                icon="VideoPlay"
                                size="small"
                                @click="previewVideo(file)"
                              >
                                预览
                              </el-button>
                              <el-button
                                icon="Download"
                                size="small"
                                @click="downloadVideoFile(file)"
                              >
                                下载
                              </el-button>
                            </div>
                          </div>
                        </div>
                        <div v-else class="text-gray-500">暂无视频</div>
                      </div>
                      <!-- 范围类展示,例如：1 ~ 5 %-->
                      <div v-else-if="chil.rightField">
                        <div>
                          {{ formatRangeValue(formData[chil.field], formData[chil.rightField], chil.dw) }}
                        </div>
                      </div>
                      <!-- 普通文本类展示-->
                      <div v-else>
                        {{ isValueEmpty(formData[chil.field]) ? "-" : formData[chil.field] }}
                        <span v-if="chil.dw && !isValueEmpty(formData[chil.field])"> {{ chil.dw }}</span>
                      </div>
                    </slot>
                  </div>
                </div>
                <!-- 文件列表 -->
                <file-list
                  v-else-if="item.type == 'fileList'"
                  :is-user-name="item.isUserName"
                  :file-list-id="item.fileIds || formData[item.fileIdsKey]"
                  :is-operation="item.isOperation"
                  :oper="item.oper"
                />
                <!-- 审核记录 -->
                <audit-records
                  v-else-if="item.type == 'records'"
                  :list="item.list"
                  :audit-id="auditId"
                  :bus-key="busKeyPar"
                />
              </el-col>
              <slot :name="item.key" :data="formData">
                <el-col :span="24" v-if="item.type == 'customDec'"></el-col>
              </slot>
            </template>
          </el-row>
        </el-collapse-item>
      </div>
      <div class="flex justify-center align-center pt-4 pb-4" v-if="$slots.footer"><slot name="footer"></slot></div>
    </el-collapse>

    <!-- 视频预览组件 -->
    <pro-Video-Preview
      v-model:visible="videoPreviewVisible"
      :video-data="currentVideoData"
      @download="onVideoDownload"
    />
  </div>
</template>
<script>
import FileList from "./components/file-list.vue";
import proVideoPreview from "@components/pro-video-preview/index.vue";

const auditRecords = defineAsyncComponent(() => import("./components/index.vue"));
import { getDictionary } from "/@/api/common/dictionary";
import { formatDic, getFormatDic } from "/@/utils/zszc";
import { deepClone, isDeepEqual } from "/@/utils/other";
import { getFiles } from "/@/api/common/upload";
import { VideoPlay } from '@element-plus/icons-vue';

export default {
  name: "ProDetail",
  components: {
    auditRecords,
    FileList,
    proVideoPreview,
    VideoPlay,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    configData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    recordId: {
      type: String,
      default: "",
    },
    busKey: {
      type: String,
      default: "",
    },
    border: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      list: [],
      auditId: "",
      busKeyPar: "",
      activeNames: [],
      // 字典加载状态管理（改为普通对象，确保响应式）
      dictLoadingStates: {},
      // 字典数据缓存
      dictCache: {},
      // 上一次的 configData，用于深度比较
      previousConfigData: null,
      // 视频预览相关数据
      videoPreviewVisible: false,
      currentVideoData: {},
      // 文件数据缓存（包括图片和视频）
      fileDataCache: {},
      // 文件加载状态管理
      fileLoadingStates: {},
      // 上一次的文件字段值，用于变化检测
      previousFileFieldValues: {},
    };
  },
  computed: {
    formData() {
      return this.data || this.configData.formData;
    },
    descriptionsItem() {
      // 直接过滤并返回，保持最大程度的响应式连接
      return this.list
        .filter((item) => !item.isHidden)
        .map((item) => {
          return {
            ...item,
            children: item.children?.filter((chil) => chil.isHidden != true) || null,
          };
        });
    },
  },
  watch: {
    configData: {
      handler(val) {
        // 深度比较新旧值，避免重复触发
        if (this.previousConfigData && isDeepEqual(val, this.previousConfigData)) {
          return;
        }

        // 保存当前值用于下次比较
        this.previousConfigData = deepClone(val);
        // 深拷贝 list 数据并预先初始化 option 属性
        this.list = val.list
          ? deepClone(val.list).map((item) => {
              if (item.children) {
                item.children = item.children.map((child) => {
                  // 为有 dicId 或 interfaceName 的字段预先初始化空的 option 属性，确保响应式追踪
                  if ((child.dicId || child.interfaceName) && !child.option) {
                    child.option = {};
                  }
                  return child;
                });
              }
              return item;
            })
          : [];
        this.activeNames =
          this.list &&
          this.list.map((i) => {
            return i.title;
          });
        this.$nextTick(() => {
          this.loadAllData();
        });
      },
      immediate: true,
      deep: true,
    },
    busKey(val) {
      this.busKeyPar = val;
    },
    // 监听 formData 变化，确保在数据准备好后加载文件
    formData: {
      handler(newFormData, oldFormData) {
        // 如果 formData 从空变为有数据，或者文件相关字段发生变化，重新加载文件
        if (newFormData && Object.keys(newFormData).length > 0) {
          // 检测文件字段是否发生变化
          const changedFileFields = this.detectFileFieldChanges(newFormData, oldFormData);

          if (changedFileFields.length > 0) {
            // 延迟执行，确保 DOM 更新完成
            this.$nextTick(() => {
              this.loadFileDataForFields(changedFileFields);
            });
          }
        }
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    isMultiSelect(item) {
      item += "";
      return item ? item.includes(",") : false;
    },
    // 判断值是否为空（null、undefined、空字符串），但不包括0
    isValueEmpty(value) {
      return value === null || value === undefined || value === "";
    },
    // 格式化范围值显示
    formatRangeValue(startValue, endValue, unit = "") {
      let existStartValue = startValue !== null && startValue !== "" && startValue !== undefined;
      let existEndValue = endValue !== null && endValue !== "" && endValue !== undefined;
      // 两个值都存在
      if (existStartValue && existEndValue) {
        return `${startValue} ~ ${endValue} ${unit}`;
      }
      // 只有起始值存在
      else if (existStartValue) {
        return `${startValue} ${unit}`;
      }
      // 只有结束值存在
      else if (existEndValue) {
        return `${endValue} ${unit}`;
      }
      // 两个值都不存在
      else {
        return "-";
      }
    },
    // 通过option获取名称
    getName(data) {
      return data.option.filter((item) => "" + item.value === "" + this.formData[data.field])[0]?.label;
    },
    // 加载所有需要的数据（字典和接口）
    loadAllData() {
      const dictRequests = new Set();
      const interfaceRequests = new Map(); // 使用Map存储接口函数和对应的字段信息

      // 遍历list找出所有需要加载的字段
      this.list.forEach((listItem) => {
        if (listItem.children) {
          listItem.children.forEach((child) => {
            if (child.dicId) {
              dictRequests.add(child.dicId);
            }
            if (child.interfaceName) {
              // 使用函数的字符串表示作为键，避免重复调用相同的接口
              const interfaceKey = child.interfaceName.toString() + "_" + child.field;
              interfaceRequests.set(interfaceKey, {
                field: child.field,
                interfaceName: child.interfaceName,
              });
            }
          });
        }
      });

      // 为每个字典ID发起请求
      dictRequests.forEach((dicId) => {
        this.loadDictionary(dicId);
      });

      // 为每个接口发起请求
      interfaceRequests.forEach((fieldInfo) => {
        this.loadInterface(fieldInfo.interfaceName, fieldInfo.field);
      });

      // 文件数据由 formData 的 watch 来触发加载
    },
    // 专门加载文件数据的方法（保持向后兼容）
    loadFileData() {
      // 获取所有文件字段
      const allFileFields = this.getAllFileFields();
      this.loadFileDataForFields(allFileFields);
    },

    // 为指定字段加载文件数据
    loadFileDataForFields(targetFields = []) {
      if (targetFields.length === 0) {
        return;
      }

      const fileRequests = new Map();

      // 遍历list找出需要加载文件的字段
      this.list.forEach((listItem) => {
        if (listItem.children) {
          listItem.children.forEach((child) => {
            // 处理图片和视频文件配置，且字段在目标列表中
            if ((child.img === true || child.video === true) && targetFields.includes(child.field)) {
              const fileKey = child.field + "_" + (child.img ? "img" : "video");
              fileRequests.set(fileKey, {
                field: child.field,
                type: child.img ? "img" : "video",
                child: child,
              });
            }
          });
        }
      });

      // 为每个文件字段发起请求
      fileRequests.forEach((fieldInfo) => {
        this.loadFiles(fieldInfo.field, fieldInfo.type, fieldInfo.child);
      });
    },
    // 加载单个字典
    async loadDictionary(dicId) {
      if (this.dictCache[dicId]) {
        // 如果已经缓存，直接使用
        this.applyDictionaryData(dicId, this.dictCache[dicId], false, "dicId");
        return;
      }
      try {
        const res = await getDictionary(dicId);
        const optionData = getFormatDic(formatDic(res.dicList));
        // 缓存字典数据
        this.dictCache[dicId] = optionData;
        // 应用字典数据
        this.applyDictionaryData(dicId, optionData, false, "dicId");
      } catch (error) {
        console.error(`字典加载失败 [${dicId}]:`, error);
        // 设置错误状态
        this.applyDictionaryData(dicId, null, true, "dicId");
      }
    },
    // 加载单个接口数据
    async loadInterface(interfaceName, field) {
      // 使用函数的字符串表示和字段名作为缓存键
      const cacheKey = `${interfaceName.toString()}_${field}`;

      if (this.dictCache[cacheKey]) {
        // 如果已经缓存，直接使用
        this.applyDictionaryData(interfaceName, this.dictCache[cacheKey], false, "interfaceName");
        return;
      }
      try {
        // 调用外部传入的接口方法，传递 formData 和 field 参数
        const res = (await this.callInterface(interfaceName, this.formData, field)).data;
        const optionData = this.formatInterfaceData(res);
        // 缓存接口数据
        this.dictCache[cacheKey] = optionData;
        // 应用接口数据
        this.applyDictionaryData(interfaceName, optionData, false, "interfaceName");
      } catch (error) {
        console.error(`接口加载失败 [${interfaceName}]:`, error);
        // 设置错误状态
        this.applyDictionaryData(interfaceName, null, true, "interfaceName");
      }
    },
    // 调用外部传入的接口方法
    async callInterface(interfaceName, formData, field) {
      // 如果interfaceName是一个函数，直接调用
      if (typeof interfaceName === "function") {
        return await interfaceName(formData, field);
      }

      // 如果interfaceName是字符串，需要从配置中找到对应的函数
      // 这种情况下，interfaceName应该是在配置中定义的函数引用
      throw new Error(`接口方法必须是一个函数，当前类型: ${typeof interfaceName}`);
    },
    // 格式化接口返回数据
    formatInterfaceData(data) {
      // 处理返回格式为 [{label:'',value:''},...] 的数据
      if (!Array.isArray(data)) {
        console.warn("接口返回数据格式不正确，期望数组格式");
        return {};
      }
      const result = {};
      data.forEach((item) => {
        result[item.value || item.id] = item.label;
      });

      return result;
    },
    // 加载文件数据（图片和视频）
    async loadFiles(field, type, childConfig) {
      const fieldValue = this.formData[field];

      if (!fieldValue) {
        return;
      }

      // 构建缓存键和加载状态键
      const cacheKey = `${field}_${type}_${fieldValue}`;
      const loadingKey = `${field}_${type}`;

      // 检查是否正在加载中，防止重复请求
      if (this.fileLoadingStates[loadingKey]) {
        return;
      }

      if (this.fileDataCache[cacheKey]) {
        // 如果已经缓存，直接应用数据
        this.applyFileData(field, type, this.fileDataCache[cacheKey], false);
        return;
      }

      try {
        // 设置加载状态
        this.fileLoadingStates[loadingKey] = true;

        // 处理文件ID数组（支持逗号分隔的字符串或数组格式）
        let fileIds = [];
        if (typeof fieldValue === 'string') {
          fileIds = fieldValue.split(',').filter(id => id.trim());
        } else if (Array.isArray(fieldValue)) {
          fileIds = fieldValue;
        } else {
          fileIds = [fieldValue];
        }

        if (fileIds.length === 0) {
          return;
        }

        // 调用 getFiles 接口获取文件信息
        const res = await getFiles(fileIds);
        const fileData = res.data || [];

        // 缓存文件数据
        this.fileDataCache[cacheKey] = fileData;

        // 应用文件数据
        this.applyFileData(field, type, fileData, false);
      } catch (error) {
        console.error(`文件加载失败 [${field}]:`, error);
        // 设置错误状态
        this.applyFileData(field, type, null, true);
      } finally {
        // 清除加载状态
        this.fileLoadingStates[loadingKey] = false;
      }
    },
    // 应用文件数据到组件
    applyFileData(field, type, fileData, isError = false) {
      // 创建新的list副本
      const newList = deepClone(this.list);

      // 更新新副本中的数据
      newList.forEach((listItem) => {
        if (listItem.children) {
          listItem.children.forEach((child) => {
            const shouldUpdate = child.field === field && ((type === 'img' && child.img === true) || (type === 'video' && child.video === true));

            if (shouldUpdate) {
              if (isError) {
                child.fileData = null;
                child.fileError = '文件加载失败';
              } else {
                child.fileData = fileData;
                child.fileError = null;
              }
            }
          });
        }
      });

      // 替换整个list
      this.list = newList;
    },
    // 预览视频
    previewVideo(videoData) {
      this.currentVideoData = videoData;
      this.videoPreviewVisible = true;
    },
    // 处理视频下载
    onVideoDownload(videoData) {
      console.log('视频下载:', videoData);
    },
    // 下载视频文件
    downloadVideoFile(file) {
      if (!file.fileUrl) {
        this.$message.error('视频地址不存在');
        return;
      }

      try {
        // 创建下载链接
        const link = document.createElement('a');
        link.href = file.fileUrl;
        link.download = file.fileName || 'video.mp4';
        link.target = '_blank';

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$message.success('开始下载视频文件');
      } catch (error) {
        console.error('视频下载失败:', error);
        this.$message.error('视频下载失败，请重试');
      }
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '未知';

      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));

      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    },

    // 获取所有文件字段
    getAllFileFields() {
      const fileFields = [];
      this.list.forEach((listItem) => {
        if (listItem.children) {
          listItem.children.forEach((child) => {
            if (child.img === true || child.video === true) {
              fileFields.push(child.field);
            }
          });
        }
      });
      return fileFields;
    },

    // 检测文件字段变化
    detectFileFieldChanges(newFormData, oldFormData) {
      const changedFields = [];
      const fileFields = this.getAllFileFields();

      fileFields.forEach(field => {
        const newValue = newFormData ? newFormData[field] : null;
        const oldValue = oldFormData ? oldFormData[field] : null;
        const previousValue = this.previousFileFieldValues[field];

        // 检查值是否真正发生变化
        if (newValue !== previousValue) {
          changedFields.push(field);
          // 更新记录的值
          this.previousFileFieldValues[field] = newValue;
        }
      });

      return changedFields;
    },
    // 应用数据到组件（支持字典和接口数据）
    applyDictionaryData(identifier, optionData, isError = false, type = "dicId") {
      // 创建新的list副本
      const newList = deepClone(this.list);

      // 更新新副本中的数据
      newList.forEach((listItem) => {
        if (listItem.children) {
          listItem.children.forEach((child) => {
            // 根据类型匹配对应的字段
            const shouldUpdate =
              (type === "dicId" && child.dicId === identifier) ||
              (type === "interfaceName" && child.interfaceName === identifier);
            if (shouldUpdate) {
              if (isError) {
                child.option = { [this.formData[child.field]]: "数据加载失败" };
              } else {
                child.option = optionData;
              }
            }
          });
        }
      });

      // 替换整个list
      this.list = newList;
    },

    // 清理文件相关状态（在组件销毁时调用）
    clearFileStates() {
      this.fileLoadingStates = {};
      this.fileDataCache = {};
      this.previousFileFieldValues = {};
    },
  },
  // 组件销毁时清理状态
  beforeDestroy() {
    this.clearFileStates();
  },
};
</script>
<style lang="scss">
.pro-detail {
  .title-content {
    display: flex;
    align-items: center;
  }

  .title-desc {
    margin-left: 10px;
    font-size: 16px;
    font-weight: 600;
  }

  .title {
    font-size: 18px;
    font-weight: 900;
    display: flex;
    align-items: center;
  }

  .title-block::before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 22px;
    margin-right: 5px;
    @apply bg-primary;
  }

  .descrip-item {
    display: flex;
    align-items: center;

    .desc-span {
      flex-shrink: 0;
      padding: 8px 11px;
    }

    .desc-content {
      display: flex;
      align-items: center;
      padding: 6px 11px;
      flex: 1;
    }
  }

  .col-desc-border .descrip-item {
    border: 1px solid #ebeef5;
    border-bottom: none;
    border-right: none;
    height: 100%;

    .desc-span {
      display: flex;
      align-items: center;
      height: 100%;
      color: #606266;
      background-color: #f5f7fa;
      border-right: 1px solid #ebeef5;
      font-weight: bold;
    }
  }

  .el-row {
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
  }

  .el-collapse {
    border: 0;

    .page {
      margin: 15px 0 0 0;
      padding: 0 15px 30px 15px;
      background-color: #fff;
      border-radius: 8px;

      &:first-child {
        margin-top: 0;
      }
    }

    .el-collapse-item {
      .el-collapse-item__wrap,
      .el-collapse-item__header {
        border-bottom: 0;
      }
      .el-collapse-item__content {
        padding-bottom: 0;
      }
    }

    // 字典加载状态样式
    .dict-loading {
      color: #909399;
      font-style: italic;
    }
  }
}
</style>
