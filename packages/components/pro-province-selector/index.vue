<script setup>
/**
 * ProProvinceSelector - 省份选择器组件
 *
 * 功能特性：
 * 1. 支持单选和多选模式
 * 2. 智能数据格式检测和转换
 * 3. 多种数据格式支持（数组、字符串）
 * 4. 缓存机制优化性能
 * 5. 完整的事件回调支持
 *
 * 多选模式使用说明：
 * - 设置 multiple="true" 启用多选
 * - 多选数据格式（输出）：一维数组 ["市1", "市2"] （只保留最后一级的值）
 * - 字符串格式（输出）："市1,市2" （用逗号分隔最后一级的值）
 * - 地区名称格式（输出）："省1/市1,省2/市2" （字符串格式，用逗号分隔）
 * - 支持 multipleLimit 限制选择数量
 * - 内部使用二维数组与 Cascader 组件交互，对外输出简化格式
 *
 * 示例：
 * <pro-province-selector
 *   v-model="selectedRegions"
 *   :multiple="true"
 *   :multiple-limit="3"
 *   @region-change="handleRegionChange"
 * />
 */
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { getDicTionaryAndValue } from '/@/api/common/dic_response';
import { dictApi } from '/@/api/dict';
import { Loading } from '@element-plus/icons-vue';

// 定义Props
const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: ''
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: true
  },
  checkStrictly: {
    type: Boolean,
    default: true
  },
  validateEvent: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small'].includes(value)
  },
  dicId: {
    type: String,
    default: ''
  },
  // 支持多种属性名称变体，增强兼容性
  dictId: {
    type: String,
    default: ''
  },
  dictid: {
    type: String,
    default: ''
  },
  separator: {
    type: String,
    default: ' / '
  },
  showAllLevels: {
    type: Boolean,
    default: true
  },
  collapseTags: {
    type: Boolean,
    default: false
  },
  maxCollapseTags: {
    type: Number,
    default: 1
  },
  // 多选功能相关属性
  multiple: {
    type: Boolean,
    default: false
  },
  multipleLimit: {
    type: Number,
    default: 0 // 0 表示无限制
  },
  multipleSeparator: {
    type: String,
    default: ';' // 多选字符串格式的分隔符，用于分隔多个选择路径
  }
});

// 定义事件
const emit = defineEmits([
  'update:modelValue',
  'change',
  'region-change', // 新增：当地区选择变化时触发，返回值和地区名称
  'blur',
  'focus',
  'clear',
  'visible-change',
  'expand-change',
  'remove-tag'
]);

// 响应式数据
const loading = ref(false);
const error = ref('');
const registerAddress = ref([]);

// 记录原始输入格式，用于保持输出格式一致性
const originalFormat = ref('array'); // 'array' | 'string'

// 多选模式检测
const isMultipleMode = computed(() => {
  // 优先使用显式的 multiple 属性
  if (props.multiple !== undefined) {
    return props.multiple;
  }

  // 如果未显式设置，根据数据结构自动判断
  if (Array.isArray(props.modelValue)) {
    // 检查是否为二维数组（多选格式）
    if (props.modelValue.length > 0 && Array.isArray(props.modelValue[0])) {
      return true;
    }
    // 一维数组且长度大于1时，也可能是多选格式（新格式）
    // 这里需要更智能的判断，暂时保持原有逻辑
    return false;
  }

  // 字符串格式检测：包含逗号且不包含分号时，可能是多选的新格式
  if (typeof props.modelValue === 'string' && props.modelValue.includes(',') && !props.modelValue.includes('/')) {
    // 这里需要更精确的判断逻辑，暂时保持保守
    return false;
  }

  return false;
});

// 动态占位符文本
const dynamicPlaceholder = computed(() => {
  if (props.placeholder) {
    return props.placeholder;
  }

  if (isMultipleMode.value) {
    return props.multipleLimit > 0
      ? `请选择省份（最多${props.multipleLimit}个）`
      : '请选择省份（可多选）';
  }

  return '请选择省份';
});

// 智能处理字典ID属性，支持多种命名方式
const effectiveDicId = computed(() => {
  // 优先级：dicId > dictId > dictid
  const id = props.dicId || props.dictId || props.dictid || '';

  // 开发环境下的提示信息
  if (process.env.NODE_ENV === 'development') {
    const providedProps = [];
    if (props.dicId) providedProps.push('dicId');
    if (props.dictId) providedProps.push('dictId');
    if (props.dictid) providedProps.push('dictid');

    if (providedProps.length > 1) {
      console.warn(`[ProProvinceSelector] 检测到多个字典ID属性: ${providedProps.join(', ')}。建议使用 'dic-id' (kebab-case) 或 'dicId' (camelCase)。当前使用: ${id}`);
    }

    if (id && !props.dicId && (props.dictId || props.dictid)) {
      console.info(`[ProProvinceSelector] 建议使用 'dic-id' 属性替代 '${providedProps[0]}'，以保持命名一致性。`);
    }
  }

  return id;
});

// 缓存管理
const cache = new Map();
const CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

// Cascader配置
const cascaderProps = computed(() => {
  const config = {
    checkStrictly: props.checkStrictly,
    value: 'dicValue',
    label: 'dicName',
    children: 'children',
    emitPath: !props.checkStrictly,
    multiple: isMultipleMode.value, // 根据多选模式动态配置
  };

  // 多选模式下的额外配置
  if (isMultipleMode.value && props.multipleLimit > 0) {
    // Element Plus Cascader 本身不直接支持 multipleLimit
    // 我们将在事件处理中实现限制逻辑
  }

  return config;
});

// 计算属性：双向绑定的值
const currentValue = computed({
  get: () => {
    // 多选模式处理
    if (isMultipleMode.value) {
      if (Array.isArray(props.modelValue)) {
        originalFormat.value = 'array';
        // 检查是否为二维数组格式
        if (props.modelValue.length > 0 && Array.isArray(props.modelValue[0])) {
          return props.modelValue;
        }
        // 如果是一维数组（新格式），需要转换为 cascader 需要的格式
        // 这里我们假设一维数组中的每个值都是完整的路径值，需要根据实际数据结构来构建路径
        if (props.modelValue.length > 0) {
          // 将一维数组中的每个值转换为对应的完整路径
          return props.modelValue.map(value => {
            // 这里需要根据实际的数据结构来查找完整路径
            const path = findPathByValue(value, registerAddress.value);
            return path || [value];
          });
        }
        return [];
      }
      if (typeof props.modelValue === 'string' && props.modelValue) {
        originalFormat.value = 'string';
        // 多选字符串格式：逗号分隔的最后一级值
        const values = props.modelValue.split(',').filter(Boolean);
        return values.map(value => {
          // 查找每个值对应的完整路径
          const path = findPathByValue(value, registerAddress.value);
          return path || [value];
        });
      }
      return [];
    }

    // 单选模式处理（原有逻辑）
    if (Array.isArray(props.modelValue)) {
      originalFormat.value = 'array';
      return props.modelValue;
    }
    if (typeof props.modelValue === 'string') {
      originalFormat.value = 'string';
      if (props.modelValue) {
        // 如果是字符串，根据英文逗号分割
        return props.modelValue.split(',').filter(Boolean);
      }
    }
    // 默认情况，根据当前格式决定
    return [];
  },
  set: (value) => {
    // 多选模式处理
    if (isMultipleMode.value) {
      let outputValue;

      if (originalFormat.value === 'string') {
        // 多选字符串格式：将选择的最后一级值用逗号连接
        if (Array.isArray(value) && value.length > 0) {
          const lastLevelValues = value.map(path => {
            if (Array.isArray(path) && path.length > 0) {
              return path[path.length - 1]; // 只取最后一级的值
            }
            return path;
          }).filter(Boolean);
          outputValue = lastLevelValues.join(',');
        } else {
          outputValue = '';
        }
      } else {
        // 多选数组格式：返回一维数组，只包含最后一级的值
        if (Array.isArray(value) && value.length > 0) {
          outputValue = value.map(path => {
            if (Array.isArray(path) && path.length > 0) {
              return path[path.length - 1]; // 只取最后一级的值
            }
            return path;
          }).filter(Boolean);
        } else {
          outputValue = [];
        }
      }

      emit('update:modelValue', outputValue);
      if (props.validateEvent) {
        nextTick(() => {
          emit('change', outputValue);
        });
      }
      return;
    }

    // 单选模式处理（原有逻辑）
    let outputValue;

    if (originalFormat.value === 'string') {
      // 如果原始格式是字符串，返回逗号分割的字符串
      outputValue = Array.isArray(value) ? value.join(',') : (value || '');
    } else {
      // 如果原始格式是数组，返回数组
      outputValue = Array.isArray(value) ? value : (value ? [value] : []);
    }

    emit('update:modelValue', outputValue);
    if (props.validateEvent) {
      nextTick(() => {
        emit('change', outputValue);
      });
    }
  }
});

// 工具函数：格式化省份数据
const formatProvinceData = (data) => {
  if (!data) return [];

  // 如果是数组，直接处理
  if (Array.isArray(data)) {
    return data.map(formatSingleOption);
  }

  // 如果是对象，尝试提取数据
  if (typeof data === 'object') {
    const candidates = [data.data, data.dicList, data.children, data.list, data];

    for (const candidate of candidates) {
      if (Array.isArray(candidate)) {
        return candidate.map(formatSingleOption);
      }
    }
  }

  return [];
};

// 格式化单个选项
const formatSingleOption = (option) => {
  if (!option || typeof option !== 'object') {
    return {
      dicValue: String(option || ''),
      dicName: String(option || '')
    };
  }

  const dicValue = option.dicValue || option.value || option.code || option.id || '';
  const dicName = option.dicName || option.label || option.name || option.text || '';
  const children = option.children || option.child || option.subList || [];

  return {
    dicValue: String(dicValue),
    dicName: String(dicName),
    children: Array.isArray(children) ? children.map(formatSingleOption) : undefined,
    hot: option.hot,
    sort: option.sort,
    ...option
  };
};

// 缓存操作
const setCache = (key, data) => {
  cache.set(key, {
    data: JSON.parse(JSON.stringify(data)),
    timestamp: Date.now()
  });
};

const getCache = (key) => {
  const cached = cache.get(key);
  if (!cached) return null;

  if (Date.now() - cached.timestamp > CACHE_EXPIRE_TIME) {
    cache.delete(key);
    return null;
  }

  return JSON.parse(JSON.stringify(cached.data));
};

// 根据值获取地区名称的工具函数（支持单选和多选）
const getRegionNameByValue = (value, options = registerAddress.value) => {
  if (!value || !options || options.length === 0) return '';

  // 多选模式处理
  if (isMultipleMode.value) {
    if (Array.isArray(value) && value.length > 0 && Array.isArray(value[0])) {
      // 二维数组格式，处理多个选择路径
      const regionNames = value.map(path => getSingleRegionName(path, options));
      return regionNames.join(','); // 返回字符串格式，用逗号分隔
    }
    // 兼容一维数组格式
    return getSingleRegionName(value, options);
  }

  // 单选模式处理（原有逻辑）
  return getSingleRegionName(value, options);
};

// 根据最后一级的值查找完整路径
const findPathByValue = (targetValue, options = registerAddress.value, currentPath = []) => {
  if (!options || !Array.isArray(options)) return null;

  for (const option of options) {
    const currentValue = option.dicValue || option.value || option.code;
    const newPath = [...currentPath, currentValue];

    // 如果找到目标值，返回完整路径
    if (currentValue === targetValue) {
      return newPath;
    }

    // 递归查找子级
    const children = option.children || option.child || option.subList;
    if (children && children.length > 0) {
      const foundPath = findPathByValue(targetValue, children, newPath);
      if (foundPath) {
        return foundPath;
      }
    }
  }

  return null;
};

// 获取单个选择路径的地区名称
const getSingleRegionName = (value, options = registerAddress.value) => {
  if (!value || !options || options.length === 0) return '';

  const valueArray = Array.isArray(value) ? value : [value];
  const names = [];
  let currentOptions = options;

  // 遍历选中的值，逐级查找对应的名称
  for (const val of valueArray) {
    const found = currentOptions.find(option =>
      (option.dicValue && option.dicValue === val) ||
      (option.value && option.value === val) ||
      (option.code && option.code === val)
    );

    if (found) {
      // 获取名称字段
      const name = found.dicName || found.label || found.name || found.text || '';
      names.push(name);
      // 继续查找下一级
      currentOptions = found.children || found.child || found.subList || [];
    } else {
      break;
    }
  }

  // 用分隔符连接各级地区名称
  return names.join(props.separator);
};

// 获取省份数据
const getAddress = async () => {
  try {
    loading.value = true;
    error.value = '';

    // 使用智能处理后的字典ID或默认的registerAddres
    const dicId = effectiveDicId.value || '7113492989149185';
    const cacheKey = `province_data_${dicId}`;

    // 尝试从缓存获取数据
    const cachedData = getCache(cacheKey);
    if (cachedData && cachedData.length > 0) {
      registerAddress.value = cachedData;
      loading.value = false;
      return;
    }

    // 从API获取数据
    const res = await getDicTionaryAndValue(dicId);

    if (res) {
      // 使用工具函数格式化数据
      let formattedData = formatProvinceData(res);

      // 如果格式化后没有数据，尝试其他可能的数据结构
      if (formattedData.length === 0 && Array.isArray(res) && res.length > 0) {
        if (res[0]?.children) {
          formattedData = formatProvinceData(res[0].children);
        }
      }

      if (formattedData.length > 0) {
        registerAddress.value = formattedData;
        // 缓存数据
        setCache(cacheKey, formattedData);
      } else {
        registerAddress.value = [];
        error.value = '暂无省份数据';
      }
    } else {
      registerAddress.value = [];
      error.value = '暂无省份数据';
    }
  } catch (err) {
    console.error('获取省份数据失败:', err);
    error.value = '获取省份数据失败，请稍后重试';
    registerAddress.value = [];
  } finally {
    loading.value = false;
  }
};

// 事件处理函数
const handleChange = (value) => {
  // 多选限制检查
  if (isMultipleMode.value && props.multipleLimit > 0) {
    if (Array.isArray(value) && value.length > props.multipleLimit) {
      // 超出限制，截取前 multipleLimit 个选项
      value = value.slice(0, props.multipleLimit);

      // 可以在这里添加用户提示
      if (process.env.NODE_ENV === 'development') {
        console.warn(`[ProProvinceSelector] 选择数量超出限制，最多只能选择 ${props.multipleLimit} 个选项`);
      }
    }
  }

  currentValue.value = value;

  // 获取对应的地区名称
  const regionName = getRegionNameByValue(value);

  // 根据模式和原始格式准备输出值
  let outputValue;

  if (isMultipleMode.value) {
    // 多选模式
    if (originalFormat.value === 'string') {
      // 多选字符串格式：只保留最后一级的值，用逗号连接
      if (Array.isArray(value) && value.length > 0) {
        const lastLevelValues = value.map(path => {
          if (Array.isArray(path) && path.length > 0) {
            return path[path.length - 1]; // 只取最后一级的值
          }
          return path;
        }).filter(Boolean);
        outputValue = lastLevelValues.join(',');
      } else {
        outputValue = '';
      }
    } else {
      // 多选数组格式：返回一维数组，只包含最后一级的值
      if (Array.isArray(value) && value.length > 0) {
        outputValue = value.map(path => {
          if (Array.isArray(path) && path.length > 0) {
            return path[path.length - 1]; // 只取最后一级的值
          }
          return path;
        }).filter(Boolean);
      } else {
        outputValue = [];
      }
    }
  } else {
    // 单选模式（原有逻辑）
    if (originalFormat.value === 'string') {
      outputValue = Array.isArray(value) ? value.join(',') : (value || '');
    } else {
      outputValue = Array.isArray(value) ? value : (value ? [value] : []);
    }
  }

  // 触发新的region-change事件，返回值和地区名称
  emit('region-change', {
    value: outputValue,
    regionName: regionName,
    regionPath: Array.isArray(value) ? value : (value ? [value] : []),
    originalFormat: originalFormat.value,
    isMultiple: isMultipleMode.value
  });
};

const handleClear = () => {
  currentValue.value = [];

  // 触发原有的clear事件
  emit('clear');

  // 根据模式和原始格式准备空值
  const emptyValue = originalFormat.value === 'string' ? '' : [];
  const emptyRegionName = ''; // 统一返回空字符串

  // 触发region-change事件，传递空值
  emit('region-change', {
    value: emptyValue,
    regionName: emptyRegionName,
    regionPath: [],
    originalFormat: originalFormat.value,
    isMultiple: isMultipleMode.value
  });
};

const handleBlur = (event) => {
  emit('blur', event);
};

const handleFocus = (event) => {
  emit('focus', event);
};

const handleVisibleChange = (visible) => {
  emit('visible-change', visible);
};

const handleExpandChange = (value) => {
  emit('expand-change', value);
};

const handleRemoveTag = (value) => {
  emit('remove-tag', value);
};

// 监听字典ID变化，重新获取数据（支持所有属性变体）
watch(effectiveDicId, (newId, oldId) => {
  // 只有当字典ID真正发生变化且新ID不为空时才重新获取数据
  if (newId && newId !== oldId) {
    getAddress();
  }
}, { immediate: false });

// 组件挂载时获取数据
onMounted(() => {
  getAddress();
});

// 暴露组件方法
defineExpose({
  getAddress,
  loading,
  error,
});
</script>

<template>
  <div class="pro-province-selector">
    <el-cascader
      v-model="currentValue"
      :options="registerAddress"
      :props="cascaderProps"
      :disabled="disabled || loading"
      :placeholder="loading ? '加载中...' : dynamicPlaceholder"
      :clearable="clearable"
      :filterable="filterable"
      :size="size"
      :separator="separator"
      :show-all-levels="showAllLevels"
      :collapse-tags="collapseTags"
      :max-collapse-tags="maxCollapseTags"
      @change="handleChange"
      @clear="handleClear"
      @blur="handleBlur"
      @focus="handleFocus"
      @visible-change="handleVisibleChange"
      @expand-change="handleExpandChange"
      @remove-tag="handleRemoveTag"
    >
      <!-- 自定义节点内容插槽 -->
      <template #default="{ node, data }">
        <span>{{ data.dicName }}</span>
      </template>

      <!-- 空数据插槽 -->
      <template #empty>
        <div class="cascader-empty">
          <el-icon v-if="loading" class="is-loading">
            <Loading />
          </el-icon>
          <span v-else-if="error" class="error-text">{{ error }}</span>
          <span v-else>暂无数据</span>
        </div>
      </template>
    </el-cascader>
  </div>
</template>

<style scoped lang="scss">
.pro-province-selector {
  width: 100%;

  .cascader-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--el-text-color-secondary);

    .is-loading {
      animation: rotating 2s linear infinite;
    }

    .error-text {
      color: var(--el-color-danger);
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
