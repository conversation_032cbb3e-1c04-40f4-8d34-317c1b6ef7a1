# ProProvinceSelector JavaScript版本使用说明

这是省份选择器组件的JavaScript版本，不依赖TypeScript，可以直接在JavaScript项目中使用。

## 快速开始

### 1. 导入组件

```javascript
import ProProvinceSelector from '/@/components/pro-province-selector/index-js.vue';
```

### 2. 基础使用

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <ProProvinceSelector v-model="selectedProvince" />
    
    <!-- 带配置的用法 -->
    <ProProvinceSelector 
      v-model="regionCode"
      :check-strictly="true"
      placeholder="请选择地区"
      clearable
      @change="handleChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ProProvinceSelector from '/@/components/pro-province-selector/index-js.vue';

const selectedProvince = ref('');
const regionCode = ref([]);

const handleChange = (value) => {
  console.log('选择变化:', value);
};
</script>
```

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | String/Array | '' | 绑定值，支持 v-model |
| disabled | Boolean | false | 是否禁用 |
| placeholder | String | '请选择省份' | 占位符文本 |
| clearable | Boolean | true | 是否可清空 |
| filterable | Boolean | true | 是否可搜索 |
| checkStrictly | Boolean | true | 是否严格的遵守父子节点不互相关联 |
| validateEvent | Boolean | true | 是否触发表单校验 |
| size | String | 'default' | 尺寸：large/default/small |
| dicId | String | '' | 自定义字典ID（推荐使用 dic-id） |
| dictId | String | '' | 自定义字典ID（兼容属性） |
| dictid | String | '' | 自定义字典ID（兼容属性） |
| separator | String | ' / ' | 选项分隔符 |
| showAllLevels | Boolean | true | 是否显示完整路径 |
| collapseTags | Boolean | false | 多选时是否折叠Tag |
| maxCollapseTags | Number | 1 | 多选时最多显示的Tag数量 |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 绑定值变化时触发 | (value) |
| change | 选择器值变化时触发 | (value) |
| blur | 失去焦点时触发 | (event) |
| focus | 获得焦点时触发 | (event) |
| clear | 清空时触发 | () |
| visible-change | 下拉框出现/隐藏时触发 | (visible) |
| expand-change | 展开节点发生变化时触发 | (value) |
| remove-tag | 多选模式下移除Tag时触发 | (value) |

## 表单校验示例

```vue
<template>
  <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
    <el-form-item label="地区选择" prop="region">
      <ProProvinceSelector 
        v-model="form.region"
        placeholder="请选择地区"
      />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive } from 'vue';

const formRef = ref();
const form = reactive({
  region: []
});

const rules = {
  region: [
    { required: true, message: '请选择地区', trigger: 'change' }
  ]
};

const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      console.log('表单数据:', form);
    }
  });
};
</script>
```

## 在CMS站点表单中的使用

参考 `apps/web-platform/src/views/cms/site/form.vue` 的集成方式：

```vue
<template>
  <el-form-item label="地区">
    <ProProvinceSelector 
      v-model="formData.regionCode" 
      :check-strictly="datas.addressProps.checkStrictly"
      placeholder="请选择地区"
      clearable 
    />
  </el-form-item>
</template>

<script setup>
import ProProvinceSelector from '/@/components/pro-province-selector/index-js.vue';

// 数据处理保持原有逻辑
const submitForm = async () => {
  // 提交时将数组转换为字符串
  formData.value.regionCode = formData.value.regionCode.join('|');
  // ... 其他提交逻辑
};

// 编辑时将字符串转换为数组
const openDialog = async (row) => {
  if (row?.id) {
    getDetails(row.id).then((res) => {
      formData.value = res.data;
      formData.value.regionCode = res.data?.regionCode.split('|');
    });
  }
};
</script>
```

## 功能特性

- ✅ 支持 v-model 双向绑定
- ✅ 自动数据获取和缓存
- ✅ 表单校验集成
- ✅ 加载状态和错误处理
- ✅ 完整的事件系统
- ✅ 灵活的配置选项
- ✅ 无TypeScript依赖

## 注意事项

1. 组件会自动从字典API获取省份数据
2. 默认使用 `dictApi.registerAddres` 字典
3. 支持数据缓存，5分钟过期
4. 兼容现有的数据处理逻辑
5. 保持与Element Plus Cascader相同的API
