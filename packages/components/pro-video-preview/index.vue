<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title || '视频预览'"
    width="58%"
    :before-close="handleClose"
    class="video-preview-dialog"
  >
    <div class="video-container">
      <!-- 视频播放器 -->
      <div v-if="!error" class="video-wrapper">
        <video
          ref="videoRef"
          :src="videoUrl"
          controls
          preload="metadata"
          class="w-full max-h-96 rounded-lg shadow-lg"
          @loadstart="onVideoLoadStart"
          @loadeddata="onVideoLoaded"
          @error="onVideoError"
        >
          您的浏览器不支持视频播放。
        </video>

        <!-- 视频信息 -->
        <div v-if="videoInfo" class="mt-4 p-4 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="text-lg font-medium text-gray-900 mb-2">{{ videoInfo.fileName }}</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <div>文件大小: {{ formatFileSize(videoInfo.fileSize) }}</div>
                <div v-if="videoInfo.uploadTime">上传时间: {{ videoInfo.uploadTime }}</div>
                <div v-if="videoInfo.insertPersonName">上传人: {{ videoInfo.insertPersonName }}</div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-2 ml-4">
              <el-button
                type="primary"
                icon="Download"
                @click="downloadVideo"
                :loading="downloading"
              >
                {{ downloading ? "下载中..." : "下载" }}
              </el-button>

              <el-button
                icon="FullScreen"
                @click="toggleFullscreen"
              >
                全屏
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <!-- 视频加载错误 -->
      <div v-else-if="error" class="flex flex-col items-center justify-center h-96">
        <el-icon class="text-4xl text-red-500 mb-2">
          <WarningFilled />
        </el-icon>
        <span class="text-gray-600 mb-4">{{ error }}</span>
        <el-button type="primary" @click="retryLoad">重新加载</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";

// Props 定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  videoData: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: ""
  }
});

// Emits 定义
const emit = defineEmits(["update:visible", "download"]);

// 响应式数据
const videoRef = ref(null);
const loading = ref(false);
const error = ref("");
const downloading = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value)
});

const videoUrl = computed(() => props.videoData?.fileUrl || "");
const videoInfo = computed(() => props.videoData || {});

// 监听视频数据变化
watch(() => props.videoData, (newData) => {
  if (newData && newData.fileUrl) {
    error.value = "";
    loading.value = true;
  }
}, { immediate: true });

// 监听弹框显示状态
watch(() => props.visible, (visible) => {
  if (visible && videoRef.value) {
    // 弹框打开时暂停视频
    nextTick(() => {
      if (videoRef.value) {
        videoRef.value.currentTime = 0;
      }
    });
  } else if (!visible && videoRef.value) {
    // 弹框关闭时暂停视频
    videoRef.value.pause();
  }
});

// 方法定义
const handleClose = () => {
  if (videoRef.value) {
    videoRef.value.pause();
  }
  emit("update:visible", false);
};

const onVideoError = () => {
  loading.value = false;
  error.value = "视频加载失败，请检查网络连接或视频文件是否存在";
};

const retryLoad = () => {
  error.value = "";
  loading.value = true;
  if (videoRef.value) {
    videoRef.value.load();
  }
};

const downloadVideo = async () => {
  if (!videoUrl.value) {
    ElMessage.error("视频地址不存在");
    return;
  }

  downloading.value = true;

  try {
    // 创建下载链接
    const link = document.createElement("a");
    link.href = videoUrl.value;
    link.download = videoInfo.value.fileName || "video.mp4";
    link.target = "_blank";

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success("开始下载视频文件");

    // 触发下载事件，供父组件监听
    emit("download", videoInfo.value);
  } catch (error) {
    console.error("视频下载失败:", error);
    ElMessage.error("视频下载失败，请重试");
  } finally {
    downloading.value = false;
  }
};

const toggleFullscreen = () => {
  if (videoRef.value) {
    if (videoRef.value.requestFullscreen) {
      videoRef.value.requestFullscreen();
    } else if (videoRef.value.webkitRequestFullscreen) {
      videoRef.value.webkitRequestFullscreen();
    } else if (videoRef.value.mozRequestFullScreen) {
      videoRef.value.mozRequestFullScreen();
    } else if (videoRef.value.msRequestFullscreen) {
      videoRef.value.msRequestFullscreen();
    }
  }
};

const formatFileSize = (bytes) => {
  if (!bytes) return "未知";

  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
};
</script>

<style lang="scss" scoped>
.video-preview-dialog {
  .video-container {
    min-height: 400px;
  }

  .video-wrapper {
    .video-info {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      margin-top: 16px;
    }
  }
}

// 全屏时的样式
:deep(.el-dialog__body) {
  padding: 20px;
}

// 视频控件样式优化
video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

video::-webkit-media-controls-play-button,
video::-webkit-media-controls-volume-slider,
video::-webkit-media-controls-timeline,
video::-webkit-media-controls-current-time-display,
video::-webkit-media-controls-time-remaining-display {
  color: white;
}
</style>
