<script setup>
defineProps({
  title: {
    type: String,
    default: '设置title自定义标题',
  },
});
</script>

<template>
  <div class="pro-detail mb-2">
    <div class="title-content title-block">
      <span class="title">{{ title }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.pro-detail {
  .title-content {
    display: flex;
    align-items: center;
  }

  .title-desc {
    margin-left: 10px;
    font-size: 16px;
    font-weight: 600;
  }

  .title {
    font-size: 18px;
    font-weight: 900;
    display: flex;
    align-items: center;
  }

  .title-block::before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 22px;
    margin-right: 5px;
    @apply bg-primary;
  }

}
</style>
