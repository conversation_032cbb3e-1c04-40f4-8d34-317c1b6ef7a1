// 详情配置
export const detailConfig = {
    list: [
        {
            title: '申请信息',
            type: 'descrip',
            column: 3,
            labelWidth: 160,
            labelAlign: 'right',
            children: [
                { title: '企业名称', field: 'tradeAmt' },
                { title: '统一社会信用代码', field: 'remitDate' },
                { title: '法人名称', field: 'dueDate' },
                { title: '电话', field: 'billTypeText' },
                { title: '申请时间', field: 'transferFlagText' },
                { title: '需求进度', field: 'transferFlagText' },
                { title: '状态更新时间', field: 'transferFlagText', span: 24 },
            ],
        },
        {
            title: '产品信息',
            type: 'descrip',
            column: 3,
            labelWidth: 160,
            labelAlign: 'right',
            children: [
                { title: '产品名称', field: 'tradeAmt' },
                { title: '金融机构', field: 'remitDate' },
                { title: '贷款利率', field: 'billTypeText' },
                { title: '贷款额度', field: 'dueDate' },
                { title: '贷款期限', field: 'transferFlagText' },
                { title: '担保方式', field: 'transferFlagText' },
                { title: '还款方式', field: 'transferFlagText', span: 24 },
            ],
        },
        {
            title: '获贷信息',
            type: 'descrip',
            column: 3,
            labelWidth: 160,
            labelAlign: 'right',
            children: [
                { title: '贷款状态', field: 'issueEntName' },
                { title: '获贷时间', field: 'issueAcctNo' },
                { title: '是否首贷', field: 'issueAcctNo' },
                { title: '获贷金额', field: 'issueAcctName' },
                { title: '放款利率', field: 'issuePjsOrganId' },
                { title: '贷款期限', field: 'issuePjsOrganId' },
            ],
        },
    ],
};
