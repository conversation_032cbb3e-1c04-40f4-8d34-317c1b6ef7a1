<template>
  <div class="layout-padding">
    <pro-back-pre :title="route.meta.title"/>
    <!-- 正常 -->
    <div class="overflow-y-auto" v-loading="pageLoading">
      <pro-detail :config-data="detailConfig" :data="detailData"/>
    </div>
  </div>
</template>

<script setup>
import {detailConfig} from './data';
// import { useDicts, getDicName } from '/@/hooks/useDicts';

// 获取页面所需字典
// const useDict = useDicts('BillTypeEnum', 'BIllTransferFlagEnum');

// const billType = computed(() => getDicName(useDict.BillTypeEnum, props.data.billType));
// const transferFlag = computed(() => getDicName(useDict.BIllTransferFlagEnum, props.data.transferFlag));
const route = useRoute();
const pageLoading = ref(true);
const detailData = ref();

const getDetail = (id) => {
  detailData.value = {};
  // getDetail(id)
  // 	.then(({ data }) => {
  // 		detailData.value = data;
  // 	})
  // 	.finally(() => {
  pageLoading.value = false;
  // 	});
};
const query = route.query;
onMounted(() => {
  getDetail(query.id);
});
</script>
