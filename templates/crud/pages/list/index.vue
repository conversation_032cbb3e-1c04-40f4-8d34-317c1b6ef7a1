<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<page-table :search-config="searchConfig" :table-config="tableConfig" :table-header="tableHeader" @operation="onOperation" />
		</div>
	</div>
</template>
<script setup>
import { searchConfig, tableConfig, tableHeader } from './data';
import { useMessageBox } from '@core/hooks/message';
const InnerTable = defineAsyncComponent(() => import('/@/components/page-table.vue'));
const router = useRouter();
// 表格操作方法
const onOperation = ({ field, row }, refresh) => {
	switch (field) {
		case 'detail':
			router.push({
				path: ``,
				query: { id: row.id },
			});
			break;
		case 'del':
			useMessageBox()
				.confirm('确定删除当前数据?', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				})
				.then(() => {
					// 执行接口 并刷新列表
					refresh && refresh();
				})
				.catch(() => null);
			break;
	}
};
</script>
