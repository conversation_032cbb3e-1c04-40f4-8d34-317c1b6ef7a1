// import { dictApi } from '/@/api/dict/index';
// 搜索配置
export const searchConfig = {
	items: [
		{
			prop: 'name',
			type: 'date',
			attrs: {
				placeholder: '请输入产品名称',
				clearable: true,
			},
		},
		{
			prop: 'times',
			type: 'daterange',
			attrs: {
				clearable: true,
				startPlaceholder: '申请开始时间',
				endPlaceholder: '申请结束时间',
			},
		},
		{
			prop: 'invoiceStatus',
			type: 'select',
			option: 'invoiceStatus',
			attrs: {
				placeholder: '请选择需求进度',
				clearable: true,
			},
		},
		{
			prop: 'invoiceStatus',
			type: 'select',
			option: 'invoiceStatus',
			attrs: {
				placeholder: '请选择贷款状态',
				clearable: true,
			},
		},
	],
};
// table表格
export const tableHeader = [
	{ type: 'index', label: '序号', width: 55 },
	{ prop: 'invoiceNum', label: '分类', width: 200 },
	{ prop: 'invoiceNum', label: '产品名称', showOverflowTooltip: true },
	{ prop: 'platformType', label: '金融机构名称', width: 300 },
	{ prop: 'appType', label: '申请时间' },
	{ prop: 'taxType', label: '获贷金额(万元)', width: 140 },
	{ prop: 'taxType', label: '获贷时间', width: 170 },
	{ prop: 'taxType', label: '需求进度', width: 100 },
	{ prop: 'taxType', label: '贷款状态', width: 100 },
];

export const tableConfig = {
	request: {
		apiName: () => {
			return new Promise((resolve) => {
				resolve({
					code: 200,
					data: {
						list: [
							{
								id: 1,
							},
						],
						size: 10,
						current: 1,
						total: 10,
					},
				});
			});
		},
	},
	operation: {
		width: 180,
		fixed: 'right',
	},
};

