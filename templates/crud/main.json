{"name": "D:/project/official/xyd/xyd-web-monorepo/templates/CRUD", "description": "完整的CRUD模板，包含列表、详情、新增编辑、审核等功能页面及API接口", "param": ["moduleName"], "selectParam": [], "addFile": [{"name": "index.vue", "path": "{moduleName}[-d]/pages/list", "fileTemplatePath": "pages/list/index.vue"}, {"name": "data.js", "path": "{moduleName}[-d]/pages/list", "fileTemplatePath": "pages/list/data.js"}, {"name": "index.vue", "path": "{moduleName}[-d]/pages/detail", "fileTemplatePath": "pages/detail/index.vue"}, {"name": "data.js", "path": "{moduleName}[-d]/pages/detail", "fileTemplatePath": "pages/detail/data.js"}, {"name": "index.vue", "path": "{moduleName}[-d]/pages/edit", "fileTemplatePath": "pages/edit/index.vue"}, {"name": "data.js", "path": "{moduleName}[-d]/pages/edit", "fileTemplatePath": "pages/edit/data.js"}, {"name": "index.js", "path": "{moduleName}[-d]/api", "fileTemplatePath": "api/index.js"}, {"name": "", "path": "{moduleName}[-d]/img", "fileTemplatePath": ""}, {"name": "", "path": "{moduleName}[-d]/components", "fileTemplatePath": ""}, {"name": "index.js", "path": "", "fileTemplatePath": "api.tm"}], "insertInFile": [], "extensions": [], "globalBasePath": ""}