import {
    LoanStatusEnum,
    RequireProgressEnum,
    FinanceRequirementGuaranteeModeEnum,
    FinanceRequirementRepaymentWaysEnum
} from '@dict';

// 详情配置
export const detailConfig = () => {
    return {
        list: [
            {
                title: '申请信息',
                type: 'descrip',
                column: 3,
                labelWidth: 160,
                labelAlign: 'right',
                children: [
                    {title: '企业名称', field: 'orgName', span: 8},
                    {title: '统一社会信用代码', field: 'uniscId', span: 8},
                    {title: '法人名称', field: 'legalPersonName', span: 8},
                    {title: '电话', field: 'contact', span: 8},
                    {title: '融资需求金额', field: 'amount', span: 8, dw: '万元'},
                    {title: '贷款期限', field: 'maturity', span: 8, dw: '个月'},
                    {title: '担保方式', field: 'guaranteeMode', span: 8, dicId: FinanceRequirementGuaranteeModeEnum},
                    {title: '还款方式', field: 'repaymentWays', span: 8, dicId: FinanceRequirementRepaymentWaysEnum,tag:true},
                    {title: '贷款用途', field: 'orientation', span: 8},
                    {title: '需求发布时间', field: 'publishTime', span: 8},
                    {title: '需求进度', field: 'requireProgress', span: 8, dicId: RequireProgressEnum},
                    {title: '状态更新时间', field: 'updateStatusTime', span: 8},
                ],
            },
            {
                title: '获贷信息',
                type: 'descrip',
                column: 3,
                labelWidth: 160,
                labelAlign: 'right',
                children: [
                    {title: '贷款状态', field: 'loanStatus', span: 8, dicId: LoanStatusEnum},
                    {title: '获贷时间', field: 'loanDate', span: 8},
                    {title: '贷款期限', field: 'loanMaturity', span: 8, dw: '个月'},
                    {title: '获贷金额', field: 'loanAmount', span: 8, dw: '万元'},
                    {title: '放贷利率', field: 'lendingRate', span: 16, dw: '%'},
                ],
            },
        ],
    };
};
