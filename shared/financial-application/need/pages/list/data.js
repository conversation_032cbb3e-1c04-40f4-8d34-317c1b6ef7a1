import { getList } from '../../api';
import { LoanStatusEnum, RequireProgressEnum, FinanceRequirementGuaranteeModeEnum, FinanceRequirementRepaymentWaysEnum } from '@dict';

// 搜索配置
export const searchConfig = {
	items: [
		{
			prop: 'orgName',
			type: 'input',
			attrs: {
				placeholder: '请输入企业名称',
				clearable: true,
			},
		},
		{
			prop: 'times',
			type: 'daterange',
			attrs: {
				clearable: true,
				startPlaceholder: '需求发布开始时间',
				endPlaceholder: '需求发布结束时间',
				rangeSeparator: '-',
				'value-format': 'YYYY-MM-DD HH:mm:ss',
			},
			splitProp: ['publishStartTime', 'publishEndTime'],
		},
		{
			prop: 'requireProgress',
			type: 'select',
			option: 'requireProgress',
			attrs: {
				placeholder: '请选择需求进度',
				clearable: true,
			},
		},
		{
			prop: 'loanStatus',
			type: 'select',
			option: 'loanStatus',
			attrs: {
				placeholder: '请选择贷款状态',
				clearable: true,
			},
		},
	],
};
// table表格
export const tableHeader = () => {
	const isUcenter = location.pathname.includes(import.meta.env.VITE_ROUTE_PATH);
	return [
		{ type: 'selection', label: '', width: 55, fixed: true },
		{ type: 'index', label: '序号', width: 55 },
		{ prop: 'orgName', label: '企业名称', width: 200 },
		{ prop: 'publishFinanceOrgName', label: '指定金融机构名称', width: 200, hidden:isUcenter },
		{ prop: 'amount', label: '融资需求金额', width: 140 },
		{ prop: 'maturity', label: '贷款期限', width: 140, showOverflowTooltip: true },
		{ prop: 'guaranteeMode', label: '担保方式 ', width: 180, showOverflowTooltip: true },
		{ prop: 'repaymentWays', label: '还款方式', width: 200, showOverflowTooltip: true},
		{ prop: 'lendingRate', label: '获贷利率', width: 200, showOverflowTooltip: true, hidden:isUcenter  },
		{ prop: 'publishTime', label: '需求发布时间', width: 200, showOverflowTooltip: true },
		{ prop: 'loanAmount', label: '获贷金额（万元）', width: 200, showOverflowTooltip: true },
		{ prop: 'loanDate', label: '获贷时间', width: 200, showOverflowTooltip: true },
		{ prop: 'requireProgress', label: '需求进度', width: 200, showOverflowTooltip: true },
		{ prop: 'loanStatus', label: '贷款状态', width: 170 },
	];
};
export const tableConfig = {
	request: {
		apiName: getList,
	},
	operation: {
		width: 220,
		fixed: 'right',
	},
	dic: {
		loanStatus: LoanStatusEnum,
		requireProgress: RequireProgressEnum,
		guaranteeMode: FinanceRequirementGuaranteeModeEnum,
		repaymentWays: FinanceRequirementRepaymentWaysEnum,
	},
};
