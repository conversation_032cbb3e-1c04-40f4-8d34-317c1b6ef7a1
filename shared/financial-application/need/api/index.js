// 列表
export function getList(params) {
	return window.request({
		url: '/etbtrade/financingRequirement/page',
		method: 'GET',
		params,
	});
}
// 信息
export function getInfo(id) {
	return window.request({
		url: `/etbtrade/financingRequirement/${id}`,
		method: 'GET',

	});
}

export const exportData = '/etbtrade/financingRequirement/export'

// 更新
export function edit(data, isLoad) {
	return window.request({
		url: isLoad ? `/etbtrade/financingRequirement/update-loan-status` : `/etbtrade/financingRequirement/update-require-status`,
		method: "post",
		data
	});
}
