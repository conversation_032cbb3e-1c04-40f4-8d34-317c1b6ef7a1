// 列表
export function getList(params) {
  return window.request({
    url: "/etbtrade/finance-product-apply/page",
    method: "GET",
    params
  });
}

// 信息
export function getInfo(id) {
  return window.request({
    url: `/etbtrade/finance-product-apply/detail/${id}`,
    method: "GET"

  });
}

export const exportData = "/etbtrade/finance-product-apply/export";

// 更新
export function edit(data, isLoad) {
  return window.request({
    url: isLoad ? `/etbtrade/finance-product-apply/update-loan-status` : `/etbtrade/finance-product-apply/update-require-status`,
    method: "post",
    data
  });
}
