<template>
	<div class="layout-padding">
		<pro-back-pre />
		<!-- 正常 -->
		<div class="overflow-y-auto" v-loading="pageLoading">
			<pro-detail :config-data="detailConfig()" :data="detailData" />
		</div>
	</div>
</template>

<script setup>
import { detailConfig } from './data';
import { getInfo } from '../../api';

const pageLoading = ref(true);
const detailData = ref();
const route = useRoute();

const getDetails = (id) => {
	detailData.value = {};
	getInfo(id)
		.then(({ data }) => {
			detailData.value = data;
		})
		.finally(() => {
			pageLoading.value = false;
		});
};
onMounted(() => {
	getDetails(route.query.id);
});
</script>
