import {
    yesOrNoEnum,
    FinanceProductApplyLoanStateEnum,
    FinanceProductApplyDemandProgressEnum,
    FinanceProductProductTypeEnum,
    FinanceProductGuarantyTypeEnum,
    FinanceProductRepaymentTypeEnum,
} from '@dict';

// 详情配置
export const detailConfig = () => {
    return {
        list: [
            {
                title: '申请信息',
                type: 'descrip',
                column: 3,
                labelWidth: 160,
                labelAlign: 'right',
                children: [
                    {title: '企业名称', field: 'companyName', span: 8},
                    {title: '统一社会信用代码', field: 'ecode', span: 8},
                    {title: '法人名称', field: 'legalPersonName', span: 8},
                    {title: '电话', field: 'organizationContactPhone', span: 8},
                    {title: '申请时间', field: 'applyTime', span: 8},
                    {title: '需求进度', field: 'requireStatus', span: 8, dicId: FinanceProductApplyDemandProgressEnum},
                    {title: '状态更新时间', field: 'requireUpdateTime', span: 24},
                ],
            },
            {
                title: '产品信息',
                type: 'descrip',
                column: 3,
                labelWidth: 160,
                labelAlign: 'right',
                children: [
                    {title: '产品名称', field: 'productName', span: 8},
                    {title: '产品类型', field: 'productType', span: 8, dicId: FinanceProductProductTypeEnum},
                    {title: '金融机构', field: 'financeOrganizationName', span: 8},
                    {title: '贷款利率', field: 'loanRateMin', span: 8, rightField: 'loanRateMax', dw: '%'},
                    {title: '贷款额度', field: 'loanQuotaMin', span: 8, rightField: 'loanQuotaMax', dw: '万元'},
                    {title: '贷款期限', field: 'loanTimeMin', span: 8, rightField: 'loanTimeMax', dw: '个月'},
                    {title: '担保方式', field: 'guarantyMethod', span: 8, dicId: FinanceProductGuarantyTypeEnum},
                    {title: '还款方式', field: 'paymentType', span: 16, dicId: FinanceProductRepaymentTypeEnum,tag:true},
                ],
            },
            {
                title: '获贷信息',
                type: 'descrip',
                column: 3,
                labelWidth: 160,
                labelAlign: 'right',
                children: [
                    {title: '贷款状态', field: 'loanStatus', span: 8, dicId: FinanceProductApplyLoanStateEnum},
                    {title: '获贷时间', field: 'loanTime', span: 8},
                    {title: '是否首贷', field: 'ifFirstLoan', span: 8, option: yesOrNoEnum.data},
                    {title: '获贷金额', field: 'loanAmount', span: 8, dw: '万元'},
                    {title: '放贷利率', field: 'loanRate', span: 8, dw: '%'},
                    {title: '贷款期限', field: 'loanPeriod', span: 8, dw: '个月'},
                ],
            },
        ],
    };
};
