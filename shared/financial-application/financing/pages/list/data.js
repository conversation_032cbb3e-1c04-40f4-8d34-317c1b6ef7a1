import { getList } from '../../api';
import { FinanceProductApplyLoanStateEnum, FinanceProductApplyDemandProgressEnum, FinanceProductGuarantyTypeEnum } from '@dict';

const isUcenter = location.pathname.includes(import.meta.env.VITE_ROUTE_PATH);

// 搜索配置
export const searchConfig = {
	items: [
		{
			prop: 'companyName',
			type: 'input',
			attrs: {
				placeholder: '请输入企业名称',
				clearable: true,
			},
		},
		{
			prop: 'productName',
			type: 'input',
			attrs: {
				placeholder: '请输入产品名称',
				clearable: true,
			},
		},
		{
			prop: 'financeOrganizationName',
			type: 'input',
			attrs: {
				placeholder: '请输入金融机构名称',
				clearable: true,
			},
			hidden: isUcenter,
		},
		{
			prop: 'times',
			type: 'daterange',
			attrs: {
				clearable: true,
				startPlaceholder: '申请开始时间',
				endPlaceholder: '申请结束时间',
				rangeSeparator: '-',
				'value-format': 'YYYY-MM-DD HH:mm:ss',
			},
			splitProp: ['applyTimeStart', 'applyTimeEnd'],
		},
		{
			prop: 'requireStatus',
			type: 'select',
			option: 'requireStatus',
			attrs: {
				placeholder: '请选择需求进度',
				clearable: true,
			},
		},
		{
			prop: 'loanStatus',
			type: 'select',
			option: 'loanStatus',
			attrs: {
				placeholder: '请选择贷款状态',
				clearable: true,
			},
		},
	],
};
// table表格
export const tableHeader = () => {
	return [
		{ type: 'selection', label: '', width: 55, fixed: true },
		{ type: 'index', label: '序号', width: 55 },
		{ prop: 'companyName', label: '企业名称', width: 200 },
		{ prop: 'legalPersonName', label: '企业法人', width: 200 },
		{ prop: 'legalPersonPhone', label: '联系电话', width: 200 },
		{ prop: 'productName', label: '产品名称', width: 140 },
		{
			prop: 'financeOrganizationName',
			label: '金融机构名称',
			width: 140,
			showOverflowTooltip: true,
			hidden: isUcenter,
		},
		{ prop: 'applyTime', label: '申请时间', width: 180, showOverflowTooltip: true },
		{ prop: 'loanAmount', label: '获贷金额（万元）', width: 200, showOverflowTooltip: true },
		{ prop: 'loanTime', label: '获贷时间', width: 200, showOverflowTooltip: true },
		{ prop: 'requireStatus', label: '需求进度', width: 200, showOverflowTooltip: true },
		{ prop: 'loanStatus', label: '贷款状态', width: 170 },
	];
};
export const tableConfig = {
	request: {
		apiName: getList,
	},
	operation: {
		width: 220,
		fixed: 'right',
	},
	dic: {
		loanStatus: FinanceProductApplyLoanStateEnum,
		requireStatus: FinanceProductApplyDemandProgressEnum,
		guaranteeMode: FinanceProductGuarantyTypeEnum,
	},
};
