<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<page-table :search-config="searchConfig" ref="innerTableRef" :table-config="tableConfig" :table-header="tableHeader()" @operation="onOperation" @selection-change="onSelectChange">
        <template #loanAmount="{ row }"> {{ row.loanAmount ? row.loanAmount + '万元' : '-' }}</template>
			</page-table>
			<component :is="components[dynamicComponent]" v-if="dialogVisible" :notEdit="notEdit" v-model:visible="dialogVisible" :row="rowData" @refresh="refresh"></component>
		</div>
	</div>
</template>
<script setup>
import { searchConfig, tableConfig, tableHeader } from './data';


let dynamicComponent = ref('');
const components = {
	edit: defineAsyncComponent(() => import('./components/edit')),
};
import { useRouter } from 'vue-router';
import { downBlobFile } from '/@/utils/other';
import { exportData } from '../../api';
import { ElMessage } from 'element-plus';

let innerTableRef = ref('');
let rowData = ref(null);
let dialogVisible = ref(false);

function refresh() {
	innerTableRef.value?.onSearchHandle();
}

// 表格selection
const state = reactive({
	selectedData: [],
});
const onSelectChange = (selection) => {
	state.selectedData = selection;
};
const router = useRouter();
const notEdit = ref(false);
// 表格操作方法
const onOperation = async ({ field, row, btn }) => {
	rowData.value = row || null;
	const ids = state.selectedData.map((item) => item.id).join(',');
	notEdit.value = false;
	switch (field) {
		case 'export':
			btn.loading = true;
			try {
				btn.loading = !(await downBlobFile(exportData, { ids, ...toRaw(innerTableRef.value.oldParams) }));
			} catch (error) {
				btn.loading = false;
				ElMessage.error('下载失败');
			}
			break;
		case 'detail':
			router.push({
				path: '../detail/index',
				query: {
					id: row.id,
				},
			});
			break;
		case 'update':
			dialogVisible.value = true;
			dynamicComponent.value = 'edit';
			break;
	}
};
</script>
