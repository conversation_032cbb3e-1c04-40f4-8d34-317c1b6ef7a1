import { yesOrNoEnum, FinanceProductApplyDemandProgressEnum } from "@dict";

export const formListFn = function({ notEdit }) {
  return [
    {
      id: "requireStatus",
      name: "需求进度",
      dicKey: notEdit.value ? "FinanceProductApplyDemandProgressEnum" : "DockingInProgressEnum",
      filterable: true,
      isString: true,
      attributes: { disabled: notEdit }
    },
    { id: "requireUpdateTime", name: "状态更新时间", type: "datetime", attributes: { disabled: notEdit } },
    {
      id: "loanStatus",
      name: "贷款状态",
      dicKey: "LoanStatusEnum",
      filterable: true,
      isString: true,
      val: {},
      relation: notEdit
    },
    {
      id: "loanTime", name: "获贷时间", type: "datetime", attributes: { disabled: notEdit }, relation: {
        id: "requireStatus",
        val: '4'
      }
    },
    {
      id: "ifFirst<PERSON>oan",
      name: "是否首贷",
      type: "radio",
      list: yesOrNoEnum.data,
      attributes: { disabled: notEdit }, relation: {
        id: "requireStatus",
        val: '4'
      }
    },
    {
      id: "loanAmount",
      name: "获贷金额",
      prepend: "￥",
      append: "万元",
      validate: {
        type: "number",
        int: 10,
        decimal: 2
      },
      attributes: { disabled: notEdit }, relation: {
        id: "requireStatus",
        val: '4'
      }
    },
    {
      id: "loanRate", name: "放款利率", append: "%", validate: {
        type: "number",
        max: 99.99,
        decimal: 2
      }, attributes: { disabled: notEdit }, relation: {
        id: "requireStatus",
        val: '4'
      }
    },
    {
      id: "loanPeriod", name: "货款期限", append: "个月", validate: {
        type: "number",
        decimal: 0
      }, attributes: { disabled: notEdit }, relation: {
        id: "requireStatus",
        val: '4'
      }
    }
  ];
};
