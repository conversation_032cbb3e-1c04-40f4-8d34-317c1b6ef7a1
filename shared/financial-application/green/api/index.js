// 列表
export function getList(params) {
	return window.request({
		url: '/etbtrade/green-finance-apply/page',
		method: 'GET',
		params,
	});
}
// 信息
export function getInfo(id) {
	return window.request({
		url: `/etbtrade/green-finance-apply/detail/${id}`,
		method: 'GET',

	});
}

export const exportData = '/etbtrade/green-finance-apply/export'

// 更新
export function edit(data, isLoad) {
	return window.request({
		url: isLoad ? `/etbtrade/green-finance-apply/update-loan-status` : `/etbtrade/green-finance-apply/update-require-status`,
		method: "post",
		data
	});
}
