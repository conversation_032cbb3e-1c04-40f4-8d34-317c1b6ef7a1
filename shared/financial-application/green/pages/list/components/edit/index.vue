<template>
	<el-dialog v-model="dialogVisible" :title="props.notEdit ? '详情' : props.row ? '编辑' : '新增'" width="500">
		<div class="w-100">
			<co-form disabled ref="formRef" :form-list="formList" :form-data="formData" :dictConfig="dictConfig" :labelWidth="labelWidth">

			</co-form>
		</div>
    <template #footer>
      <div>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </template>
	</el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { formListFn } from './data.js';
import { edit, getInfo } from '../../../../api';
import { useDicts } from '/@/hooks/useDicts';
import * as dic from '@dict';
const emit = defineEmits(['update:visible', 'refresh']);
const props = defineProps(['visible', 'row', 'notEdit']);

const dictConfig = useDicts(dic, 'DockingInProgressEnum','LoanStatusEnum','FinanceProductApplyDemandProgressEnum');

const formRef = ref();
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('140px');
let dialogVisible = computed({
	set(n) {
		emit('update:visible', n);
	},
	get() {
		return props.visible;
	},
});
const notEdit = ref(false)
formList.value = formListFn({ notEdit });
formData.value = props.row || {};
onMounted(() => {
	if (props.row) {
		getDetail();
	}
});

watchEffect(()=>{
  notEdit.value = props.row.requireStatus == 4
})
function getDetail() {
	getInfo(props.row.id).then((res) => {
    let obj = { ...formData.value, ...res.data }
    if(props.row.requireStatus == 1) delete obj.requireStatus
		formData.value = obj;
	});
}

async function submit() {
	let saveData = await formRef.value.getFormData();
  edit({ ...props.row, ...saveData }, notEdit.value)
		.then(() => {
			ElMessage.success('操作成功');
      dialogVisible.value = false;
      emit('refresh');
		})
}
</script>

<style lang="scss"></style>
