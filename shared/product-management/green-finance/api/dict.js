import {reactive, nextTick} from 'vue';
import {ElMessage} from 'element-plus';

// 读取文件，找到字典接口并合并
const dicKeyApi = (() => {
  const req = import.meta.glob('./**/*.js', {eager: true});
  let num = 0;
  const api = {};
  const keys = Object.keys(req);
  keys.forEach((k) => {
    Object.assign(api, req[k]);
    num += Object.keys(req[k]).length;
  });
  if (num !== Object.keys(api).length) {
    ElMessage.error('存在重复的字典接口，请检查');
  }
  return api;
})();


let saveDicData = {};
let saveDicLoading = {};
let saveDicPromise = {};

// 获取字段数据
function getNewDic(key) {
  // 如果没有缓存
  if (!saveDicData[key]) {
    return [];
  }
  // 创建一个新的对象
  return saveDicData[key].map(item => {
    return {...item};
  });
}

// 如果数据正在加载中，则创建一个Promise对象，等待数据继续请求
function newDicPromise(key, dictConfig) {
  // 定一个字典空对象，用于存放创建的Promise
  saveDicPromise[key] = [];
  return new Promise((resolve, reject) => {
    // 存放Promise对象
    saveDicPromise[key].push({resolve, reject});
  }).then((data) => {
    dictConfig[key] = data;
  }).catch(() => {
    dictConfig[key] = [];
  });
}

/**
 * 获取字典列表
 * @param dicKey {String|Array} 字典key
 * @param [callback] {Function} 字典请求完毕回调执行
 */
export function getDictConfig(dicKey, callback) {
  if (!dicKey) {
    return {};
  }
  // 参数统一化
  const dkList = typeof dicKey === 'string' ? [dicKey] : dicKey;
  const orgDict = {};
  // 字典默认值
  dkList.forEach(k => (orgDict[k] = []));
  // 定义字典
  const dictConfig = reactive(orgDict);
  // 请求字典
  const $promiseList = dkList.map((key) => {
    // 读取缓存
    if (saveDicData[key]) {
      return nextTick().then(() => {
        dictConfig[key] = getNewDic(key);
      });
    }
    // 字典接口
    const dicFun = dicKeyApi[key];
    // 如果字典接口不存在
    if (!dicFun) {
      return Promise.resolve();
    }
    // 如果数据正在加载中
    if (saveDicLoading[key]) {
      return newDicPromise(key, dictConfig);
    }
    // 添加接口请求标识
    saveDicLoading[key] = true;
    // 请求数据
    return dicFun().then((res) => {
      // 存在字典数据
      saveDicData[key] = res.data;
      // 对存放数据进行复制
      dictConfig[key] = getNewDic(key);
    }).then(() => {
      // 执行存放的Promise
      if (saveDicPromise[key]) {
        saveDicPromise[key].forEach((row) => {
          row.resolve(getNewDic(key));
        });
      }
    }).catch((e) => {
      console.log('字典请求失败', e);
      // 执行存放的Promise
      if (saveDicPromise[key]) {
        saveDicPromise[key].forEach((row) => {
          row.reject(e);
        });
      }
    }).finally(() => {
      // 清除存放的Promise
      saveDicLoading[key] = void (0);
      saveDicPromise[key] = void (0);
    });
  });
  // 如果存在回调，则字典请求完毕后，回调执行
  if (typeof callback === 'function') {
    Promise.all($promiseList).finally(() => {
      callback();
    });
  }
  return dictConfig;
}

// 清除字典数据
export function cleanDictConfig(key) {
  if (typeof key === 'string') {
    if (key === 'all') {
      saveDicData = {};
    } else if (key) {
      saveDicData[key] = void (0);
    }
  } else {
    for (let k in key) {
      saveDicData[k] = void (0);
    }
  }
}
