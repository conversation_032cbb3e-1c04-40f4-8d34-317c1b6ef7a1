import {getDictionary} from '/@/api/common/dictionary';
import {GFApprovalModeEnum, GFPaymentTypeEnum, GFProductTypeEnum, GFGuaranteeMethodEnum} from '@dict';

// 列表
export function getList(params) {
    return window.request({
        url: '/etbtrade/green-finance-product/page',
        method: 'GET',
        params,
    });
}

// 导出
export const exportData = '/etbtrade/green-finance-product/export'

// 调整热门
export function updateHot(params) {
    return window.request({
        url: '/etbtrade/green-finance-product/update-hot',
        method: 'GET',
        params,
    });
}

// 查看详情
export function getDetail(id) {
    return window.request({
        url: `/etbtrade/green-finance-product/${id}`,
        method: 'GET',
    });
}

// 调整排序
export function updateSort(params) {
    return window.request({
        url: '/etbtrade/green-finance-product/update-sort',
        method: 'GET',
        params,
    });
}

//  切换上下架
export function updateStatus(data) {
    return window.request({
        url: '/etbtrade/green-finance-product/update-status',
        method: 'post',
        data,
    });
}

//  保存
export function save(data, row) {
    return window.request({
        url: row ? '/etbtrade/green-finance-product/update' : '/etbtrade/green-finance-product/save',
        method: 'post',
        data,
    });
}

//  审核
export function audit(data) {
    return window.request({
        url: '/etbtrade/green-finance-product/audit',
        method: 'post',
        data,
    });
}

// 删除数据
export function deleteById(id) {
    return window.request({
        url: `/etbtrade/green-finance-product/delete/${id}`,
        method: 'GET',
    });
}

// 机构管理列表
export function getOrganizationManagementList(type) {
    return window
        .request({
            url: `/etbtrade/organizationManagement/list`,
            method: 'GET',
        })
        .then((res) => {
            let data = res.data.map((item) => {
                return {
                    id: item.id,
                    label: item.orgName,
                    uniscId: item.uniscId,
                    value: item.id
                };
            })
            return type == 'clear' ? data : {
                data
            };
        });
}

// 标签名称
export function getTagLibraryListByTagName(params) {
    return window
        .request({
            url: `/etbtrade/tagLibrary/list`,
            method: 'GET',
            params,
        })
        .then((res) => {
            if (res.data.length == 0) return {data: []};
            return {
                data: res.data.map((item) => {
                    return {
                        selectType: item.selectType,
                        id: item.tagName,
                        label: item.tagName,
                    };
                }),
            };
        });
}

// 查询启用的不分页分类列表
export function getGreenFinanceCategoryList(type) {
    return window
        .request({
            url: `/etbtrade/green-finance-category/list`,
            method: 'GET',
        })
        .then((res) => {
            let data = res.data.map((item) => {
                return {
                    id: item.id,
                    label: item.categoryName,
                    value: item.id
                };
            })
            return type == 'clear' ? data : {data};
        });
}

// 标签库分类
export function getTagLibraryList(params) {
    return window
        .request({
            url: `/etbtrade/tagLibrary/list`,
            method: 'GET',
            params,
        })
        .then((res) => {
            if (res.data.length == 0 || res.data[0].selectionList.length == 0) return {data: []};
            return {
                data: res.data[0].selectionList.map((item) => {
                    return {
                        id: item,
                        label: item,
                    };
                }),
            };
        });
}

// 查询可用站点list
export function getListAvailable(params) {
    return window
        .request({
            url: `/etbtrade/service-area/list-available`,
            method: 'GET',
            params,
        })
        .then((res) => {
            if (res.data.length == 0) return {data: []};
            return {
                data: res.data.map((item) => {
                    return {
                        id: item.code,
                        label: item.name,
                    };
                }),
            };
        });
}

// 审批模式
export function getGFApprovalModeEnum() {
    return getDictionary(GFApprovalModeEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 产品类型
export function getGFProductTypeEnum() {
    return getDictionary(GFProductTypeEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 还款方式
export function getGFPaymentTypeEnum() {
    return getDictionary(GFPaymentTypeEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 担保方式
export function getGFGuaranteeMethodEnum() {
    return getDictionary(GFGuaranteeMethodEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 导出
export function guaranteeOrderGiveup(data) {
    return window.request({
        url: `/etbtrade/guarantee-order/give-up`,
        method: 'POST',
        data,
    });
}

// 获取推荐企业列表
export function getRecommendEnterprises(params) {
    return window.request({
        url: `/etbtrade/enterprise-product-match/page`,
        method: 'get',
        params,
    });
}
