<template>
	<el-dialog v-model="dialogVisible" :title="row ? '编辑' : '新增'" width="580px">
		<div class="w-100 co-single-form">
			<co-form ref="formRef" :form-list="formList" :form-data="formData" inline :labelWidth="labelWidth" @event="onFormEvent">
				<template #sortNumTip>
					<span class="text-red-600 text-xs ml-1">注：排序值越大越靠前</span>
				</template>
			</co-form>
		</div>
    <template #footer>
      <div>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </template>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { formListFn } from './data.js';
import { updateSort } from '../../../../api';
const emit = defineEmits(['update:visible']);
const props = defineProps(['visible', 'row']);

const formRef = ref();
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('150px');
formList.value = formListFn();
formData.value = props.row || {};
let dialogVisible = computed({
	set(n) {
		emit('update:visible', n);
	},
	get() {
		return props.visible;
	},
});

async function submit() {
	let saveData = await formRef.value.getFormData();
	updateSort({ ...props.row, ...saveData }, props.row)
		.then(() => {
			ElMessage.success('操作成功');
		})
		.finally(() => {
			dialogVisible.value = false;
			emit('refresh');
		});
}
</script>

<style lang="scss">
.co-single-form {
	.el-form {
		display: flex;
		flex-wrap: wrap;
	}

	.el-form-item {
		margin: 0 0 20px 0;
	}

	.co-form-block {
		flex: 0 0 100%;
	}
}
</style>
