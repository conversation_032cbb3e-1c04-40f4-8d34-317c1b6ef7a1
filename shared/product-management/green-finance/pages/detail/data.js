import { GFAuditStatusEnum, yesOrNoEnum, onlineEnum } from "@dict";
import {
  getOrganizationManagementList,
  getGFApprovalModeEnum,
  getGFProductTypeEnum,
  getGFPaymentTypeEnum,
  getGFGuaranteeMethodEnum
} from "../../api";
// 详情配置
export const detailConfig = () => {
  const isAudit = useRoute().query.type == "auditPage";
  return {
    list: [
      {
        title: "产品信息",
        type: "descrip",
        column: 1,
        labelWidth: 160,
        labelAlign: "right",
        children: [
          { title: "产品名称", field: "productName", span: 24 },
          {
            title: "发布机构",
            field: "financeOrganizationId",
            span: 24,
            interfaceName: getOrganizationManagementList
          },
          { title: "产品分类", field: "categoryName", span: 24 },
          { title: "担保方式", field: "guarantyMethod", span: 24, interfaceName: getGFGuaranteeMethodEnum },
          { title: "产品类型", field: "productType", span: 24, interfaceName: getGFProductTypeEnum },
          { title: "标签介绍", field: "tag", span: 24 },
          { title: "机构联系人姓名", field: "organizationContactName", span: 24 },
          { title: "机构联系电话", field: "organizationContactPhone", span: 24 },
          { title: "贷款利率", field: "loanRateMin", rightField: "loanRateMax", dw: "%", span: 24 },
          { title: "贷款额度", field: "loanQuotaMin", rightField: "loanQuotaMax", dw: "万元", span: 24 },
          { title: "贷款期限", field: "loanTimeMin", rightField: "loanTimeMax", dw: "个月", span: 24 },
          { title: "是否政策性产品", field: "ifPolicyProduct", span: 24, option: yesOrNoEnum.data },
          { title: "审批模式", field: "approvalMode", span: 24, interfaceName: getGFApprovalModeEnum },
          { title: "还款方式", field: "paymentType", span: 24, interfaceName: getGFPaymentTypeEnum, tag: true },
          { title: "服务区域", field: "serviceArea", span: 24 },
          { title: "产品介绍", field: "introduction", span: 24 },
          { title: "基本条件", field: "basicCondition", span: 24 },
          { title: "产品特点", field: "productFeature", span: 24 },
          { title: "产品标签", field: "productTag", span: 24 },
          { title: "发布时间", field: "insertTime", span: 24 },
          // 以下内容审核页不可见
          { title: "审核状态", field: "auditStatus", span: 24, isHidden: isAudit, dicId: GFAuditStatusEnum },
          { title: "审核时间", field: "auditTime", span: 24, isHidden: isAudit },
          { title: "审核失败原因", field: "auditMsg", span: 24, isHidden: isAudit },
          { title: "上架状态", field: "status", span: 24, isHidden: isAudit, option: onlineEnum.data },
          { title: "是否热门产品", field: "ifHot", span: 24, isHidden: isAudit, option: yesOrNoEnum.data },
          { title: "排序", field: "sortNum", span: 24, isHidden: isAudit }
        ]
      }
    ]
  };
};
