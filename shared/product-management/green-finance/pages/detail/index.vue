<template>
	<div class="layout-padding">
		<pro-back-pre />
		<!-- 正常 -->
		<div class="overflow-y-auto" v-loading="pageLoading">
			<pro-detail :config-data="detailConfig(!isAudit)" :data="detailData">
				<template #productTag="{ row }">
					<addTable v-model:data="row.productTag" :disabled="true"></addTable>
				</template>
				<template #tag="{ row }">
					<el-tag v-for="(item, index) in row.tag ? row.tag.split(',') : []" :key="index" class="mr-2">{{ item }} </el-tag>
				</template>
				<template #footer >
					<div v-if="route.query.type === 'auditPage'">
						<el-button @click="$router.go(-1)">返回</el-button>
						<el-button type="danger" @click="auditReject">驳回</el-button>
						<el-button type="primary" @click="auditPass">通过</el-button>
					</div>
				</template>
			</pro-detail>
		</div>
	</div>
</template>

<script setup>
import { detailConfig } from './data';
import { getDetail, audit } from '../../api';
import addTable from '../edit/components/addTable';

const pageLoading = ref(true);
const detailData = ref();
const isAudit = ref(false);
const route = useRoute();
const router = useRouter();

function auditPass() {
	ElMessageBox.confirm('确认审批通过?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(() => {
		audit({ id: route.query.id, auditStatus: 2 }).then((res) => {
			ElMessage.success(res.msg);
			router.go(-1);
		});
	});
}

function auditReject() {
	ElMessageBox.prompt('请输入驳回原因', '提示', {
		inputType: 'textarea',
		confirmButtonText: '确定',
		cancelButtonText: '取消',
    inputValidator: (value) => {
      // 去除首尾空白字符（包括换行符、空格、制表符等）
      const trimmedValue = value ? value.trim() : '';

      // 检查长度是否在有效范围内
      if (trimmedValue.length === 0) {
        return '请输入驳回原因';
      }
      if (trimmedValue.length > 100) {
        return `驳回原因不能超过100个字符，当前${trimmedValue.length}个字符`;
      }

      // 验证通过
      return true;
    },
    inputPlaceholder: '请输入驳回原因（最多100字）',
    customClass: 'reject-reason-dialog',
	}).then(({ value }) => {
    // 使用处理后的值（去除首尾空白）
    const trimmedValue = value ? value.trim() : '';
		audit({ id: route.query.id, auditStatus: 3, auditMsg: trimmedValue }).then((res) => {
			ElMessage.success(res.msg);
			router.go(-1);
		});
	});
}

const getDetails = (id) => {
	detailData.value = {};
	getDetail(id)
		.then(({ data }) => {
			// auditStatus === 1 审核中
			isAudit.value = data.auditStatus === 1;
			data.productTag = JSON.parse(data.productTag || '[]');
			detailData.value = data;
		})
		.finally(() => {
			pageLoading.value = false;
		});
};
onMounted(() => {
	getDetails(route.query.id);
});
</script>
