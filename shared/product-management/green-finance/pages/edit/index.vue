<template>
  <div class="layout-padding">
    <pro-back-pre/>
    <div class="co-single-form layout-padding-auto layout-padding-view">
      <co-form ref="formRef" :form-list="formList" :form-data="formData" :dictConfig="dictConfig"
               :labelWidth="labelWidth">
        <template v-slot:productTag="{ row, data }">
          <add-table v-model:data="data[row.id]"/>
        </template>
        <template #tag="{ row, data }">
          <addTag v-model="data[row.id]" maxTags="5" maxLength="5"/>
        </template>
      </co-form>
      <div class="flex justify-center mt-10">
        <div>
          <el-button @click="$router.go(-1)">取消</el-button>
          <el-button type="primary" @click="submit" :loading="submitLoading">提交</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue';
import {formListFn} from './data.js';
import {getDictConfig} from '../../api/dict';
import {save, getDetail} from '../../api';
import {useUserInfo} from '/@/stores/userInfo';

const router = useRouter();
const route = useRoute();
const formRef = ref();
const submitLoading = ref(false);
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('150px');
const addTable = defineAsyncComponent(() => import('./components/addTable/index.vue'));
const addTag = defineAsyncComponent(() => import('@components/pro-add-tag'));
const isUcenter = location.pathname.includes(import.meta.env.VITE_ROUTE_PATH);
const dictConfig = getDictConfig(['getOrganizationManagementList', 'getGreenFinanceCategoryList', 'getGFApprovalModeEnum', 'getGFProductTypeEnum', 'getGFPaymentTypeEnum', 'getGFGuaranteeMethodEnum', 'getListAvailable']);
formList.value = formListFn();
watchEffect(() => {
  if (dictConfig.getOrganizationManagementList.length) {
    if (isUcenter) {
      let item = dictConfig.getOrganizationManagementList.find((item) => item.uniscId == useUserInfo().userInfos.ecode);
      formData.value = {financeOrganizationId: item.id}
    }
    getInfo();
  }
});

function getInfo() {
  if (route.query.id) {
    getDetail(route.query.id).then((res) => {
      res.data.paymentType = res.data.paymentType?.split(',') || [];
      res.data.serviceAreaCode = res.data.serviceAreaCode?.split(',') || [];
      res.data.productTag = JSON.parse(res.data.productTag || '[]');
      formData.value = res.data;
    });
  }
}

async function submit() {
  let saveData = await formRef.value.getFormData();
  let formData = {
    id: route.query.id,
    ...saveData,
    paymentType: saveData.paymentType.join(',') || [],
    serviceAreaCode: saveData.serviceAreaCode.join(',') || [],
    serviceArea:saveData.serviceAreaCode.map(areaCode=>dictConfig.getListAvailable.find((item) => item.id == areaCode).label ).join('、') || '-',
    productTag: JSON.stringify(saveData.productTag),
  };
  submitLoading.value = true;
  save(formData, route.query.id)
      .then(() => {
        ElMessage.success('保存成功');
      })
      .then(() => {
        router.go(-1);
      })
      .finally(() => {
        submitLoading.value = false;
      });
}
</script>

<style lang="scss">
.co-single-form {
  .el-form {
    display: flex;
    flex-wrap: wrap;
  }

  .el-form-item {
    flex: 0 0 50%;
  }

  .co-form-block {
    flex: 0 0 100%;
  }
}
</style>
