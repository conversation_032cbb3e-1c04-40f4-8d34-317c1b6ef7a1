import {yesOrNoEnum} from '@dict';

export const formListFn = function () {
    const isUcenter = location.pathname.includes(import.meta.env.VITE_ROUTE_PATH);
    return [
        {id: 'productName', name: '产品名称', max: 15},
        {type: 'text'},
        {
            id: 'financeOrganizationId',
            name: '发布机构',
            dicKey: 'getOrganizationManagementList',
            filterable: true,
            disabled: isUcenter,
        },
        {id: 'categoryId', name: '产品分类', type: 'select', dicKey: 'getGreenFinanceCategoryList', filterable: true},
        {
            id: 'guarantyMethod',
            name: '担保方式',
            type: 'select',
            dicKey: 'getGFGuaranteeMethodEnum',
            filterable: true,
            isString: true,
        },
        {id: 'productType', name: '产品类型', dicKey: 'getGFProductTypeEnum', filterable: true, isString: true},
        {id: 'organizationContactName', name: '机构联系人姓名', max: 100, validate: 'chinese'},
        {id: 'organizationContactPhone', name: '机构联系电话', validate: 'phone',},
        {
            name: '贷款利率（%）',
            children: [
                {
                    id: 'loanRateMin',
                    name: '最小限额',
                    validate: ({data}, value, callback) => {
                        // 检查是否为数字
                        if (value && isNaN(Number(value))) {
                            callback(new Error('请输入有效的数字'));
                        }

                        // 检查不能为负数
                        if (value && Number(value) < 0) {
                            callback(new Error('不能为负数'));
                        }

                        // 检查小数位数
                        if (value && value.toString().includes('.') && value.toString().split('.')[1].length > 2) {
                            callback(new Error('最多保留两位小数'));
                        }

                        // 检查最小限额不能超过最大限额
                        if (value && data.loanRateMax && Number(value) > Number(data.loanRateMax)) {
                            callback(new Error('最小限额不能超过最大限额'));
                        }
                        callback();
                    },
                },
                {
                    id: 'loanRateMax',
                    name: '最大限额',
                    validate: ({data}, value, callback) => {
                        // 检查是否为数字
                        if (value && isNaN(Number(value))) {
                            callback(new Error('请输入有效的数字'));
                        }

                        // 检查不能为负数
                        if (value && Number(value) < 0) {
                            callback(new Error('不能为负数'));
                        }

                        // 检查小数位数
                        if (value && value.toString().includes('.') && value.toString().split('.')[1].length > 2) {
                            callback(new Error('最多保留两位小数'));
                        }

                        // 检查最大限额不能低于最小限额
                        if (value && data.loanRateMin && Number(value) < Number(data.loanRateMin)) {
                            callback(new Error('最大限额不能低于最小限额'));
                        }

                        callback();
                    },
                },
            ],
        },
        {
            name: '贷款额度（万元）',
            children: [
                {
                    id: 'loanQuotaMin',
                    name: '最小额度',
                    validate: ({data}, value, callback) => {
                        // 检查是否为数字
                        if (value && isNaN(Number(value))) {
                            callback(new Error('请输入有效的数字'));
                        }

                        // 检查不能为负数
                        if (value && Number(value) < 0) {
                            callback(new Error('不能为负数'));
                        }

                        // 检查小数位数
                        if (value && value.toString().includes('.') && value.toString().split('.')[1].length > 2) {
                            callback(new Error('最多保留两位小数'));
                        }

                        // 检查最小额度不能超过最大额度
                        if (value && data.loanQuotaMax && Number(value) > Number(data.loanQuotaMax)) {
                            callback(new Error('最小额度不能超过最大额度'));
                        }
                        callback();
                    },
                },
                {
                    id: 'loanQuotaMax',
                    name: '最大额度',
                    validate: ({data}, value, callback) => {
                        // 检查是否为数字
                        if (value && isNaN(Number(value))) {
                            callback(new Error('请输入有效的数字'));
                        }

                        // 检查不能为负数
                        if (value && Number(value) < 0) {
                            callback(new Error('不能为负数'));
                        }

                        // 检查小数位数
                        if (value && value.toString().includes('.') && value.toString().split('.')[1].length > 2) {
                            callback(new Error('最多保留两位小数'));
                        }

                        // 检查最大额度不能低于最小额度
                        if (value && data.loanQuotaMin && Number(value) < Number(data.loanQuotaMin)) {
                            callback(new Error('最大额度不能低于最小额度'));
                        }

                        callback();
                    },
                },
            ],
        },
        {
            name: '贷款期限（个月）',
            children: [
                {
                    id: 'loanTimeMin',
                    name: '最小期限',
                    validate: ({data}, value, callback) => {
                        // 检查是否为数字
                        if (value && isNaN(Number(value))) {
                            callback(new Error('请输入有效的数字'));
                        }

                        // 检查不能为负数
                        if (value && Number(value) < 0) {
                            callback(new Error('不能为负数'));
                        }

                        // 检查是否为整数
                        if (value && !Number.isInteger(Number(value))) {
                            callback(new Error('请输入整数'));
                        }

                        // 检查最小期限不能超过最大期限
                        if (value && data.loanTimeMax && Number(value) > Number(data.loanTimeMax)) {
                            callback(new Error('最小期限不能超过最大期限'));
                        }
                        callback();
                    },
                },
                {
                    id: 'loanTimeMax',
                    name: '最大期限',
                    validate: ({data}, value, callback) => {
                        // 检查是否为数字
                        if (value && isNaN(Number(value))) {
                            callback(new Error('请输入有效的数字'));
                        }

                        // 检查不能为负数
                        if (value && Number(value) < 0) {
                            callback(new Error('不能为负数'));
                        }

                        // 检查是否为整数
                        if (value && !Number.isInteger(Number(value))) {
                            callback(new Error('请输入整数'));
                        }

                        // 检查最大期限不能低于最小期限
                        if (value && data.loanTimeMin && Number(value) < Number(data.loanTimeMin)) {
                            callback(new Error('最大期限不能低于最小期限'));
                        }

                        callback();
                    },
                },
            ],
        },
        {
            id: 'ifPolicyProduct',
            name: '是否政策性产品',
            type: 'radio',
            list: yesOrNoEnum.data,
        },
        {
            id: 'approvalMode',
            name: '审批模式',
            dicKey: 'getGFApprovalModeEnum',
            isString: true,
            filterable: true,
        },
        {
            id: 'paymentType',
            name: '还款方式',
            type: 'select',
            dicKey: 'getGFPaymentTypeEnum',
            filterable: true,
            isString: true,
            multiple: true,
        },
        {
            id: 'serviceAreaCode',
            name: '服务区域',
            multiple: true,
            dicKey: 'getListAvailable'
        },
        {
            id: 'serviceArea',
            name: '服务区域',
            slot: 'serviceArea',
            setRelation: true,
            relation: {id: 'serviceAreaCode', val: false},
        },
        {type: 'text'},
        {id: 'tag', name: '标签介绍', slot: 'tag', placeholder: '请添加标签介绍，至少添加1个，最多添加5个，每个10个字符'},
        {type: 'text'},
        {id: 'introduction', name: '产品介绍', type: 'textarea', max: 500},
        {type: 'text'},
        {id: 'basicCondition', name: '基本条件', type: 'textarea', required: false, max: 500},
        {type: 'text'},
        {id: 'productFeature', name: '产品特点', type: 'textarea', required: false, max: 500},
        {type: 'text'},
        {id: 'productTag', name: '产品标签', type: 'radio', slot: 'productTag', required: false},
    ];
};
