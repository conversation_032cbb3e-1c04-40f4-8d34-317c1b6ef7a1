<template>
  <el-dialog v-model="dialogVisible" title="产品标签-新增" width="600">
    <div class="w-100 add-table-form">
      <co-form ref="formRef" :form-list="formList" :form-data="formData" inline :dictConfig="dictConfig"
               @event="onFormEvent"></co-form>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {ref} from 'vue';
import {formListFn} from './data.js';

const emit = defineEmits(['update:visible', 'event']);
const props = defineProps(['visible']);

const formRef = ref();
const formList = ref([]);
const formData = ref({});
let dictConfig = ref({});
import {getTagLibraryList, getTagLibraryListByTagName} from '../../../../../api';

formList.value = formListFn();
formData.value = {};

function onFormEvent(val, row) {
  if (val.key == 'tagName' && !row) {
    formRef.value.setFormDataKey('category', [], true)
    dictConfig.value.category = []
  }
  if (val.key == 'tagName') {
    if (dictConfig.value.tagName?.length) {
      let {selectType} = dictConfig.value.tagName.find(item => item.id == row)
      formList.value[1].multiple = selectType ? true : false
    }
    getTagLibraryList({tagName: row}).then(res => {
      dictConfig.value = {...dictConfig.value, category: res.data}
    })
  }
}
getTagLibraryListByTagName().then(res => {
  dictConfig.value = {tagName: res.data}
})
let dialogVisible = computed({
  set(n) {
    emit('update:visible', n);
  },
  get() {
    return props.visible;
  },
});

async function submit() {
  let saveData = await formRef.value.getFormData();
  emit('event', {...saveData})
  dialogVisible.value = false;
}
</script>

<style lang="scss">
.add-table-form {
  .el-form {
    display: flex;
    flex-wrap: wrap;
  }

  .el-form-item {
    flex: 0 0 100% !important;
    margin: 0 0 20px 0;
  }

  .co-form-block {
    flex: 0 0 100%;
  }
}
</style>
