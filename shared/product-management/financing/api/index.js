import {getDictionary} from '/@/api/common/dictionary';
import {GFApprovalModeEnum, GFPaymentTypeEnum, GFProductTypeEnum, GFGuaranteeMethodEnum} from '@dict';

const isUcenter = location.pathname.includes(import.meta.env.VITE_ROUTE_PATH);

// 列表
export function getList(params) {
    return window.request({
        url: isUcenter ? '/etbtrade/finance-product/page' : '/etbtrade/finance-product/platform-page',
        method: 'GET',
        params,
    });
}

// 导出
export const exportData = '/etbtrade/finance-product/export';

// 调整热门
export function updateHot(data) {
    return window.request({
        url: '/etbtrade/finance-product/platform-set-hot',
        method: 'post',
        data,
    });
}

// 查看详情
export function getDetail(id) {
    return window.request({
        url: `/etbtrade/finance-product/detail/${id}`,
        method: 'GET',
    });
}

// 调整排序
export function updateSort(data) {
    return window.request({
        url: '/etbtrade/finance-product/platform-set-sort',
        method: 'post',
        data,
    });
}

//  切换上下架
export function updateStatus(data) {
    return window.request({
        url: '/etbtrade/finance-product/batch-update-status',
        method: 'post',
        data,
    });
}

//  保存
export function save(data, row) {
    return window.request({
        url: row ? '/etbtrade/finance-product/update' : '/etbtrade/finance-product/save',
        method: 'post',
        data,
    });
}

//  审核
export function audit(data) {
    return window.request({
        url: '/etbtrade/finance-product/platform-audit',
        method: 'post',
        data,
    });
}

// 删除数据
export function deleteById(id) {
    return window.request({
        url: `/etbtrade/finance-product/delete`,
        method: 'delete',
        data: [id]
    });
}

// 机构管理列表
export function getOrganizationManagementList(type) {
    return window
        .request({
            url: `/etbtrade/organizationManagement/list`,
            method: 'GET',
        })
        .then((res) => {
            let data = res.data.map((item) => {
                return {
                    id: item.id,
                    label: item.orgName,
                    uniscId: item.uniscId,
                    value: item.id
                };
            })
            return type == 'clear' ? data : {
                data
            };
        });
}

// 标签库分类
export function getTagLibraryList(params) {
    return window
        .request({
            url: `/etbtrade/tagLibrary/list`,
            method: 'GET',
            params,
        })
        .then((res) => {
            if (res.data.length == 0 || res.data[0].selectionList.length == 0) return {data: []};
            return {
                data: res.data[0].selectionList.map((item) => {
                    return {
                        id: item,
                        label: item,
                    };
                }),
            };
        });
}

// 标签名称
export function getTagLibraryListByTagName(params) {
    return window
        .request({
            url: `/etbtrade/tagLibrary/list`,
            method: 'GET',
            params,
        })
        .then((res) => {
            if (res.data.length == 0) return {data: []};
            return {
                data: res.data.map((item) => {
                    return {
                        selectType:item.selectType,
                        id: item.tagName,
                        label: item.tagName,
                    };
                }),
            };
        });
}

// 查询可用站点list
export function getListAvailable(params) {
    return window
        .request({
            url: `/etbtrade/service-area/list-available`,
            method: 'GET',
            params,
        })
        .then((res) => {
            if (res.data.length == 0) return {data: []};
            return {
                data: res.data.map((item) => {
                    return {
                        id: item.code,
                        label: item.name,
                    };
                }),
            };
        });
}

// 审批模式
export function getGFApprovalModeEnum() {
    return getDictionary(GFApprovalModeEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 产品类型
export function getGFProductTypeEnum() {
    return getDictionary(GFProductTypeEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 还款方式
export function getGFPaymentTypeEnum() {
    return getDictionary(GFPaymentTypeEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 担保方式
export function getGFGuaranteeMethodEnum() {
    return getDictionary(GFGuaranteeMethodEnum).then((res) => {
        return {
            data: res.dicList.map((item) => {
                return {
                    id: item.dicValue,
                    label: item.dicName,
                };
            }),
        };
    });
}

// 获取推荐企业列表
export function getRecommendEnterprises(params) {
    return window.request({
        url: `/etbtrade/enterprise-product-match/page`,
        method: 'get',
        params,
    });
}
