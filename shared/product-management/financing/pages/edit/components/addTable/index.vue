<script setup>
import addDialog from './dialog';

// 定义 Props
const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
  disabled: {
		type: Boolean,
		default: false,
	},
});

// 定义 Emits
const emit = defineEmits(['update:data']);
const pageConfig = reactive({
	previewUrl: '',
	previewTitle: '',
	tableConfig: {
		pagination: false,
	},
	tableData: [...props.data], // 初始化为外部传入的数据
	tableHeader: [
		{ type: 'index', label: '序号', width: '80' },
		{ prop: 'tagName', label: '标签名称' },
		{ prop: 'category', label: '选择项' },
		{ prop: 'description', label: '描述' },
		{ prop: 'operation', label: '操作',hidden: props.disabled },
	],
});
// 监听外部数据变化，同步到内部
watch(
	() => props.data,
	(newData) => {
		Object.assign(pageConfig.tableData,newData)
	},
	{ deep: true }
);

// 监听内部数据变化，同步到外部
watch(
	() => pageConfig.tableData,
	(newData) => {
		emit('update:data', [...newData]);
	},
	{ deep: true }
);
let visible = ref(false);

// 删除数据
function removeHandler(row) {
  // row.$index 不准确，使用uuid删除
  let index = pageConfig.tableData.findIndex(item=>item._uuid==row._uuid)
	pageConfig.tableData.splice(index, 1);
	// 数据变化会通过 watch 自动同步到外部
}

// 添加数据
function addData(newData) {
	pageConfig.tableData.push(newData);
	// 数据变化会通过 watch 自动同步到外部
}
</script>

<template>
	<div>
		<addDialog v-model:visible="visible" v-if="visible" @event="addData($event)" />
		<co-table :data="pageConfig.tableData" :border="false" :config="pageConfig.tableConfig" :header="pageConfig.tableHeader" align="left">
			<template #category="{ row }">
				<el-tag v-for="(item, index) in row.category" :key="index" class="mr-1">{{ item }}</el-tag>
			</template>
			<template #operation="row" >
				<el-button icon="delete" link type="danger"  @click="removeHandler(row)">删除</el-button>
			</template>
		</co-table>
		<div class="flex justify-center mt-2 text-3xl" v-if="!disabled">
			<el-button type="primary" text @click="visible = true">
				<el-icon color="#165dff">
					<Plus />
				</el-icon>
				新增
			</el-button>
		</div>
	</div>
</template>

<style scoped lang="scss">
.zs-table-page {
	width: 70vw;
}
</style>
