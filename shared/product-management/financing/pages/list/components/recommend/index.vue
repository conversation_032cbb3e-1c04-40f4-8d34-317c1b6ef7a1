<template>
  <el-dialog v-model="dialogVisible" title="查看推荐企业" width="35%" top="5vh">
    <div class="recommend-enterprise-container" v-loading="loading">
      <!-- 企业列表 -->
      <div class="enterprise-grid" v-if="enterpriseList.length > 0">
        <div
            v-for="enterprise in enterpriseList"
            :key="enterprise.id"
            class="enterprise-card"
        >
          <!-- 匹配度标签 -->
          <div class="match-rate-tag">
            匹配度: {{ enterprise.matchingDegree }}%
          </div>

          <!-- 企业信息 -->
          <div class="enterprise-info">
            <h3 class="company-name">{{ enterprise.orgName }}</h3>
            <p class="company-address">{{ enterprise.orgAddress }}</p>
            <!-- 显示地区信息 -->
            <p v-if="enterprise.orgRegionName" class="company-region">
              <el-icon class="region-icon"><Location /></el-icon>
              <span>{{ enterprise.orgRegionName }}</span>
            </p>

            <!-- 状态信息 -->
            <div class="status-info">
              <el-icon class="status-icon" color="#67C23A">
                <Check/>
              </el-icon>
              <span class="status-text">企业数据符合申请条件</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多指示器 -->
      <div
        v-if="enterpriseList.length > 0"
        :ref="setLoadMoreRef"
        class="load-more-indicator"
      >
        <div v-if="loadingMore" class="loading-more flex items-center">
          <el-icon class="is-loading mr-2">
            <Loading />
          </el-icon>
          <span>加载中...</span>
        </div>
        <div v-else-if="!pagination.hasMore" class="no-more-data">
          <span>已加载全部数据</span>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error && !loading" class="error-state">
        <el-empty description="加载失败">
          <el-button type="primary" @click="retryLoad">重试</el-button>
        </el-empty>
      </div>

      <!-- 空数据状态 -->
      <div v-else-if="!loading && enterpriseList.length === 0" class="empty-state">
        <el-empty description="暂无匹配企业数据"/>
      </div>
    </div>

    <template #footer>
      <div>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getRecommendEnterprises } from '../../../../api';
import { getDictionary } from "/@/api/common/dictionary";
import { useScrollPaging } from '../../../../../utils/useScrollPaging';
import { Loading, Check, Location } from '@element-plus/icons-vue';
import { ref, computed, watch, onUnmounted } from 'vue';

const emit = defineEmits(['update:visible', 'refresh']);
const props = defineProps(['visible', 'row']);
import { dictApi } from '/@/api/dict';

// 对话框显示状态
const dialogVisible = computed({
  set(n) {
    emit('update:visible', n);
  },
  get() {
    return props.visible;
  },
});

// 使用滚动分页 hook
const {
  dataList: enterpriseList,
  loading,
  loadingMore,
  pagination,
  error,
  refresh,
  setLoadMoreRef,
  destroyScrollObserver
} = useScrollPaging({
  fetchFun: getRecommendEnterprises,
  params: computed(() => ({ productId: props.row?.id })),
  size: 10, // 每页10条数据
  autoLoad: true, // 不自动加载，等对话框打开时再加载
  enableRegionCodeLookup: true, // 启用地区代码反查
  regionCodeField: 'orgRegionCode', // 地区代码字段名
  regionNameField: 'orgRegionName' // 地区名称字段名
});

// 监听对话框显示状态，打开时加载数据
watch(dialogVisible, (newVal) => {
  if (newVal && props.row?.id) {
    refresh();
  }
});

// 组件卸载时清理监听器
onUnmounted(() => {
  destroyScrollObserver();
});

// 重试加载数据
const retryLoad = () => {
  refresh();
};

// 移除测试代码，地区代码反查功能已集成到useScrollPaging hook中

// 确定按钮处理
const handleConfirm = () => {
  dialogVisible.value = false;
  emit('refresh');
};
</script>

<style lang="scss" scoped>
.recommend-enterprise-container {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
}

.enterprise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.enterprise-card {
  position: relative;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.match-rate-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.enterprise-info {
  padding-top: 10px;
}

.company-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.company-address {
  font-size: 14px;
  color: #606266;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.company-region {
  font-size: 13px;
  color: #909399;
  margin: 0 0 16px 0;
  line-height: 1.4;
  display: flex;
  align-items: center;
}

.region-icon {
  margin-right: 4px;
  font-size: 14px;
  color: #409eff;
}

.status-info {
  display: flex;
  align-items: center;
  background: #f0f9ff;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #67c23a;
}

.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

.status-text {
  font-size: 13px;
  color: #67c23a;
  font-weight: 500;
}

/* 加载更多指示器样式 */
.load-more-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  margin-top: 20px;
}

.loading-more {
  color: #409eff;
  font-size: 14px;
}

.no-more-data {
  color: #909399;
  font-size: 14px;
  text-align: center;
}

/* 错误状态样式 */
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 空数据状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enterprise-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px 0;
  }

  .enterprise-card {
    padding: 16px;
  }

  .company-name {
    font-size: 16px;
  }
}
</style>
