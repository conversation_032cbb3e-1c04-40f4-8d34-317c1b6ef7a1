import {getList, getOrganizationManagementList} from '../../api';
import {GFGuaranteeMethodEnum, GFAuditStatusEnum, yesOrNoEnum, onlineEnum, GFProductTypeEnum} from '@dict';

const isUcenter = location.pathname.includes(import.meta.env.VITE_ROUTE_PATH);

// 搜索配置
export const searchConfig = {
    items: [
        {
            prop: 'productName',
            type: 'input',
            attrs: {
                placeholder: '请输入产品名称',
                clearable: true,
            },
        },
        {
            prop: 'financeOrganizationId',
            type: 'select',
            option: 'financeOrganizationId',
            attrs: {
                placeholder: '请选择发布机构',
                clearable: true,
            },
            hidden: isUcenter
        },
        {
            prop: 'times',
            type: 'daterange',
            attrs: {
                clearable: true,
                startPlaceholder: '发布开始时间',
                endPlaceholder: '发布结束时间',
                rangeSeparator: '-',
                'value-format': 'YYYY-MM-DD HH:mm:ss',
            },
            splitProp: ['publishTimeStart', 'publishTimeEnd'],
        },
        {
            prop: 'status',
            type: 'select',
            option: 'status',
            attrs: {
                placeholder: '请选择上架状态',
                clearable: true,
            },
        },
        {
            prop: 'guarantyMethod',
            type: 'select',
            option: 'guarantyMethod',
            attrs: {
                placeholder: '请选择担保方式',
                clearable: true,
            },
        },
        {
            prop: 'ifHot',
            type: 'select',
            option: 'ifHot',
            attrs: {
                placeholder: '请选择热门产品',
                clearable: true,
            },
        },
        {
            prop: 'ifIndustryClusterProduct',
            type: 'select',
            option: 'ifHot',
            attrs: {
                placeholder: '请选择是否产业集群',
                clearable: true,
            },
        },
        {
            prop: 'auditStatus',
            type: 'select',
            option: 'auditStatus',
            attrs: {
                placeholder: '请选择审核状态',
                clearable: true,
            },
        },
    ],
};
// table表格
export const tableHeader = () => {
    return [
        {type: 'selection', label: '', width: 55, fixed: true},
        {type: 'index', label: '序号', width: 55},
        {prop: 'financeOrganizationId', label: '发布机构', width: 200, hidden: isUcenter},
        {prop: 'productName', label: '产品名称', width: 220, showOverflowTooltip: true},
        {prop: 'loanRateMax', label: '贷款利率', width: 200, showOverflowTooltip: true},
        {prop: 'loanQuotaMax', label: '贷款额度', width: 170},
        {prop: 'loanTimeMax', label: '贷款期限', width: 140},
        {prop: 'guarantyMethod', label: '担保方式', width: 170},
        {prop: 'tag', label: '标签', width: 170},
        {prop: 'insertTime', label: '发布时间', width: 200},
        {prop: 'ifHot', label: '是否热门产品', width: 150},
        {prop: 'status', label: '上架状态', width: 100},
        {prop: 'auditStatus', label: '审核状态', width: 100},
        {prop: 'sortNum', label: '排序', width: 100, hidden: isUcenter},
        {prop: 'insertPersonName', label: '创建人', width: 150, hidden: isUcenter},
    ];
};

export const tableConfig = {
    request: {
        apiName: getList,
    },
    operation: {
        width: 200,
        fixed: 'right',
    },
    dic: {
        status: onlineEnum,
        ifHot: yesOrNoEnum,
        guarantyMethod: GFGuaranteeMethodEnum,
        auditStatus: GFAuditStatusEnum,
        GFProductTypeEnum: GFProductTypeEnum,
        financeOrganizationId: getOrganizationManagementList.bind(null, 'clear')
    },
};
