import { getDictionary } from "/@/api/common/dictionary";
import { dictApi } from '/@/api/dict';

// 地区字典数据缓存
let regionDictCache = null;
let regionDictPromise = null;

/**
 * 获取地区字典数据
 * @param {String} dicId 字典ID，默认使用 registerAddres
 * @returns {Promise<Array>} 地区字典数据
 */
async function getRegionDict(dicId = '621976305115137') {
  // 如果已有缓存，直接返回
  if (regionDictCache) {
    return regionDictCache;
  }

  // 如果正在请求中，返回同一个Promise
  if (regionDictPromise) {
    return regionDictPromise;
  }

  // 创建请求Promise
  regionDictPromise = getDictionary(dicId || dictApi.registerAddres)
    .then(res => {
      // 处理字典数据
      if (res && Array.isArray(res)) {
        // 如果直接返回数组格式（树形结构）
        regionDictCache = res;
        return regionDictCache;
      } else if (res && res.dicList) {
        // 如果返回包含dicList的对象
        regionDictCache = res.dicList;
        return regionDictCache;
      } else {
        throw new Error('字典数据格式不正确');
      }
    })
    .catch(err => {
      console.error('获取地区字典失败:', err);
      regionDictPromise = null; // 重置Promise，允许重试
      throw err;
    });

  return regionDictPromise;
}

/**
 * 在字典数据中递归查找指定编码的地区
 * @param {Array} dictData 字典数据
 * @param {String} code 要查找的地区编码
 * @param {Number} targetGrade 目标层级 (2=省, 3=市, 4=区)
 * @returns {Object|null} 找到的地区对象，包含dicName和dicValue
 */
function findRegionByCode(dictData, code, targetGrade = null) {
  if (!Array.isArray(dictData) || !code) {
    return null;
  }

  for (const item of dictData) {
    // 检查当前项是否匹配
    if (item.dicValue === code) {
      // 如果指定了目标层级，检查层级是否匹配
      if (targetGrade === null || item.grade === targetGrade) {
        return {
          dicName: item.dicName,
          dicValue: item.dicValue,
          grade: item.grade
        };
      }
    }

    // 递归查找子节点
    if (item.children && Array.isArray(item.children)) {
      const found = findRegionByCode(item.children, code, targetGrade);
      if (found) {
        return found;
      }
    }
  }

  return null;
}

/**
 * 将orgRegionCode转换为地区名称
 * @param {String} orgRegionCode 地区编码，格式如 "13,1301,130102"
 * @param {String} separator 分隔符，默认为空字符串（直接拼接）
 * @param {String} dicId 字典ID，默认使用 registerAddres
 * @returns {Promise<String>} 地区名称，如 "河北省石家庄市长安区"
 */
export async function getRegionNameByCode(orgRegionCode, separator = '', dicId = '621976305115137') {
  if (!orgRegionCode || typeof orgRegionCode !== 'string') {
    return '';
  }

  try {
    // 获取字典数据
    const dictData = await getRegionDict(dicId);
    
    // 分割地区编码
    const codes = orgRegionCode.split(',').map(code => code.trim()).filter(code => code);
    
    if (codes.length === 0) {
      return '';
    }

    const regionNames = [];

    // 按层级查找地区名称
    for (let i = 0; i < codes.length; i++) {
      const code = codes[i];
      const targetGrade = i + 2; // grade从2开始（2=省, 3=市, 4=区）
      
      const region = findRegionByCode(dictData, code, targetGrade);
      if (region && region.dicName) {
        regionNames.push(region.dicName);
      } else {
        // 如果找不到对应的地区名称，使用原始编码
        console.warn(`未找到编码 ${code} 对应的地区名称`);
        regionNames.push(code);
      }
    }

    // 拼接地区名称
    return regionNames.join(separator);
  } catch (error) {
    console.error('地区代码反查失败:', error);
    return orgRegionCode; // 失败时返回原始编码
  }
}

/**
 * 批量处理企业数据的地区代码反查
 * @param {Array} enterprises 企业数据列表
 * @param {String} codeField 地区代码字段名，默认为 'orgRegionCode'
 * @param {String} nameField 地区名称字段名，默认为 'orgRegionName'
 * @param {String} separator 分隔符，默认为空字符串
 * @param {String} dicId 字典ID，默认使用 registerAddres
 * @returns {Promise<Array>} 处理后的企业数据列表
 */
export async function processEnterpriseRegionCodes(
  enterprises, 
  codeField = 'orgRegionCode', 
  nameField = 'orgRegionName',
  separator = '',
  dicId = '621976305115137'
) {
  if (!Array.isArray(enterprises) || enterprises.length === 0) {
    return enterprises;
  }

  try {
    // 预加载地区字典数据
    await getRegionDict(dicId);

    // 并行处理所有企业的地区代码
    const processedEnterprises = await Promise.all(
      enterprises.map(async (enterprise) => {
        const regionCode = enterprise[codeField];
        if (regionCode) {
          try {
            const regionName = await getRegionNameByCode(regionCode, separator, dicId);
            return {
              ...enterprise,
              [nameField]: regionName
            };
          } catch (error) {
            console.error(`处理企业 ${enterprise.id || enterprise.orgName} 的地区代码失败:`, error);
            return {
              ...enterprise,
              [nameField]: regionCode // 失败时使用原始代码
            };
          }
        } else {
          return {
            ...enterprise,
            [nameField]: ''
          };
        }
      })
    );

    return processedEnterprises;
  } catch (error) {
    console.error('批量处理地区代码失败:', error);
    // 如果批量处理失败，返回原始数据
    return enterprises;
  }
}

/**
 * 清除地区字典缓存（用于测试或强制刷新）
 */
export function clearRegionDictCache() {
  regionDictCache = null;
  regionDictPromise = null;
}
