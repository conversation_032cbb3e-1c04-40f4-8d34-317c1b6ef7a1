import { ref, reactive, nextTick, unref } from 'vue'
import { processEnterpriseRegionCodes } from './regionCodeUtils'

/**
 * 滚动加载分页 Hook
 * @param {Object} options 配置选项
 * @param {Function} options.fetchFun 数据获取函数
 * @param {Object} options.params 请求参数
 * @param {Number} options.size 每页数据量，默认10
 * @param {Boolean} options.autoLoad 是否自动加载第一页，默认true
 * @param {Boolean} options.enableRegionCodeLookup 是否启用地区代码反查，默认false
 * @param {String} options.regionCodeField 地区代码字段名，默认'orgRegionCode'
 * @param {String} options.regionNameField 地区名称字段名，默认'orgRegionName'
 */
export function useScrollPaging(options = {}) {
  const {
    fetchFun,
    params = {},
    size = 10,
    autoLoad = true,
    enableRegionCodeLookup = false,
    regionCodeField = 'orgRegionCode',
    regionNameField = 'orgRegionName'
  } = options

  // 分页状态
  const pagination = reactive({
    current: 1,
    size,
    total: 0,
    hasMore: true
  })

  // 加载状态
  const loading = ref(false)
  const loadingMore = ref(false)

  // 数据列表
  const dataList = ref([])

  // 错误状态
  const error = ref(null)

  // 滚动监听元素引用
  const loadMoreRef = ref(null)

  // IntersectionObserver 实例
  let observer = null

  /**
   * 获取数据
   * @param {Boolean} isLoadMore 是否为加载更多
   */
  const fetchData = async (isLoadMore = false) => {
    if (!fetchFun) {
      console.error('fetchFun is required')
      return
    }

    // 如果是加载更多但没有更多数据，直接返回
    if (isLoadMore && !pagination.hasMore) {
      return
    }

    try {
      // 设置加载状态
      if (isLoadMore) {
        loadingMore.value = true
      } else {
        loading.value = true
        // 重置数据
        dataList.value = []
        pagination.current = 1
        pagination.hasMore = true
      }

      error.value = null

      // 构建请求参数
      const currentParams = unref(params)
      const requestParams = {
        ...currentParams,
        current: pagination.current,
        size: pagination.size
      }
      // 调用数据获取函数
      const response = await fetchFun(requestParams)

      // 处理响应数据
      const { data } = response
      let newList = data.list || data.records || []
      const total = data.total || 0

      // 如果启用了地区代码反查功能，处理企业数据
      if (enableRegionCodeLookup && newList.length > 0) {
        try {
          newList = await processEnterpriseRegionCodes(
            newList,
            regionCodeField,
            regionNameField
          )
        } catch (error) {
          console.error('地区代码反查处理失败:', error)
          // 失败时继续使用原始数据，不影响主流程
        }
      }

      // 更新分页信息
      pagination.total = total

      // 判断是否还有更多数据
      pagination.hasMore = pagination.current * pagination.size < total

      // 更新数据列表
      if (isLoadMore) {
        // 追加数据
        dataList.value = [...dataList.value, ...newList]
      } else {
        // 替换数据
        dataList.value = newList
      }
    } catch (err) {
      console.error('数据加载失败:', err)
      error.value = err
    } finally {
      loading.value = false
      loadingMore.value = false
    }
  }

  /**
   * 加载更多数据
   */
  const loadMore = () => {
    if (loadingMore.value || !pagination.hasMore) {
      return
    }

    pagination.current++
    fetchData(true)
  }

  /**
   * 刷新数据（重新加载第一页）
   */
  const refresh = () => {
    fetchData(false)
  }

  /**
   * 重置分页状态
   */
  const reset = () => {
    pagination.current = 1
    pagination.total = 0
    pagination.hasMore = true
    dataList.value = []
    error.value = null
  }

  /**
   * 初始化滚动监听
   */
  const initScrollObserver = () => {
    if (!loadMoreRef.value) {
      return
    }

    // 创建 IntersectionObserver
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          // 当监听元素进入视口时，触发加载更多
          if (entry.isIntersecting && pagination.hasMore && !loadingMore.value) {
            loadMore()
          }
        })
      },
      {
        // 提前50px触发
        rootMargin: '50px',
        threshold: 0.1
      }
    )

    observer.observe(loadMoreRef.value)
  }

  /**
   * 销毁滚动监听
   */
  const destroyScrollObserver = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  /**
   * 设置监听元素
   */
  const setLoadMoreRef = (el) => {
    loadMoreRef.value = el
    if (el) {
      nextTick(() => {
        initScrollObserver()
      })
    }
  }

  // 自动加载第一页
  if (autoLoad) {
    nextTick(() => {
      fetchData(false)
    })
  }

  return {
    // 数据
    dataList,
    pagination,

    // 状态
    loading,
    loadingMore,
    error,

    // 方法
    fetchData,
    loadMore,
    refresh,
    reset,
    setLoadMoreRef,
    destroyScrollObserver
  }
}
